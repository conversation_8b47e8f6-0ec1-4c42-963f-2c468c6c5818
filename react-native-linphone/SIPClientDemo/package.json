{"name": "SIPClientDemo", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"react": "18.1.0", "react-native": "0.70.5", "react-native-linphone-voip": "../react-native-linphone-voip"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/runtime": "^7.12.5", "@react-native-community/eslint-config": "^2.0.0", "babel-jest": "^26.6.3", "eslint": "^7.32.0", "jest": "^26.6.3", "metro-react-native-babel-preset": "0.72.3", "react-test-renderer": "18.1.0"}, "jest": {"preset": "react-native"}}