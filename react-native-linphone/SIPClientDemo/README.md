# React Native SIP Client Demo

A complete SIP client application built with React Native and Linphone SDK, featuring voice calls, messaging, contact management, and call history.

## 🎯 Features

### ✅ Core SIP Functionality
- **SIP Registration** - Connect to SIP servers with username/password authentication
- **Voice Calls** - Make and receive high-quality voice calls
- **Call Management** - Answer, decline, hold, mute, speaker controls
- **DTMF Support** - Send DTMF tones during calls
- **Multiple Codecs** - Support for PCMU, PCMA, G722, G729, Opus
- **Transport Protocols** - UDP, TCP, TLS support

### 📱 User Interface
- **Login Screen** - Professional SIP account configuration
- **Dialer** - Touch-friendly dialpad with call history integration
- **Call Screen** - Full-featured call interface with controls
- **Call History** - Complete call log with duration and timestamps
- **Messages** - SIP messaging with conversation view
- **Contacts** - Contact management with quick call/message actions
- **Settings** - Audio settings, codec preferences, account management

### 🔧 Technical Features
- **Real-time Events** - Live call state updates and notifications
- **Audio Controls** - Echo cancellation, noise suppression
- **Background Support** - Handle calls when app is backgrounded
- **Error Handling** - Comprehensive error handling and user feedback
- **Responsive Design** - Optimized for all screen sizes

## 📁 Project Structure

```
SIPClientDemo/
├── src/
│   ├── components/
│   │   ├── MainTabNavigator.js    # Main app navigation
│   │   └── CallScreen.js          # In-call interface
│   ├── screens/
│   │   ├── LoginScreen.js         # SIP account login
│   │   ├── DialerScreen.js        # Phone dialer
│   │   ├── CallHistoryScreen.js   # Call history list
│   │   ├── MessagesScreen.js      # SIP messaging
│   │   ├── ContactsScreen.js      # Contact management
│   │   └── SettingsScreen.js      # App settings
│   ├── services/
│   │   └── SIPService.js          # Linphone SDK wrapper
│   └── SIPClientApp.js            # Main app component
├── ios/                           # iOS native code
├── android/                       # Android native code
└── node_modules/
    └── react-native-linphone-voip/ # Custom Linphone library
```

## 🚀 Installation & Setup

### Prerequisites
- Node.js 16+ and npm/yarn
- React Native CLI
- Xcode 14+ (for iOS)
- Android Studio (for Android)
- CocoaPods (for iOS dependencies)

### 1. Clone and Install Dependencies
```bash
cd SIPClientDemo
yarn install
```

### 2. iOS Setup
```bash
cd ios
bundle install
bundle exec pod install
cd ..
```

### 3. Android Setup
```bash
# Android dependencies are automatically handled
```

### 4. Run the Application
```bash
# iOS
npx react-native run-ios

# Android
npx react-native run-android
```

## 📖 Usage Guide

### 1. SIP Account Configuration
- Launch the app to see the login screen
- Enter your SIP credentials:
  - **Username**: Your SIP username
  - **Password**: Your SIP password
  - **Domain**: Your SIP server domain (e.g., sip.example.com)
  - **Proxy**: Optional proxy server (defaults to domain)
  - **Transport**: Choose UDP, TCP, or TLS
- Tap "Connect" to register

### 2. Making Calls
- Navigate to the **Dialer** tab
- Enter phone number using the dialpad
- Tap the green call button
- Use in-call controls for mute, speaker, DTMF, etc.

### 3. Receiving Calls
- Incoming calls automatically show the call screen
- Tap green button to answer
- Tap red button to decline

### 4. Messaging
- Go to **Messages** tab
- Tap compose button (✏️) to create new message
- Enter recipient SIP address and message
- View conversation history

### 5. Managing Contacts
- **Contacts** tab shows your contact list
- Tap + to add new contacts
- Quick actions: call (📞), message (💬), delete (🗑️)

### 6. Settings & Configuration
- **Settings** tab for app configuration
- Audio settings: echo cancellation, noise suppression
- Codec preferences and DTMF mode
- Account logout option

## 🔧 Technical Implementation

### SIP Service Architecture
The `SIPService` class wraps the Linphone SDK and provides:
- Event-driven architecture with listeners
- Promise-based async operations
- Automatic state management
- Error handling and recovery

### Key Components

#### SIPService.js
```javascript
// Core SIP functionality
await sipService.register(credentials);
await sipService.makeCall(phoneNumber);
await sipService.sendMessage(recipient, message);

// Event listeners
sipService.onIncomingCall(callback);
sipService.onCallStateChanged(callback);
sipService.onMessageReceived(callback);
```

#### LoginScreen.js
- SIP credential input form
- Transport protocol selection
- Connection state management
- Demo credentials helper

#### DialerScreen.js
- Touch-friendly dialpad
- Phone number input/editing
- Call initiation
- Integration with call screen

#### CallScreen.js
- Real-time call state display
- Audio controls (mute, speaker)
- DTMF tone sending
- Call duration timer

## 🎨 UI/UX Design

### Design Principles
- **iOS-style Interface** - Native iOS look and feel
- **Touch-Friendly** - Large buttons and easy navigation
- **Professional** - Clean, business-appropriate design
- **Accessible** - Clear labels and intuitive controls

### Color Scheme
- **Primary**: #007AFF (iOS Blue)
- **Success**: #4CAF50 (Green for calls)
- **Danger**: #f44336 (Red for hang up)
- **Background**: #f5f5f5 (Light gray)
- **Text**: #333 (Dark gray)

## 🔍 Testing

### Manual Testing Checklist
- [ ] SIP registration with valid credentials
- [ ] Outgoing call to valid SIP address
- [ ] Incoming call handling (answer/decline)
- [ ] In-call controls (mute, speaker, DTMF)
- [ ] Call history logging
- [ ] Message sending/receiving
- [ ] Contact management (add/edit/delete)
- [ ] Settings configuration
- [ ] App backgrounding during calls

### Test SIP Accounts
For testing, you can use:
- **Linphone.org** free SIP accounts
- **SIP2SIP.info** free service
- Local **Asterisk** or **FreeSWITCH** server

## 🐛 Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clean and rebuild
cd ios && rm -rf build && cd ..
npx react-native clean
npx react-native run-ios
```

#### SIP Registration Fails
- Check network connectivity
- Verify SIP server credentials
- Try different transport protocol (UDP/TCP/TLS)
- Check firewall/NAT settings

#### Audio Issues
- Enable microphone permissions
- Check device audio settings
- Try toggling speaker mode
- Verify codec compatibility

#### Call Quality Problems
- Check network bandwidth
- Try different audio codecs
- Enable echo cancellation
- Reduce background noise

## 📚 Dependencies

### Core Dependencies
- **React Native 0.70.5** - Mobile app framework
- **react-native-linphone-voip** - Custom Linphone SDK wrapper

### Development Dependencies
- **@babel/core** - JavaScript compiler
- **@react-native-community/eslint-config** - Code linting
- **jest** - Testing framework
- **prettier** - Code formatting

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Linphone SDK** - Open source SIP library
- **React Native Community** - Framework and tools
- **Belledonne Communications** - Linphone development

---

**Built with ❤️ using React Native and Linphone SDK**
