import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';

const CallHistoryScreen = ({sipService}) => {
  const [callHistory, setCallHistory] = useState([]);

  useEffect(() => {
    // Load initial call history
    setCallHistory(sipService.getCallHistory());

    // Listen for call history updates
    const unsubscribe = sipService.addEventListener('callHistoryUpdated', (history) => {
      setCallHistory(history);
    });

    return unsubscribe;
  }, [sipService]);

  const formatDate = (date) => {
    const now = new Date();
    const callDate = new Date(date);
    const diffTime = Math.abs(now - callDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays === 2) {
      return 'Yesterday';
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return callDate.toLocaleDateString();
    }
  };

  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (duration) => {
    if (!duration || duration === 0) return '';
    
    const seconds = Math.floor(duration / 1000);
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    
    if (mins > 0) {
      return `${mins}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const getCallIcon = (call) => {
    if (call.state === 'incoming') {
      return call.duration > 0 ? '📞' : '📵'; // Answered or missed
    } else {
      return '📲'; // Outgoing
    }
  };

  const getCallTypeText = (call) => {
    if (call.state === 'incoming') {
      return call.duration > 0 ? 'Incoming' : 'Missed';
    } else {
      return 'Outgoing';
    }
  };

  const handleCallBack = async (phoneNumber) => {
    try {
      await sipService.makeCall(phoneNumber);
    } catch (error) {
      Alert.alert('Call Failed', error.message || 'Unable to make call');
    }
  };

  const renderCallItem = ({item}) => (
    <TouchableOpacity
      style={styles.callItem}
      onPress={() => handleCallBack(item.remoteAddress)}>
      <View style={styles.callIcon}>
        <Text style={styles.callIconText}>{getCallIcon(item)}</Text>
      </View>
      
      <View style={styles.callDetails}>
        <Text style={styles.phoneNumber}>{item.remoteAddress}</Text>
        <View style={styles.callMeta}>
          <Text style={styles.callType}>{getCallTypeText(item)}</Text>
          {item.duration > 0 && (
            <>
              <Text style={styles.separator}>•</Text>
              <Text style={styles.duration}>{formatDuration(item.duration)}</Text>
            </>
          )}
        </View>
      </View>
      
      <View style={styles.callTime}>
        <Text style={styles.timeText}>{formatTime(item.startTime)}</Text>
        <Text style={styles.dateText}>{formatDate(item.startTime)}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>📞</Text>
      <Text style={styles.emptyStateTitle}>No Call History</Text>
      <Text style={styles.emptyStateText}>
        Your call history will appear here once you make or receive calls.
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Call History</Text>
        {callHistory.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => {
              Alert.alert(
                'Clear History',
                'Are you sure you want to clear all call history?',
                [
                  {text: 'Cancel', style: 'cancel'},
                  {
                    text: 'Clear',
                    style: 'destructive',
                    onPress: () => setCallHistory([]),
                  },
                ]
              );
            }}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        )}
      </View>

      <FlatList
        data={callHistory}
        renderItem={renderCallItem}
        keyExtractor={(item, index) => `${item.id || index}`}
        ListEmptyComponent={renderEmptyState}
        style={styles.list}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#f44336',
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  list: {
    flex: 1,
  },
  callItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  callIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  callIconText: {
    fontSize: 18,
  },
  callDetails: {
    flex: 1,
  },
  phoneNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  callMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  callType: {
    fontSize: 14,
    color: '#666',
  },
  separator: {
    fontSize: 14,
    color: '#666',
    marginHorizontal: 6,
  },
  duration: {
    fontSize: 14,
    color: '#666',
  },
  callTime: {
    alignItems: 'flex-end',
  },
  timeText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  dateText: {
    fontSize: 12,
    color: '#666',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateIcon: {
    fontSize: 60,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default CallHistoryScreen;
