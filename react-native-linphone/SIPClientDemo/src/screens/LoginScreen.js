import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';

const LoginScreen = ({onLogin, isConnecting}) => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
    domain: '',
    proxy: '',
    transport: 'UDP',
  });

  const handleInputChange = (field, value) => {
    setCredentials(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleLogin = () => {
    // Validate inputs
    if (!credentials.username.trim()) {
      Alert.alert('Error', 'Please enter username');
      return;
    }
    if (!credentials.password.trim()) {
      Alert.alert('Error', 'Please enter password');
      return;
    }
    if (!credentials.domain.trim()) {
      Alert.alert('Error', 'Please enter domain');
      return;
    }

    onLogin(credentials);
  };

  const fillDemoCredentials = () => {
    setCredentials({
      username: 'demo_user',
      password: 'demo_pass',
      domain: 'sip.example.com',
      proxy: 'sip.example.com',
      transport: 'UDP',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>SIP Client</Text>
            <Text style={styles.subtitle}>Enter your SIP credentials</Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Username</Text>
              <TextInput
                style={styles.input}
                value={credentials.username}
                onChangeText={(value) => handleInputChange('username', value)}
                placeholder="Enter username"
                autoCapitalize="none"
                autoCorrect={false}
                editable={!isConnecting}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Password</Text>
              <TextInput
                style={styles.input}
                value={credentials.password}
                onChangeText={(value) => handleInputChange('password', value)}
                placeholder="Enter password"
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
                editable={!isConnecting}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Domain</Text>
              <TextInput
                style={styles.input}
                value={credentials.domain}
                onChangeText={(value) => handleInputChange('domain', value)}
                placeholder="sip.example.com"
                autoCapitalize="none"
                autoCorrect={false}
                keyboardType="url"
                editable={!isConnecting}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Proxy (Optional)</Text>
              <TextInput
                style={styles.input}
                value={credentials.proxy}
                onChangeText={(value) => handleInputChange('proxy', value)}
                placeholder="Leave empty to use domain"
                autoCapitalize="none"
                autoCorrect={false}
                keyboardType="url"
                editable={!isConnecting}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Transport</Text>
              <View style={styles.transportContainer}>
                {['UDP', 'TCP', 'TLS'].map((transport) => (
                  <TouchableOpacity
                    key={transport}
                    style={[
                      styles.transportButton,
                      credentials.transport === transport && styles.transportButtonActive,
                    ]}
                    onPress={() => handleInputChange('transport', transport)}
                    disabled={isConnecting}>
                    <Text
                      style={[
                        styles.transportButtonText,
                        credentials.transport === transport && styles.transportButtonTextActive,
                      ]}>
                      {transport}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.demoButton]}
              onPress={fillDemoCredentials}
              disabled={isConnecting}>
              <Text style={styles.demoButtonText}>Fill Demo Credentials</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, isConnecting && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={isConnecting}>
              {isConnecting ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator color="#fff" size="small" />
                  <Text style={styles.loginButtonText}>Connecting...</Text>
                </View>
              ) : (
                <Text style={styles.loginButtonText}>Connect</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  form: {
    marginBottom: 30,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  transportContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  transportButton: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
    backgroundColor: '#fff',
  },
  transportButtonActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  transportButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  transportButtonTextActive: {
    color: '#fff',
  },
  actions: {
    gap: 12,
  },
  demoButton: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
    alignItems: 'center',
  },
  demoButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  loginButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  loginButtonDisabled: {
    backgroundColor: '#ccc',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});

export default LoginScreen;
