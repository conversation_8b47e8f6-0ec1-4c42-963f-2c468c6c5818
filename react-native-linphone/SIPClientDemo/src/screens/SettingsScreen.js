import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';

const SettingsScreen = ({sipService, onLogout}) => {
  const [settings, setSettings] = useState({
    autoAnswer: false,
    enableVideo: false,
    enableEchoCancellation: true,
    enableNoiseSuppression: true,
    preferredCodec: 'PCMU',
    dtmfMode: 'RFC2833',
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Logout',
          style: 'destructive',
          onPress: onLogout,
        },
      ]
    );
  };

  const renderSettingItem = (title, subtitle, value, onValueChange, type = 'switch') => (
    <View style={styles.settingItem}>
      <View style={styles.settingInfo}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {type === 'switch' && (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{false: '#e0e0e0', true: '#007AFF'}}
          thumbColor={value ? '#fff' : '#f4f3f4'}
        />
      )}
      {type === 'button' && (
        <TouchableOpacity onPress={onValueChange}>
          <Text style={styles.settingValue}>{value}</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderSectionHeader = (title) => (
    <Text style={styles.sectionHeader}>{title}</Text>
  );

  const showCodecOptions = () => {
    const codecs = ['PCMU', 'PCMA', 'G722', 'G729', 'Opus'];
    Alert.alert(
      'Select Codec',
      'Choose preferred audio codec',
      codecs.map(codec => ({
        text: codec,
        onPress: () => handleSettingChange('preferredCodec', codec),
      }))
    );
  };

  const showDTMFOptions = () => {
    const modes = ['RFC2833', 'SIP INFO', 'In-band'];
    Alert.alert(
      'DTMF Mode',
      'Choose DTMF transmission mode',
      modes.map(mode => ({
        text: mode,
        onPress: () => handleSettingChange('dtmfMode', mode),
      }))
    );
  };

  const showAbout = () => {
    Alert.alert(
      'About SIP Client',
      'Version 1.0.0\n\nBuilt with React Native and Linphone SDK\n\nFeatures:\n• SIP Registration\n• Voice Calls\n• Messaging\n• Call History\n• Contact Management',
      [{text: 'OK'}]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Settings</Text>
      </View>

      {renderSectionHeader('Call Settings')}
      {renderSettingItem(
        'Auto Answer',
        'Automatically answer incoming calls',
        settings.autoAnswer,
        (value) => handleSettingChange('autoAnswer', value)
      )}
      {renderSettingItem(
        'Enable Video',
        'Allow video calls (when supported)',
        settings.enableVideo,
        (value) => handleSettingChange('enableVideo', value)
      )}

      {renderSectionHeader('Audio Settings')}
      {renderSettingItem(
        'Echo Cancellation',
        'Reduce echo during calls',
        settings.enableEchoCancellation,
        (value) => handleSettingChange('enableEchoCancellation', value)
      )}
      {renderSettingItem(
        'Noise Suppression',
        'Reduce background noise',
        settings.enableNoiseSuppression,
        (value) => handleSettingChange('enableNoiseSuppression', value)
      )}
      {renderSettingItem(
        'Preferred Codec',
        'Audio codec for calls',
        settings.preferredCodec,
        showCodecOptions,
        'button'
      )}

      {renderSectionHeader('Advanced')}
      {renderSettingItem(
        'DTMF Mode',
        'How to send DTMF tones',
        settings.dtmfMode,
        showDTMFOptions,
        'button'
      )}

      {renderSectionHeader('Account')}
      <TouchableOpacity style={styles.actionButton} onPress={handleLogout}>
        <Text style={[styles.actionButtonText, styles.logoutText]}>Logout</Text>
      </TouchableOpacity>

      {renderSectionHeader('Information')}
      <TouchableOpacity style={styles.actionButton} onPress={showAbout}>
        <Text style={styles.actionButtonText}>About</Text>
      </TouchableOpacity>

      <View style={styles.footer}>
        <Text style={styles.footerText}>SIP Client v1.0.0</Text>
        <Text style={styles.footerText}>Built with React Native & Linphone</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginTop: 24,
    marginBottom: 8,
    marginHorizontal: 16,
    textTransform: 'uppercase',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  settingValue: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  actionButton: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#007AFF',
    textAlign: 'center',
  },
  logoutText: {
    color: '#f44336',
  },
  footer: {
    alignItems: 'center',
    padding: 32,
  },
  footerText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 4,
  },
});

export default SettingsScreen;
