import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import CallScreen from '../components/CallScreen';

const DialerScreen = ({sipService}) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [currentCall, setCurrentCall] = useState(null);
  const [showCallScreen, setShowCallScreen] = useState(false);

  useEffect(() => {
    // Listen for incoming calls
    const unsubscribeIncoming = sipService.onIncomingCall((call) => {
      setCurrentCall(call);
      setShowCallScreen(true);
    });

    // Listen for call state changes
    const unsubscribeCallState = sipService.onCallStateChanged((event) => {
      if (event.state === 'ended') {
        setCurrentCall(null);
        setShowCallScreen(false);
      }
    });

    return () => {
      unsubscribeIncoming();
      unsubscribeCallState();
    };
  }, [sipService]);

  const dialpadButtons = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['*', '0', '#'],
  ];

  const handleDialpadPress = (digit) => {
    setPhoneNumber(prev => prev + digit);
  };

  const handleBackspace = () => {
    setPhoneNumber(prev => prev.slice(0, -1));
  };

  const handleCall = async () => {
    if (!phoneNumber.trim()) {
      Alert.alert('Error', 'Please enter a phone number');
      return;
    }

    try {
      const callId = await sipService.makeCall(phoneNumber);
      setCurrentCall({
        id: callId,
        remoteAddress: phoneNumber,
        state: 'outgoing',
        startTime: new Date(),
      });
      setShowCallScreen(true);
    } catch (error) {
      Alert.alert('Call Failed', error.message || 'Unable to make call');
    }
  };

  const handleCallEnd = () => {
    setCurrentCall(null);
    setShowCallScreen(false);
  };

  return (
    <View style={styles.container}>
      <View style={styles.displayContainer}>
        <TextInput
          style={styles.phoneNumberDisplay}
          value={phoneNumber}
          onChangeText={setPhoneNumber}
          placeholder="Enter phone number"
          keyboardType="phone-pad"
          textAlign="center"
          fontSize={24}
        />
      </View>

      <View style={styles.dialpadContainer}>
        {dialpadButtons.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.dialpadRow}>
            {row.map((digit) => (
              <TouchableOpacity
                key={digit}
                style={styles.dialpadButton}
                onPress={() => handleDialpadPress(digit)}>
                <Text style={styles.dialpadButtonText}>{digit}</Text>
              </TouchableOpacity>
            ))}
          </View>
        ))}
      </View>

      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={styles.backspaceButton}
          onPress={handleBackspace}>
          <Text style={styles.backspaceButtonText}>⌫</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.callButton,
            !phoneNumber.trim() && styles.callButtonDisabled,
          ]}
          onPress={handleCall}
          disabled={!phoneNumber.trim()}>
          <Text style={styles.callButtonText}>📞</Text>
        </TouchableOpacity>

        <View style={styles.placeholder} />
      </View>

      <Modal
        visible={showCallScreen}
        animationType="slide"
        presentationStyle="fullScreen">
        <CallScreen
          call={currentCall}
          sipService={sipService}
          onCallEnd={handleCallEnd}
        />
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  displayContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  phoneNumberDisplay: {
    width: '100%',
    padding: 16,
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
    fontSize: 24,
    textAlign: 'center',
    color: '#333',
  },
  dialpadContainer: {
    flex: 2,
    justifyContent: 'center',
  },
  dialpadRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 8,
  },
  dialpadButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dialpadButtonText: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 20,
  },
  backspaceButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backspaceButtonText: {
    fontSize: 24,
    color: '#666',
  },
  callButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  callButtonDisabled: {
    backgroundColor: '#ccc',
  },
  callButtonText: {
    fontSize: 32,
    color: '#fff',
  },
  placeholder: {
    width: 60,
  },
});

export default DialerScreen;
