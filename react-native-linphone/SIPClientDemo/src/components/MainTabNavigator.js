import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import DialerScreen from '../screens/DialerScreen';
import CallHistoryScreen from '../screens/CallHistoryScreen';
import MessagesScreen from '../screens/MessagesScreen';
import ContactsScreen from '../screens/ContactsScreen';
import SettingsScreen from '../screens/SettingsScreen';

const MainTabNavigator = ({sipService, onLogout}) => {
  const [activeTab, setActiveTab] = useState('dialer');

  const tabs = [
    {
      id: 'dialer',
      title: 'Dialer',
      icon: '📞',
      component: DialerScreen,
    },
    {
      id: 'history',
      title: 'History',
      icon: '📋',
      component: CallHistoryScreen,
    },
    {
      id: 'messages',
      title: 'Messages',
      icon: '💬',
      component: MessagesScreen,
    },
    {
      id: 'contacts',
      title: 'Contacts',
      icon: '👥',
      component: ContactsScreen,
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: '⚙️',
      component: SettingsScreen,
    },
  ];

  const renderActiveScreen = () => {
    const activeTabData = tabs.find(tab => tab.id === activeTab);
    if (!activeTabData) return null;

    const Component = activeTabData.component;
    return (
      <Component
        sipService={sipService}
        onLogout={onLogout}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>SIP Client</Text>
        <View style={styles.statusIndicator}>
          <View style={styles.statusDot} />
          <Text style={styles.statusText}>Connected</Text>
        </View>
      </View>

      <View style={styles.content}>
        {renderActiveScreen()}
      </View>

      <View style={styles.tabBar}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tabButton,
              activeTab === tab.id && styles.tabButtonActive,
            ]}
            onPress={() => setActiveTab(tab.id)}>
            <Text style={styles.tabIcon}>{tab.icon}</Text>
            <Text
              style={[
                styles.tabTitle,
                activeTab === tab.id && styles.tabTitleActive,
              ]}>
              {tab.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
  },
  statusText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    flex: 1,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingBottom: 8,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  tabButtonActive: {
    backgroundColor: '#f0f8ff',
  },
  tabIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  tabTitle: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  tabTitleActive: {
    color: '#007AFF',
    fontWeight: '600',
  },
});

export default MainTabNavigator;
