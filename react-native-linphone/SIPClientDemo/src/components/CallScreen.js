import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';

const CallScreen = ({call, sipService, onCallEnd}) => {
  const [callState, setCallState] = useState(call?.state || 'connecting');
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [showDTMF, setShowDTMF] = useState(false);

  useEffect(() => {
    let interval;
    
    if (callState === 'connected') {
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [callState]);

  useEffect(() => {
    const unsubscribe = sipService.onCallStateChanged((event) => {
      if (call && event.callId === call.id) {
        setCallState(event.state);
        if (event.state === 'ended') {
          onCallEnd();
        }
      }
    });

    return unsubscribe;
  }, [call, sipService, onCallEnd]);

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswer = async () => {
    try {
      await sipService.answerCall(call.id);
    } catch (error) {
      Alert.alert('Error', 'Failed to answer call');
    }
  };

  const handleHangup = async () => {
    try {
      await sipService.hangupCall(call.id);
    } catch (error) {
      Alert.alert('Error', 'Failed to hang up call');
    }
  };

  const handleMute = async () => {
    try {
      await sipService.toggleMute(call.id);
      setIsMuted(!isMuted);
    } catch (error) {
      Alert.alert('Error', 'Failed to toggle mute');
    }
  };

  const handleSpeaker = async () => {
    try {
      await sipService.toggleSpeaker();
      setIsSpeakerOn(!isSpeakerOn);
    } catch (error) {
      Alert.alert('Error', 'Failed to toggle speaker');
    }
  };

  const handleDTMF = async (digit) => {
    try {
      await sipService.sendDTMF(call.id, digit);
    } catch (error) {
      Alert.alert('Error', 'Failed to send DTMF');
    }
  };

  const getCallStateText = () => {
    switch (callState) {
      case 'incoming':
        return 'Incoming call...';
      case 'outgoing':
        return 'Calling...';
      case 'connecting':
        return 'Connecting...';
      case 'connected':
        return formatDuration(callDuration);
      case 'ended':
        return 'Call ended';
      default:
        return callState;
    }
  };

  const dtmfButtons = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['*', '0', '#'],
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.contactName}>
          {call?.remoteAddress || 'Unknown'}
        </Text>
        <Text style={styles.callStatus}>
          {getCallStateText()}
        </Text>
      </View>

      <View style={styles.content}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>👤</Text>
        </View>
      </View>

      {showDTMF && (
        <View style={styles.dtmfContainer}>
          {dtmfButtons.map((row, rowIndex) => (
            <View key={rowIndex} style={styles.dtmfRow}>
              {row.map((digit) => (
                <TouchableOpacity
                  key={digit}
                  style={styles.dtmfButton}
                  onPress={() => handleDTMF(digit)}>
                  <Text style={styles.dtmfButtonText}>{digit}</Text>
                </TouchableOpacity>
              ))}
            </View>
          ))}
        </View>
      )}

      <View style={styles.controls}>
        {callState === 'connected' && (
          <>
            <TouchableOpacity
              style={[styles.controlButton, isMuted && styles.controlButtonActive]}
              onPress={handleMute}>
              <Text style={styles.controlButtonText}>
                {isMuted ? '🔇' : '🎤'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={() => setShowDTMF(!showDTMF)}>
              <Text style={styles.controlButtonText}>⌨️</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, isSpeakerOn && styles.controlButtonActive]}
              onPress={handleSpeaker}>
              <Text style={styles.controlButtonText}>
                {isSpeakerOn ? '🔊' : '🔈'}
              </Text>
            </TouchableOpacity>
          </>
        )}
      </View>

      <View style={styles.actions}>
        {callState === 'incoming' && (
          <>
            <TouchableOpacity
              style={[styles.actionButton, styles.declineButton]}
              onPress={handleHangup}>
              <Text style={styles.actionButtonText}>📞</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, styles.answerButton]}
              onPress={handleAnswer}>
              <Text style={styles.actionButtonText}>📞</Text>
            </TouchableOpacity>
          </>
        )}

        {(callState === 'outgoing' || callState === 'connecting' || callState === 'connected') && (
          <TouchableOpacity
            style={[styles.actionButton, styles.hangupButton]}
            onPress={handleHangup}>
            <Text style={styles.actionButtonText}>📞</Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  contactName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  callStatus: {
    fontSize: 18,
    color: '#ccc',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 60,
  },
  dtmfContainer: {
    paddingHorizontal: 40,
    paddingVertical: 20,
  },
  dtmfRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 8,
  },
  dtmfButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dtmfButtonText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#fff',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 60,
    paddingVertical: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonActive: {
    backgroundColor: '#007AFF',
  },
  controlButtonText: {
    fontSize: 24,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 60,
    paddingBottom: 40,
  },
  actionButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  answerButton: {
    backgroundColor: '#4CAF50',
  },
  declineButton: {
    backgroundColor: '#f44336',
  },
  hangupButton: {
    backgroundColor: '#f44336',
  },
  actionButtonText: {
    fontSize: 32,
    color: '#fff',
  },
});

export default CallScreen;
