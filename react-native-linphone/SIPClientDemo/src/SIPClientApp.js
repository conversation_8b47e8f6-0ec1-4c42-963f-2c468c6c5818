import React, {useState, useEffect} from 'react';
import {View, StyleSheet} from 'react-native';
import LoginScreen from './screens/LoginScreen';
import MainTabNavigator from './components/MainTabNavigator';
import SIPService from './services/SIPService';

const SIPClientApp = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [sipService] = useState(() => new SIPService());

  useEffect(() => {
    // Initialize SIP service
    sipService.initialize();
    
    // Listen for registration state changes
    const unsubscribe = sipService.onRegistrationStateChanged((state) => {
      if (state === 'registered') {
        setIsLoggedIn(true);
        setIsConnecting(false);
      } else if (state === 'failed' || state === 'cleared') {
        setIsLoggedIn(false);
        setIsConnecting(false);
      }
    });

    return () => {
      unsubscribe();
      sipService.destroy();
    };
  }, [sipService]);

  const handleLogin = async (credentials) => {
    console.log('handleLogin = ', credentials);
    setIsConnecting(true);
    try {
      await sipService.register(credentials);
    } catch (error) {
      console.error('Login failed:', error);
      setIsConnecting(false);
    }
  };

  const handleLogout = async () => {
    try {
      await sipService.unregister();
      setIsLoggedIn(false);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (!isLoggedIn) {
    return (
      <LoginScreen
        onLogin={handleLogin}
        isConnecting={isConnecting}
      />
    );
  }

  return (
    <View style={styles.container}>
      <MainTabNavigator
        sipService={sipService}
        onLogout={handleLogout}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});

export default SIPClientApp;
