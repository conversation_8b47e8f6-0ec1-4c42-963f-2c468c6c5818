import linphone from 'react-native-linphone-voip';

class SIPService {
  constructor() {
    this.isInitialized = false;
    this.registrationState = 'none';
    this.currentCall = null;
    this.callHistory = [];
    this.contacts = [];
    this.messages = [];
    this.eventListeners = new Map();

    // Set global reference for mock
    global.sipServiceInstance = this;
  }

  // Initialize Linphone SDK
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Linphone is already initialized when imported
      this.setupEventListeners();
      this.isInitialized = true;
      console.log('SIP Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize SIP Service:', error);
      throw error;
    }
  }

  // Setup event listeners
  setupEventListeners() {
    // Registration state changes
    this.unsubscribeRegistration = linphone.onRegistrationStateChanged((event) => {
      console.log('Registration state changed:', event);
      this.registrationState = event.state;
      this.notifyListeners('registrationStateChanged', event.state);
    });

    // Incoming call
    this.unsubscribeIncoming = linphone.onCallIncoming((event) => {
      console.log('Incoming call:', event);
      this.currentCall = {
        id: event.callId,
        remoteAddress: event.remoteAddress,
        state: 'incoming',
        startTime: new Date(),
      };
      this.notifyListeners('incomingCall', this.currentCall);
    });

    // Call state changes
    this.unsubscribeCallState = linphone.onCallStateChanged((event) => {
      console.log('Call state changed:', event);
      if (this.currentCall && this.currentCall.id === event.callId) {
        this.currentCall.state = event.state;

        if (event.state === 'connected') {
          this.currentCall.connectedTime = new Date();
        } else if (event.state === 'ended') {
          this.currentCall.endTime = new Date();
          this.addToCallHistory(this.currentCall);
          this.currentCall = null;
        }
      }
      this.notifyListeners('callStateChanged', event);
    });

    // Call connected
    this.unsubscribeCallConnected = linphone.onCallConnected((event) => {
      console.log('Call connected:', event);
      if (this.currentCall && this.currentCall.id === event.callId) {
        this.currentCall.state = 'connected';
        this.currentCall.connectedTime = new Date();
      }
      this.notifyListeners('callStateChanged', event);
    });

    // Call ended
    this.unsubscribeCallEnded = linphone.onCallEnded((event) => {
      console.log('Call ended:', event);
      if (this.currentCall && this.currentCall.id === event.callId) {
        this.currentCall.endTime = new Date();
        this.addToCallHistory(this.currentCall);
        this.currentCall = null;
      }
      this.notifyListeners('callStateChanged', event);
    });

    // Message received
    this.unsubscribeMessage = linphone.onMessageReceived((event) => {
      console.log('Message received:', event);
      const message = {
        id: Date.now().toString(),
        from: event.from,
        to: event.to,
        content: event.content,
        timestamp: new Date(event.timestamp),
        type: 'received',
      };
      this.messages.push(message);
      this.notifyListeners('messageReceived', message);
    });
  }

  // Register SIP account
  async register(credentials) {
    try {
      const config = {
        username: credentials.username,
        password: credentials.password,
        domain: credentials.domain,
        transport: credentials.transport || 'UDP',
      };
      console.log('Registration with config', config);
      await linphone.register(config);
      console.log('Registration initiated');
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  }

  // Unregister SIP account
  async unregister() {
    try {
      await linphone.unregister();
      this.registrationState = 'none';
      console.log('Unregistration initiated');
    } catch (error) {
      console.error('Unregistration failed:', error);
      throw error;
    }
  }

  // Make outgoing call
  async makeCall(address) {
    try {
      const result = await linphone.call(address);
      this.currentCall = {
        id: Date.now().toString(), // Generate a call ID
        remoteAddress: address,
        state: 'outgoing',
        startTime: new Date(),
      };
      return this.currentCall.id;
    } catch (error) {
      console.error('Failed to make call:', error);
      throw error;
    }
  }

  // Answer incoming call
  async answerCall(callId) {
    try {
      await linphone.accept();
    } catch (error) {
      console.error('Failed to answer call:', error);
      throw error;
    }
  }

  // Decline/Hang up call
  async hangupCall(callId) {
    try {
      await linphone.hangup();
    } catch (error) {
      console.error('Failed to hangup call:', error);
      throw error;
    }
  }

  // Send DTMF tones
  async sendDTMF(callId, dtmf) {
    try {
      // DTMF functionality would need to be implemented in the native module
      console.log('DTMF not implemented yet:', dtmf);
    } catch (error) {
      console.error('Failed to send DTMF:', error);
      throw error;
    }
  }

  // Send message
  async sendMessage(to, message) {
    try {
      await linphone.sendMessage(to, message);
      const messageObj = {
        id: Date.now().toString(),
        from: 'me',
        to: to,
        content: message,
        timestamp: new Date(),
        type: 'sent',
      };
      this.messages.push(messageObj);
      this.notifyListeners('messageSent', messageObj);
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }

  // Mute/Unmute microphone
  async toggleMute(callId) {
    try {
      await linphone.mute();
    } catch (error) {
      console.error('Failed to toggle mute:', error);
      throw error;
    }
  }

  // Enable/Disable speaker
  async toggleSpeaker() {
    try {
      await linphone.toggleSpeaker();
    } catch (error) {
      console.error('Failed to toggle speaker:', error);
      throw error;
    }
  }

  // Add call to history
  addToCallHistory(call) {
    const historyEntry = {
      ...call,
      duration: call.connectedTime && call.endTime 
        ? call.endTime - call.connectedTime 
        : 0,
    };
    this.callHistory.unshift(historyEntry);
    this.notifyListeners('callHistoryUpdated', this.callHistory);
  }

  // Event listener management
  onRegistrationStateChanged(callback) {
    return this.addEventListener('registrationStateChanged', callback);
  }

  onIncomingCall(callback) {
    return this.addEventListener('incomingCall', callback);
  }

  onCallStateChanged(callback) {
    return this.addEventListener('callStateChanged', callback);
  }

  onMessageReceived(callback) {
    return this.addEventListener('messageReceived', callback);
  }

  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);

    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get(event);
      if (listeners) {
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  notifyListeners(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // Getters
  getRegistrationState() {
    return this.registrationState;
  }

  getCurrentCall() {
    return this.currentCall;
  }

  getCallHistory() {
    return this.callHistory;
  }

  getMessages() {
    return this.messages;
  }

  // Cleanup
  destroy() {
    if (this.isInitialized) {
      // Unsubscribe from native events
      if (this.unsubscribeRegistration) this.unsubscribeRegistration();
      if (this.unsubscribeIncoming) this.unsubscribeIncoming();
      if (this.unsubscribeCallState) this.unsubscribeCallState();
      if (this.unsubscribeCallConnected) this.unsubscribeCallConnected();
      if (this.unsubscribeCallEnded) this.unsubscribeCallEnded();
      if (this.unsubscribeMessage) this.unsubscribeMessage();

      // Clean up event listeners
      this.eventListeners.clear();
      this.isInitialized = false;
    }
  }
}

export default SIPService;
