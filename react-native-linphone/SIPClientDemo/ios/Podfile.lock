PODS:
  - boost (1.76.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.70.5)
  - FBReactNativeSpec (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.5)
    - RCTTypeSafety (= 0.70.5)
    - React-Core (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.70.5)
  - libevent (2.1.12)
  - linphone-sdk (5.2.114)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.70.5)
  - RCTTypeSafety (0.70.5):
    - FBLazyVector (= 0.70.5)
    - RCTRequired (= 0.70.5)
    - React-Core (= 0.70.5)
  - React (0.70.5):
    - React-Core (= 0.70.5)
    - React-Core/DevSupport (= 0.70.5)
    - React-Core/RCTWebSocket (= 0.70.5)
    - React-RCTActionSheet (= 0.70.5)
    - React-RCTAnimation (= 0.70.5)
    - React-RCTBlob (= 0.70.5)
    - React-RCTImage (= 0.70.5)
    - React-RCTLinking (= 0.70.5)
    - React-RCTNetwork (= 0.70.5)
    - React-RCTSettings (= 0.70.5)
    - React-RCTText (= 0.70.5)
    - React-RCTVibration (= 0.70.5)
  - React-bridging (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi (= 0.70.5)
  - React-callinvoker (0.70.5)
  - React-Codegen (0.70.5):
    - FBReactNativeSpec (= 0.70.5)
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.5)
    - RCTTypeSafety (= 0.70.5)
    - React-Core (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-Core (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.5)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/CoreModulesHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/Default (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/DevSupport (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.5)
    - React-Core/RCTWebSocket (= 0.70.5)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-jsinspector (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTBlobHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTImageHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTTextHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTWebSocket (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.5)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-CoreModules (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/CoreModulesHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-RCTImage (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-cxxreact (0.70.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsinspector (= 0.70.5)
    - React-logger (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - React-runtimeexecutor (= 0.70.5)
  - React-hermes (0.70.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-jsinspector (= 0.70.5)
    - React-perflogger (= 0.70.5)
  - React-jsi (0.70.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi/Default (= 0.70.5)
  - React-jsi/Default (0.70.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.70.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-perflogger (= 0.70.5)
  - React-jsinspector (0.70.5)
  - React-logger (0.70.5):
    - glog
  - react-native-linphone-voip (0.1.0):
    - linphone-sdk (~> 5.2.0)
    - React-Core
  - React-perflogger (0.70.5)
  - React-RCTActionSheet (0.70.5):
    - React-Core/RCTActionSheetHeaders (= 0.70.5)
  - React-RCTAnimation (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTAnimationHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTBlob (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTBlobHeaders (= 0.70.5)
    - React-Core/RCTWebSocket (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-RCTNetwork (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTImage (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTImageHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-RCTNetwork (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTLinking (0.70.5):
    - React-Codegen (= 0.70.5)
    - React-Core/RCTLinkingHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTNetwork (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTNetworkHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTSettings (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTSettingsHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTText (0.70.5):
    - React-Core/RCTTextHeaders (= 0.70.5)
  - React-RCTVibration (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTVibrationHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-runtimeexecutor (0.70.5):
    - React-jsi (= 0.70.5)
  - ReactCommon/turbomodule/core (0.70.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-bridging (= 0.70.5)
    - React-callinvoker (= 0.70.5)
    - React-Core (= 0.70.5)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-logger (= 0.70.5)
    - React-perflogger (= 0.70.5)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-bridging (from `../node_modules/react-native/ReactCommon`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-linphone-voip (from `../node_modules/react-native-linphone-voip`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  https://gitlab.linphone.org/BC/public/podspec.git:
    - linphone-sdk
  trunk:
    - fmt
    - libevent

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes/hermes-engine.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-bridging:
    :path: "../node_modules/react-native/ReactCommon"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-linphone-voip:
    :path: "../node_modules/react-native-linphone-voip"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 9fa78656d705f55b1220151d997e57e2a3f2cde0
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: affa4ba1bfdaac110a789192f4d452b053a86624
  FBReactNativeSpec: fe8b5f1429cfe83a8d72dc8ed61dc7704cac8745
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  hermes-engine: 7fe5fc6ef707b7fdcb161b63898ec500e285653d
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  linphone-sdk: dfe70cd91cd826e1a6833f349525a305f11b438c
  RCT-Folly: fd2673f5bfadb2a8173799b6965b892a67ae31d9
  RCTRequired: 21229f84411088e5d8538f21212de49e46cc83e2
  RCTTypeSafety: 62eed57a32924b09edaaf170a548d1fc96223086
  React: f0254ccddeeef1defe66c6b1bb9133a4f040792b
  React-bridging: e46911666b7ec19538a620a221d6396cd293d687
  React-callinvoker: 66b62e2c34546546b2f21ab0b7670346410a2b53
  React-Codegen: b6999435966df3bdf82afa3f319ba0d6f9a8532a
  React-Core: 0072bd91c2e62e72b72781ef209b84c686f154df
  React-CoreModules: 5b6b7668f156f73a56420df9ec68ca2ec8f2e818
  React-cxxreact: a099892e4ae5378bfb69e5f9687f23eed754accb
  React-hermes: 4c911673c9a0e1e57493ee99b605352fadf17db0
  React-jsi: 0ac3d48db875eb15f6d960689f1e2c11dac46bae
  React-jsiexecutor: 4af6f5be6f13d9b6125fbf4b4e035779dc8c7096
  React-jsinspector: badd81696361249893a80477983e697aab3c1a34
  React-logger: e214e31cd09184f9e7073612575ad44dc5a0f434
  react-native-linphone-voip: 5fdb678cd8cb33328216050a21e834e4a2d8bf1c
  React-perflogger: e68d3795cf5d247a0379735cbac7309adf2fb931
  React-RCTActionSheet: 05452c3b281edb27850253db13ecd4c5a65bc247
  React-RCTAnimation: 578eebac706428e68466118e84aeacf3a282b4da
  React-RCTBlob: f47a0aa61e7d1fb1a0e13da832b0da934939d71a
  React-RCTImage: 60f54b66eed65d86b6dffaf4733d09161d44929d
  React-RCTLinking: 91073205aeec4b29450ca79b709277319368ac9e
  React-RCTNetwork: ca91f2c9465a7e335c8a5fae731fd7f10572213b
  React-RCTSettings: 1a9a5d01337d55c18168c1abe0f4a589167d134a
  React-RCTText: c591e8bd9347a294d8416357ca12d779afec01d5
  React-RCTVibration: 8e5c8c5d17af641f306d7380d8d0fe9b3c142c48
  React-runtimeexecutor: 7401c4a40f8728fd89df4a56104541b760876117
  ReactCommon: 70da22997e823b14851ce18d9024224ee4476b2d
  Yoga: eca980a5771bf114c41a754098cd85e6e0d90ed7

PODFILE CHECKSUM: c8c4457693cd592df264fd3f310cf7e23bb832bd

COCOAPODS: 1.16.2
