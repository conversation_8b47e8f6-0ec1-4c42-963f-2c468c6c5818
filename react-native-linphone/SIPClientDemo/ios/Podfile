require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

# Add Linphone SDK repository
source 'https://gitlab.linphone.org/BC/public/podspec.git'
source 'https://cdn.cocoapods.org/'

platform :ios, '12.4'
install! 'cocoapods', :deterministic_uuids => false

target 'SIPClientDemo' do
  config = use_native_modules!

  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # Hermes is now enabled by default. Disable by setting this flag to false.
    # Upcoming versions of React Native may rely on get_default_flags(), but
    # we make it explicit here to aid in the React Native upgrade process.
    :hermes_enabled => true,
    :fabric_enabled => flags[:fabric_enabled],
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, <PERSON>lipper will not work and
    # you should disable the next line.
    :flipper_configuration => FlipperConfiguration.disabled,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'SIPClientDemoTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    react_native_post_install(
      installer,
      # Set `mac_catalyst_enabled` to `true` in order to apply patches
      # necessary for Mac Catalyst builds
      :mac_catalyst_enabled => false
    )
    __apply_Xcode_12_5_M1_post_install_workaround(installer)

    # Fix for Linphone SDK header maps issue and deployment target
    installer.pods_project.targets.each do |target|
      if ['linphone-sdk', 'react-native-linphone-voip'].include?(target.name)
        target.build_configurations.each do |config|
          config.build_settings['USE_HEADERMAP'] = 'NO'
          config.build_settings['ALWAYS_SEARCH_USER_PATHS'] = 'NO'
          # Fix Folly config path conflict
          config.build_settings['HEADER_SEARCH_PATHS'] = '$(inherited) "${PODS_ROOT}/RCT-Folly"'
          config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++17'
        end
      end

      # Fix deployment target for all pods to match Linphone SDK requirement
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.4'
      end
    end
  end
end
