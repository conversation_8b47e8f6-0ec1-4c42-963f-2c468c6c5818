// Mock Linphone object for testing UI without native module
export const mockLinphone = {
  register: async (config: any) => {
    console.log('Mock register:', config);
    return { status: 'success', message: 'Registration initiated' };
  },
  
  unregister: async () => {
    console.log('Mock unregister');
    return { status: 'success', message: 'Unregistration completed' };
  },
  
  call: async (uri: string) => {
    console.log('Mock call:', uri);
    return { status: 'success', message: 'Call initiated', uri };
  },
  
  hangup: async () => {
    console.log('Mock hangup');
    return { status: 'success', message: 'Call terminated' };
  },
  
  accept: async () => {
    console.log('Mock accept');
    return { status: 'success', message: 'Call accepted' };
  },
  
  decline: async () => {
    console.log('Mock decline');
    return { status: 'success', message: 'Call declined' };
  },
  
  mute: async () => {
    console.log('Mock mute');
    return { status: 'success', muted: true };
  },
  
  toggleSpeaker: async () => {
    console.log('Mock toggleSpeaker');
    return { status: 'success', speakerEnabled: true };
  },
  
  sendMessage: async (to: string, content: string) => {
    console.log('Mock sendMessage:', to, content);
    return { status: 'success', message: 'Message sent', to, content };
  },
  
  createChatRoom: async (uri: string) => {
    console.log('Mock createChatRoom:', uri);
    return { status: 'success', message: 'Chat room created', uri };
  },
  
  getCallHistory: async () => {
    console.log('Mock getCallHistory');
    return { 
      status: 'success', 
      history: [
        {
          remoteAddress: 'sip:<EMAIL>',
          direction: 'Outgoing',
          startTime: Date.now() / 1000 - 3600,
          duration: 120,
          status: 'Success'
        },
        {
          remoteAddress: 'sip:<EMAIL>',
          direction: 'Incoming',
          startTime: Date.now() / 1000 - 7200,
          duration: 0,
          status: 'Missed'
        }
      ]
    };
  },
  
  startConference: async () => {
    console.log('Mock startConference');
    return { status: 'success', message: 'Conference started' };
  },
  
  inviteToConference: async (participant: string) => {
    console.log('Mock inviteToConference:', participant);
    return { status: 'success', message: 'Participant invited', participant };
  },
  
  // Event handlers (mock)
  onRegistrationStateChanged: (callback: (event: any) => void) => {
    console.log('Mock onRegistrationStateChanged');
    // Simulate successful registration after 2 seconds
    setTimeout(() => {
      callback({ state: 'Ok', message: 'Registration successful' });
    }, 2000);
    
    return () => console.log('Unsubscribe registration events');
  },
  
  onCallStateChanged: (callback: (event: any) => void) => {
    console.log('Mock onCallStateChanged');
    return () => console.log('Unsubscribe call events');
  },
  
  onMessageReceived: (callback: (event: any) => void) => {
    console.log('Mock onMessageReceived');
    return () => console.log('Unsubscribe message events');
  },
  
  onCallIncoming: (callback: (event: any) => void) => {
    console.log('Mock onCallIncoming');
    return () => console.log('Unsubscribe incoming call events');
  },
  
  onCallConnected: (callback: (event: any) => void) => {
    console.log('Mock onCallConnected');
    return () => console.log('Unsubscribe call connected events');
  },
  
  onCallEnded: (callback: (event: any) => void) => {
    console.log('Mock onCallEnded');
    return () => console.log('Unsubscribe call ended events');
  },
  
  onConferenceJoined: (callback: (event: any) => void) => {
    console.log('Mock onConferenceJoined');
    return () => console.log('Unsubscribe conference events');
  },
  
  on: (eventType: string, callback: (data: any) => void) => {
    console.log('Mock on:', eventType);
    return () => console.log('Unsubscribe event:', eventType);
  },
  
  off: (eventType: string, callback?: (data: any) => void) => {
    console.log('Mock off:', eventType);
  }
};

export default mockLinphone;
