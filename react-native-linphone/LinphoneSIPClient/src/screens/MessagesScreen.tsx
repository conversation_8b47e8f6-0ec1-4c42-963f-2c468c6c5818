import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  Modal,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import linphone, { MessageEvent } from 'react-native-linphone-voip';

interface ChatRoom {
  id: string;
  remoteAddress: string;
  displayName: string;
  lastMessage: string;
  lastMessageTime: number;
  unreadCount: number;
}

interface Message {
  id: string;
  from: string;
  to: string;
  content: string;
  timestamp: number;
  isOutgoing: boolean;
}

const MessagesScreen: React.FC = () => {
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [selectedChatRoom, setSelectedChatRoom] = useState<ChatRoom | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [showNewChatModal, setShowNewChatModal] = useState(false);
  const [newChatAddress, setNewChatAddress] = useState('');

  useEffect(() => {
    // Listen for incoming messages
    const unsubscribe = linphone.onMessageReceived((event: MessageEvent) => {
      console.log('Message received:', event);
      
      const message: Message = {
        id: `${event.timestamp}-${Math.random()}`,
        from: event.from,
        to: event.to,
        content: event.content,
        timestamp: event.timestamp,
        isOutgoing: false,
      };

      // Add message to the current chat if it's open
      if (selectedChatRoom && event.from === selectedChatRoom.remoteAddress) {
        setMessages(prev => [...prev, message]);
      }

      // Update chat rooms list
      updateChatRoomWithMessage(event.from, event.content, event.timestamp);
    });

    return unsubscribe;
  }, [selectedChatRoom]);

  const updateChatRoomWithMessage = (address: string, content: string, timestamp: number) => {
    setChatRooms(prev => {
      const existingIndex = prev.findIndex(room => room.remoteAddress === address);
      
      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = {
          ...updated[existingIndex],
          lastMessage: content,
          lastMessageTime: timestamp,
          unreadCount: updated[existingIndex].unreadCount + 1,
        };
        return updated.sort((a, b) => b.lastMessageTime - a.lastMessageTime);
      } else {
        const newRoom: ChatRoom = {
          id: address,
          remoteAddress: address,
          displayName: extractDisplayName(address),
          lastMessage: content,
          lastMessageTime: timestamp,
          unreadCount: 1,
        };
        return [newRoom, ...prev];
      }
    });
  };

  const extractDisplayName = (address: string) => {
    const match = address.match(/sip:([^@]+)@/);
    return match ? match[1] : address;
  };

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedChatRoom) return;

    try {
      const result = await linphone.sendMessage(selectedChatRoom.remoteAddress, newMessage.trim());
      
      if (result.status === 'success') {
        const message: Message = {
          id: `${Date.now()}-${Math.random()}`,
          from: 'me',
          to: selectedChatRoom.remoteAddress,
          content: newMessage.trim(),
          timestamp: Date.now() / 1000,
          isOutgoing: true,
        };

        setMessages(prev => [...prev, message]);
        updateChatRoomWithMessage(selectedChatRoom.remoteAddress, newMessage.trim(), Date.now() / 1000);
        setNewMessage('');
      } else {
        Alert.alert('Error', result.message || 'Failed to send message');
      }
    } catch (error) {
      console.error('Send message error:', error);
      Alert.alert('Error', 'Failed to send message');
    }
  };

  const handleStartNewChat = async () => {
    if (!newChatAddress.trim()) {
      Alert.alert('Error', 'Please enter a valid address');
      return;
    }

    try {
      let sipUri = newChatAddress.trim();
      if (!sipUri.startsWith('sip:')) {
        sipUri = `sip:${sipUri}@sip.example.com`;
      }

      const result = await linphone.createChatRoom(sipUri);
      
      if (result.status === 'success') {
        const newRoom: ChatRoom = {
          id: sipUri,
          remoteAddress: sipUri,
          displayName: extractDisplayName(sipUri),
          lastMessage: '',
          lastMessageTime: Date.now() / 1000,
          unreadCount: 0,
        };

        setChatRooms(prev => [newRoom, ...prev]);
        setSelectedChatRoom(newRoom);
        setMessages([]);
        setShowNewChatModal(false);
        setNewChatAddress('');
      } else {
        Alert.alert('Error', result.message || 'Failed to create chat room');
      }
    } catch (error) {
      console.error('Create chat room error:', error);
      Alert.alert('Error', 'Failed to create chat room');
    }
  };

  const renderChatRoomItem = ({ item }: { item: ChatRoom }) => (
    <TouchableOpacity
      style={styles.chatRoomItem}
      onPress={() => {
        setSelectedChatRoom(item);
        setMessages([]); // In a real app, load messages from storage
        // Mark as read
        setChatRooms(prev => 
          prev.map(room => 
            room.id === item.id ? { ...room, unreadCount: 0 } : room
          )
        );
      }}
    >
      <View style={styles.avatarContainer}>
        <Text style={styles.avatarText}>
          {item.displayName.charAt(0).toUpperCase()}
        </Text>
      </View>

      <View style={styles.chatRoomDetails}>
        <View style={styles.chatRoomHeader}>
          <Text style={styles.chatRoomName}>{item.displayName}</Text>
          <Text style={styles.chatRoomTime}>
            {formatTime(item.lastMessageTime)}
          </Text>
        </View>
        <View style={styles.chatRoomFooter}>
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage || 'No messages yet'}
          </Text>
          {item.unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadCount}>{item.unreadCount}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderMessage = ({ item }: { item: Message }) => (
    <View style={[
      styles.messageContainer,
      item.isOutgoing ? styles.outgoingMessage : styles.incomingMessage
    ]}>
      <Text style={[
        styles.messageText,
        item.isOutgoing ? styles.outgoingMessageText : styles.incomingMessageText
      ]}>
        {item.content}
      </Text>
      <Text style={styles.messageTime}>
        {formatTime(item.timestamp)}
      </Text>
    </View>
  );

  const renderChatRoomsList = () => (
    <View style={styles.container}>
      <FlatList
        data={chatRooms}
        renderItem={renderChatRoomItem}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="message" size={64} color="#CCC" />
            <Text style={styles.emptyTitle}>No Messages</Text>
            <Text style={styles.emptySubtitle}>
              Start a conversation by tapping the + button
            </Text>
          </View>
        }
        contentContainerStyle={chatRooms.length === 0 ? styles.emptyList : undefined}
      />

      <TouchableOpacity
        style={styles.fab}
        onPress={() => setShowNewChatModal(true)}
      >
        <Icon name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  const renderChatView = () => (
    <View style={styles.container}>
      <View style={styles.chatHeader}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => setSelectedChatRoom(null)}
        >
          <Icon name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.chatHeaderTitle}>
          {selectedChatRoom?.displayName}
        </Text>
        <TouchableOpacity style={styles.callButton}>
          <Icon name="call" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContainer}
      />

      <View style={styles.inputContainer}>
        <TextInput
          style={styles.messageInput}
          value={newMessage}
          onChangeText={setNewMessage}
          placeholder="Type a message..."
          multiline
          maxLength={1000}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            !newMessage.trim() && styles.sendButtonDisabled
          ]}
          onPress={handleSendMessage}
          disabled={!newMessage.trim()}
        >
          <Icon name="send" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {selectedChatRoom ? renderChatView() : renderChatRoomsList()}

      <Modal
        visible={showNewChatModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowNewChatModal(false)}
            >
              <Text style={styles.modalCancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>New Message</Text>
            <TouchableOpacity
              onPress={handleStartNewChat}
              disabled={!newChatAddress.trim()}
            >
              <Text style={[
                styles.modalDoneButton,
                !newChatAddress.trim() && styles.modalDoneButtonDisabled
              ]}>
                Done
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <Text style={styles.modalLabel}>To:</Text>
            <TextInput
              style={styles.modalInput}
              value={newChatAddress}
              onChangeText={setNewChatAddress}
              placeholder="Enter SIP address (e.g., <EMAIL>)"
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="email-address"
            />
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  chatRoomItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  chatRoomDetails: {
    flex: 1,
  },
  chatRoomHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatRoomName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  chatRoomTime: {
    fontSize: 14,
    color: '#8E8E93',
  },
  chatRoomFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    fontSize: 14,
    color: '#8E8E93',
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: '#007AFF',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadCount: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    marginRight: 12,
  },
  chatHeaderTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  callButton: {
    marginLeft: 12,
  },
  messagesList: {
    flex: 1,
  },
  messagesContainer: {
    padding: 16,
  },
  messageContainer: {
    maxWidth: '80%',
    marginVertical: 4,
    padding: 12,
    borderRadius: 18,
  },
  incomingMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#E5E5EA',
  },
  outgoingMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#007AFF',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  incomingMessageText: {
    color: '#333',
  },
  outgoingMessageText: {
    color: '#FFFFFF',
  },
  messageTime: {
    fontSize: 12,
    color: '#8E8E93',
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  messageInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sendButtonDisabled: {
    backgroundColor: '#CCC',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyList: {
    flex: 1,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalCancelButton: {
    fontSize: 16,
    color: '#007AFF',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  modalDoneButton: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  modalDoneButtonDisabled: {
    color: '#CCC',
  },
  modalContent: {
    padding: 16,
  },
  modalLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
});

export default MessagesScreen;
