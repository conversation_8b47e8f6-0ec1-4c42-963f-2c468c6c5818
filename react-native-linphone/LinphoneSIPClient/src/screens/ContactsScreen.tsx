import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  Modal,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import linphone from 'react-native-linphone-voip';

interface Contact {
  id: string;
  name: string;
  sipAddress: string;
  phoneNumber?: string;
}

const ContactsScreen: React.FC = () => {
  const [contacts, setContacts] = useState<Contact[]>([
    {
      id: '1',
      name: '<PERSON>',
      sipAddress: 'sip:<EMAIL>',
      phoneNumber: '1001',
    },
    {
      id: '2',
      name: '<PERSON>',
      sipAddress: 'sip:<EMAIL>',
      phoneNumber: '1002',
    },
    {
      id: '3',
      name: '<PERSON>',
      sipAddress: 'sip:<EMAIL>',
      phoneNumber: '1003',
    },
  ]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newContactName, setNewContactName] = useState('');
  const [newContactSipAddress, setNewContactSipAddress] = useState('');
  const [newContactPhoneNumber, setNewContactPhoneNumber] = useState('');

  const handleCall = async (contact: Contact) => {
    try {
      const result = await linphone.call(contact.sipAddress);
      
      if (result.status === 'success') {
        Alert.alert('Call', `Calling ${contact.name}...`);
      } else {
        Alert.alert('Error', result.message || 'Failed to make call');
      }
    } catch (error) {
      console.error('Call error:', error);
      Alert.alert('Error', 'Failed to make call');
    }
  };

  const handleMessage = async (contact: Contact) => {
    try {
      const result = await linphone.createChatRoom(contact.sipAddress);
      
      if (result.status === 'success') {
        Alert.alert('Message', `Opening chat with ${contact.name}...`);
        // In a real app, navigate to messages screen with this contact
      } else {
        Alert.alert('Error', result.message || 'Failed to create chat room');
      }
    } catch (error) {
      console.error('Message error:', error);
      Alert.alert('Error', 'Failed to create chat room');
    }
  };

  const handleAddContact = () => {
    if (!newContactName.trim() || !newContactSipAddress.trim()) {
      Alert.alert('Error', 'Please fill in name and SIP address');
      return;
    }

    let sipAddress = newContactSipAddress.trim();
    if (!sipAddress.startsWith('sip:')) {
      sipAddress = `sip:${sipAddress}`;
    }

    const newContact: Contact = {
      id: Date.now().toString(),
      name: newContactName.trim(),
      sipAddress,
      phoneNumber: newContactPhoneNumber.trim() || undefined,
    };

    setContacts(prev => [...prev, newContact].sort((a, b) => a.name.localeCompare(b.name)));
    setShowAddModal(false);
    setNewContactName('');
    setNewContactSipAddress('');
    setNewContactPhoneNumber('');
  };

  const handleDeleteContact = (contactId: string) => {
    Alert.alert(
      'Delete Contact',
      'Are you sure you want to delete this contact?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setContacts(prev => prev.filter(contact => contact.id !== contactId));
          },
        },
      ]
    );
  };

  const renderContactItem = ({ item }: { item: Contact }) => (
    <View style={styles.contactItem}>
      <View style={styles.avatarContainer}>
        <Text style={styles.avatarText}>
          {item.name.charAt(0).toUpperCase()}
        </Text>
      </View>

      <View style={styles.contactDetails}>
        <Text style={styles.contactName}>{item.name}</Text>
        <Text style={styles.contactSipAddress}>{item.sipAddress}</Text>
        {item.phoneNumber && (
          <Text style={styles.contactPhoneNumber}>{item.phoneNumber}</Text>
        )}
      </View>

      <View style={styles.contactActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleMessage(item)}
        >
          <Icon name="message" size={20} color="#007AFF" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleCall(item)}
        >
          <Icon name="call" size={20} color="#34C759" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleDeleteContact(item.id)}
        >
          <Icon name="delete" size={20} color="#FF3B30" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="contacts" size={64} color="#CCC" />
      <Text style={styles.emptyTitle}>No Contacts</Text>
      <Text style={styles.emptySubtitle}>
        Add contacts to quickly call or message them
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={contacts}
        renderItem={renderContactItem}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={contacts.length === 0 ? styles.emptyList : undefined}
        showsVerticalScrollIndicator={false}
      />

      <TouchableOpacity
        style={styles.fab}
        onPress={() => setShowAddModal(true)}
      >
        <Icon name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity>

      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowAddModal(false)}
            >
              <Text style={styles.modalCancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Add Contact</Text>
            <TouchableOpacity
              onPress={handleAddContact}
              disabled={!newContactName.trim() || !newContactSipAddress.trim()}
            >
              <Text style={[
                styles.modalDoneButton,
                (!newContactName.trim() || !newContactSipAddress.trim()) && styles.modalDoneButtonDisabled
              ]}>
                Save
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Name *</Text>
              <TextInput
                style={styles.input}
                value={newContactName}
                onChangeText={setNewContactName}
                placeholder="Enter contact name"
                autoCapitalize="words"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>SIP Address *</Text>
              <TextInput
                style={styles.input}
                value={newContactSipAddress}
                onChangeText={setNewContactSipAddress}
                placeholder="<EMAIL> or sip:<EMAIL>"
                autoCapitalize="none"
                autoCorrect={false}
                keyboardType="email-address"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Phone Number</Text>
              <TextInput
                style={styles.input}
                value={newContactPhoneNumber}
                onChangeText={setNewContactPhoneNumber}
                placeholder="Extension or phone number"
                keyboardType="phone-pad"
              />
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  contactDetails: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  contactSipAddress: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 2,
  },
  contactPhoneNumber: {
    fontSize: 14,
    color: '#8E8E93',
  },
  contactActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyList: {
    flex: 1,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalCancelButton: {
    fontSize: 16,
    color: '#007AFF',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  modalDoneButton: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  modalDoneButtonDisabled: {
    color: '#CCC',
  },
  modalContent: {
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
});

export default ContactsScreen;
