import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
// import linphone from 'react-native-linphone-voip';
import mockLinphone from '../utils/mockLinphone';
const linphone = mockLinphone;

interface LoginScreenProps {
  onLoginSuccess: () => void;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ onLoginSuccess }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [domain, setDomain] = useState('');
  const [transport, setTransport] = useState<'UDP' | 'TCP' | 'TLS'>('UDP');
  const [isLoading, setIsLoading] = useState(false);
  const [registrationStatus, setRegistrationStatus] = useState('');

  useEffect(() => {
    // Listen for registration state changes
    const unsubscribe = linphone.onRegistrationStateChanged((event) => {
      console.log('Registration state changed:', event);
      setRegistrationStatus(`${event.state}: ${event.message}`);
      
      if (event.state === 'Ok') {
        setIsLoading(false);
        Alert.alert('Success', 'SIP registration successful!', [
          { text: 'OK', onPress: onLoginSuccess }
        ]);
      } else if (event.state === 'Failed') {
        setIsLoading(false);
        Alert.alert('Error', `Registration failed: ${event.message}`);
      }
    });

    return unsubscribe;
  }, [onLoginSuccess]);

  const handleLogin = async () => {
    if (!username.trim() || !password.trim() || !domain.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    setRegistrationStatus('Registering...');

    try {
      const config = {
        username: username.trim(),
        password: password.trim(),
        domain: domain.trim(),
        transport,
      };
    console.log('Start register with ', config);
      const result = await linphone.register(config);
      console.log('Registration result:', result);
      
      if (result.status === 'success') {
        setRegistrationStatus('Registration initiated...');
      } else {
        setIsLoading(false);
        Alert.alert('Error', result.message || 'Registration failed');
      }
    } catch (error) {
      setIsLoading(false);
      console.error('Registration error:', error);
      Alert.alert('Error', 'Failed to register SIP account');
    }
  };

  const transportOptions = [
    { label: 'UDP', value: 'UDP' as const },
    { label: 'TCP', value: 'TCP' as const },
    { label: 'TLS', value: 'TLS' as const },
  ];

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>SIP Login</Text>
          <Text style={styles.subtitle}>Enter your SIP account details</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Username *</Text>
            <TextInput
              style={styles.input}
              value={username}
              onChangeText={setUsername}
              placeholder="Enter username"
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Password *</Text>
            <TextInput
              style={styles.input}
              value={password}
              onChangeText={setPassword}
              placeholder="Enter password"
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Domain *</Text>
            <TextInput
              style={styles.input}
              value={domain}
              onChangeText={setDomain}
              placeholder="sip.example.com"
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="url"
              editable={!isLoading}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Transport</Text>
            <View style={styles.transportContainer}>
              {transportOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.transportOption,
                    transport === option.value && styles.transportOptionSelected,
                  ]}
                  onPress={() => setTransport(option.value)}
                  disabled={isLoading}
                >
                  <Text
                    style={[
                      styles.transportText,
                      transport === option.value && styles.transportTextSelected,
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {registrationStatus ? (
            <View style={styles.statusContainer}>
              <Text style={styles.statusText}>{registrationStatus}</Text>
            </View>
          ) : null}

          <TouchableOpacity
            style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
            onPress={handleLogin}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <Text style={styles.loginButtonText}>Login</Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Make sure your SIP server is accessible and credentials are correct
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  form: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FAFAFA',
  },
  transportContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  transportOption: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
    backgroundColor: '#FAFAFA',
  },
  transportOptionSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  transportText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  transportTextSelected: {
    color: '#FFFFFF',
  },
  statusContainer: {
    backgroundColor: '#F0F8FF',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  statusText: {
    fontSize: 14,
    color: '#007AFF',
    textAlign: 'center',
  },
  loginButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  loginButtonDisabled: {
    backgroundColor: '#CCC',
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  footer: {
    marginTop: 30,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default LoginScreen;
