import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import linphone, { CallHistoryItem } from 'react-native-linphone-voip';

interface CallHistoryItemWithId extends CallHistoryItem {
  id: string;
}

const CallHistoryScreen: React.FC = () => {
  const [callHistory, setCallHistory] = useState<CallHistoryItemWithId[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadCallHistory();
  }, []);

  const loadCallHistory = async () => {
    try {
      setIsLoading(true);
      const result = await linphone.getCallHistory();
      
      if (result.status === 'success') {
        // Add unique IDs to each call history item
        const historyWithIds: CallHistoryItemWithId[] = result.history.map((item, index) => ({
          ...item,
          id: `${item.remoteAddress}-${item.startTime}-${index}`,
        }));
        setCallHistory(historyWithIds);
      } else {
        Alert.alert('Error', 'Failed to load call history');
      }
    } catch (error) {
      console.error('Load call history error:', error);
      Alert.alert('Error', 'Failed to load call history');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadCallHistory();
  };

  const handleCallBack = async (remoteAddress: string) => {
    try {
      const result = await linphone.call(remoteAddress);
      
      if (result.status === 'success') {
        Alert.alert('Call', `Calling ${remoteAddress}...`);
      } else {
        Alert.alert('Error', result.message || 'Failed to make call');
      }
    } catch (error) {
      console.error('Call back error:', error);
      Alert.alert('Error', 'Failed to make call');
    }
  };

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const formatDuration = (duration: number) => {
    if (duration === 0) return '';
    
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getCallIcon = (direction: string, status: string) => {
    if (status === 'Missed') {
      return { name: 'call-received', color: '#FF3B30' };
    }
    
    switch (direction) {
      case 'Incoming':
        return { name: 'call-received', color: '#34C759' };
      case 'Outgoing':
        return { name: 'call-made', color: '#007AFF' };
      default:
        return { name: 'call', color: '#8E8E93' };
    }
  };

  const extractDisplayName = (address: string) => {
    // Extract username from SIP URI (sip:<EMAIL> -> username)
    const match = address.match(/sip:([^@]+)@/);
    return match ? match[1] : address;
  };

  const renderCallHistoryItem = ({ item }: { item: CallHistoryItemWithId }) => {
    const callIcon = getCallIcon(item.direction, item.status);
    const displayName = extractDisplayName(item.remoteAddress);
    const duration = formatDuration(item.duration);
    const time = formatTime(item.startTime);

    return (
      <TouchableOpacity
        style={styles.callItem}
        onPress={() => handleCallBack(item.remoteAddress)}
        activeOpacity={0.7}
      >
        <View style={styles.callIconContainer}>
          <Icon name={callIcon.name} size={24} color={callIcon.color} />
        </View>

        <View style={styles.callDetails}>
          <Text style={styles.callerName}>{displayName}</Text>
          <View style={styles.callInfo}>
            <Text style={styles.callTime}>{time}</Text>
            {duration ? (
              <>
                <Text style={styles.separator}>•</Text>
                <Text style={styles.callDuration}>{duration}</Text>
              </>
            ) : null}
          </View>
        </View>

        <TouchableOpacity
          style={styles.callBackButton}
          onPress={() => handleCallBack(item.remoteAddress)}
        >
          <Icon name="call" size={20} color="#007AFF" />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="history" size={64} color="#CCC" />
      <Text style={styles.emptyTitle}>No Call History</Text>
      <Text style={styles.emptySubtitle}>
        Your recent calls will appear here
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={callHistory}
        renderItem={renderCallHistoryItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={callHistory.length === 0 ? styles.emptyList : undefined}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  callItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  callIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  callDetails: {
    flex: 1,
  },
  callerName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  callInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  callTime: {
    fontSize: 14,
    color: '#8E8E93',
  },
  separator: {
    fontSize: 14,
    color: '#8E8E93',
    marginHorizontal: 6,
  },
  callDuration: {
    fontSize: 14,
    color: '#8E8E93',
  },
  callBackButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyList: {
    flex: 1,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default CallHistoryScreen;
