import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import linphone from 'react-native-linphone-voip';

const { width } = Dimensions.get('window');
const BUTTON_SIZE = (width - 80) / 3;

const DialerScreen: React.FC = () => {
  const [phoneNumber, setPhoneNumber] = useState('');

  const dialpadButtons = [
    [
      { number: '1', letters: '' },
      { number: '2', letters: 'ABC' },
      { number: '3', letters: 'DEF' },
    ],
    [
      { number: '4', letters: 'GHI' },
      { number: '5', letters: 'JKL' },
      { number: '6', letters: 'MNO' },
    ],
    [
      { number: '7', letters: 'PQRS' },
      { number: '8', letters: 'TUV' },
      { number: '9', letters: 'WXYZ' },
    ],
    [
      { number: '*', letters: '' },
      { number: '0', letters: '+' },
      { number: '#', letters: '' },
    ],
  ];

  const handleNumberPress = (number: string) => {
    setPhoneNumber(prev => prev + number);
  };

  const handleBackspace = () => {
    setPhoneNumber(prev => prev.slice(0, -1));
  };

  const handleCall = async () => {
    if (!phoneNumber.trim()) {
      Alert.alert('Error', 'Please enter a phone number');
      return;
    }

    try {
      // Format as SIP URI if it's not already
      let sipUri = phoneNumber.trim();
      if (!sipUri.startsWith('sip:')) {
        // You might want to get the domain from settings or user preferences
        sipUri = `sip:${sipUri}@sip.example.com`;
      }

      const result = await linphone.call(sipUri);
      console.log('Call result:', result);
      
      if (result.status === 'success') {
        Alert.alert('Call', `Calling ${phoneNumber}...`);
        // Clear the number after successful call initiation
        setPhoneNumber('');
      } else {
        Alert.alert('Error', result.message || 'Failed to make call');
      }
    } catch (error) {
      console.error('Call error:', error);
      Alert.alert('Error', 'Failed to make call');
    }
  };

  const renderDialpadButton = (button: { number: string; letters: string }) => (
    <TouchableOpacity
      key={button.number}
      style={styles.dialpadButton}
      onPress={() => handleNumberPress(button.number)}
      activeOpacity={0.7}
    >
      <Text style={styles.dialpadNumber}>{button.number}</Text>
      {button.letters ? (
        <Text style={styles.dialpadLetters}>{button.letters}</Text>
      ) : null}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.displayContainer}>
        <Text style={styles.phoneNumberDisplay}>
          {phoneNumber || 'Enter number'}
        </Text>
      </View>

      <View style={styles.dialpadContainer}>
        {dialpadButtons.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.dialpadRow}>
            {row.map(renderDialpadButton)}
          </View>
        ))}
      </View>

      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity
          style={styles.backspaceButton}
          onPress={handleBackspace}
          disabled={!phoneNumber}
        >
          <Icon 
            name="backspace" 
            size={24} 
            color={phoneNumber ? '#333' : '#CCC'} 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.callButton,
            !phoneNumber && styles.callButtonDisabled
          ]}
          onPress={handleCall}
          disabled={!phoneNumber}
        >
          <Icon name="call" size={28} color="#FFFFFF" />
        </TouchableOpacity>

        <View style={styles.spacer} />
      </View>

      <View style={styles.quickActionsContainer}>
        <TouchableOpacity style={styles.quickActionButton}>
          <Icon name="voicemail" size={24} color="#007AFF" />
          <Text style={styles.quickActionText}>Voicemail</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.quickActionButton}>
          <Icon name="contacts" size={24} color="#007AFF" />
          <Text style={styles.quickActionText}>Contacts</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.quickActionButton}>
          <Icon name="history" size={24} color="#007AFF" />
          <Text style={styles.quickActionText}>Recents</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  displayContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    minHeight: 80,
  },
  phoneNumberDisplay: {
    fontSize: 32,
    fontWeight: '300',
    color: '#333',
    textAlign: 'center',
    letterSpacing: 2,
  },
  dialpadContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  dialpadRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  dialpadButton: {
    width: BUTTON_SIZE,
    height: BUTTON_SIZE,
    borderRadius: BUTTON_SIZE / 2,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dialpadNumber: {
    fontSize: 28,
    fontWeight: '400',
    color: '#333',
  },
  dialpadLetters: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
    marginTop: 2,
    letterSpacing: 1,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 20,
  },
  backspaceButton: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  callButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#34C759',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  callButtonDisabled: {
    backgroundColor: '#CCC',
  },
  spacer: {
    width: 50,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  quickActionButton: {
    alignItems: 'center',
    padding: 10,
  },
  quickActionText: {
    fontSize: 12,
    color: '#007AFF',
    marginTop: 4,
    fontWeight: '500',
  },
});

export default DialerScreen;
