import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  ScrollView,
  Switch,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import linphone from 'react-native-linphone-voip';

interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  type: 'toggle' | 'action' | 'info';
  value?: boolean;
  onPress?: () => void;
  onToggle?: (value: boolean) => void;
  icon: string;
  iconColor?: string;
}

const SettingsScreen: React.FC = () => {
  const [pushNotifications, setPushNotifications] = useState(true);
  const [autoAnswer, setAutoAnswer] = useState(false);
  const [speakerOnCall, setSpeakerOnCall] = useState(false);
  const [vibrationEnabled, setVibrationEnabled] = useState(true);

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout from your SIP account?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await linphone.unregister();
              console.log('Logout result:', result);
              Alert.alert('Success', 'Logged out successfully');
              // In a real app, navigate back to login screen
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'Failed to logout');
            }
          },
        },
      ]
    );
  };

  const handleClearCallHistory = () => {
    Alert.alert(
      'Clear Call History',
      'Are you sure you want to clear all call history?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            // In a real app, clear call history from storage
            Alert.alert('Success', 'Call history cleared');
          },
        },
      ]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'About',
      'Linphone SIP Client\nVersion 1.0.0\n\nBuilt with React Native and Linphone SDK',
      [{ text: 'OK' }]
    );
  };

  const settingSections = [
    {
      title: 'Account',
      items: [
        {
          id: 'account-info',
          title: 'Account Information',
          subtitle: 'View your SIP account details',
          type: 'action' as const,
          icon: 'account-circle',
          iconColor: '#007AFF',
          onPress: () => {
            Alert.alert('Account Info', 'Feature coming soon...');
          },
        },
        {
          id: 'logout',
          title: 'Logout',
          subtitle: 'Sign out from your SIP account',
          type: 'action' as const,
          icon: 'logout',
          iconColor: '#FF3B30',
          onPress: handleLogout,
        },
      ],
    },
    {
      title: 'Call Settings',
      items: [
        {
          id: 'auto-answer',
          title: 'Auto Answer',
          subtitle: 'Automatically answer incoming calls',
          type: 'toggle' as const,
          value: autoAnswer,
          icon: 'call-received',
          iconColor: '#34C759',
          onToggle: setAutoAnswer,
        },
        {
          id: 'speaker-on-call',
          title: 'Speaker on Call',
          subtitle: 'Enable speaker by default during calls',
          type: 'toggle' as const,
          value: speakerOnCall,
          icon: 'volume-up',
          iconColor: '#FF9500',
          onToggle: setSpeakerOnCall,
        },
        {
          id: 'vibration',
          title: 'Vibration',
          subtitle: 'Vibrate for incoming calls',
          type: 'toggle' as const,
          value: vibrationEnabled,
          icon: 'vibration',
          iconColor: '#8E8E93',
          onToggle: setVibrationEnabled,
        },
      ],
    },
    {
      title: 'Notifications',
      items: [
        {
          id: 'push-notifications',
          title: 'Push Notifications',
          subtitle: 'Receive notifications for calls and messages',
          type: 'toggle' as const,
          value: pushNotifications,
          icon: 'notifications',
          iconColor: '#FF3B30',
          onToggle: setPushNotifications,
        },
      ],
    },
    {
      title: 'Data & Privacy',
      items: [
        {
          id: 'clear-history',
          title: 'Clear Call History',
          subtitle: 'Remove all call history records',
          type: 'action' as const,
          icon: 'delete-sweep',
          iconColor: '#FF3B30',
          onPress: handleClearCallHistory,
        },
        {
          id: 'clear-messages',
          title: 'Clear Messages',
          subtitle: 'Remove all message history',
          type: 'action' as const,
          icon: 'delete-outline',
          iconColor: '#FF3B30',
          onPress: () => {
            Alert.alert('Clear Messages', 'Feature coming soon...');
          },
        },
      ],
    },
    {
      title: 'Advanced',
      items: [
        {
          id: 'network-settings',
          title: 'Network Settings',
          subtitle: 'Configure SIP transport and codecs',
          type: 'action' as const,
          icon: 'settings-ethernet',
          iconColor: '#8E8E93',
          onPress: () => {
            Alert.alert('Network Settings', 'Feature coming soon...');
          },
        },
        {
          id: 'audio-settings',
          title: 'Audio Settings',
          subtitle: 'Configure audio codecs and quality',
          type: 'action' as const,
          icon: 'audiotrack',
          iconColor: '#8E8E93',
          onPress: () => {
            Alert.alert('Audio Settings', 'Feature coming soon...');
          },
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          title: 'Help & Support',
          subtitle: 'Get help and contact support',
          type: 'action' as const,
          icon: 'help',
          iconColor: '#007AFF',
          onPress: () => {
            Alert.alert('Help & Support', 'Feature coming soon...');
          },
        },
        {
          id: 'about',
          title: 'About',
          subtitle: 'App version and information',
          type: 'action' as const,
          icon: 'info',
          iconColor: '#8E8E93',
          onPress: handleAbout,
        },
      ],
    },
  ];

  const renderSettingItem = (item: SettingItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.settingItem}
      onPress={item.onPress}
      disabled={item.type === 'toggle'}
      activeOpacity={item.type === 'action' ? 0.7 : 1}
    >
      <View style={styles.settingIcon}>
        <Icon name={item.icon} size={24} color={item.iconColor || '#8E8E93'} />
      </View>

      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{item.title}</Text>
        {item.subtitle && (
          <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
        )}
      </View>

      {item.type === 'toggle' && (
        <Switch
          value={item.value}
          onValueChange={item.onToggle}
          trackColor={{ false: '#E5E5EA', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      )}

      {item.type === 'action' && (
        <Icon name="chevron-right" size={24} color="#C7C7CC" />
      )}
    </TouchableOpacity>
  );

  const renderSection = (section: { title: string; items: SettingItem[] }) => (
    <View key={section.title} style={styles.section}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      <View style={styles.sectionContent}>
        {section.items.map(renderSettingItem)}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {settingSections.map(renderSection)}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#8E8E93',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 8,
    marginHorizontal: 16,
  },
  sectionContent: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#E5E5EA',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 18,
  },
});

export default SettingsScreen;
