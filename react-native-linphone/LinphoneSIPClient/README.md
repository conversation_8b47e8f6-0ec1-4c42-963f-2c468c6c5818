# 📞 Linphone SIP Client - React Native App

A full-featured SIP client built with React Native and the `react-native-linphone-voip` library.

## 🎯 Features

### ✅ Completed Features

- **SIP Registration**: Login with username, password, domain, and transport selection
- **Dialer**: Full numeric keypad with SIP calling functionality
- **Call History**: View and manage call history with callback functionality
- **Messages**: Real-time messaging with chat rooms and conversation management
- **Contacts**: Add, manage, and interact with SIP contacts
- **Settings**: Comprehensive settings for call preferences, notifications, and account management
- **Tab Navigation**: Clean bottom tab navigation between all features
- **Event Handling**: Real-time SIP events for calls, messages, and registration

### 🔧 Technical Features

- **React Native 0.70.5**: Latest stable version
- **TypeScript Support**: Full type safety and IntelliSense
- **React Navigation**: Modern navigation with bottom tabs
- **Vector Icons**: Material Design icons throughout the app
- **Gesture Handler**: Smooth touch interactions
- **Event-Driven Architecture**: Real-time SIP event handling

## 📱 Screenshots

### Login Screen
- Clean, modern login interface
- SIP account configuration (username, password, domain)
- Transport selection (UDP, TCP, TLS)
- Real-time registration status

### Main App Screens
1. **Dialer**: Numeric keypad with call functionality
2. **History**: Call history with callback options
3. **Messages**: Chat interface with real-time messaging
4. **Contacts**: Contact management with quick actions
5. **Settings**: Comprehensive app and SIP settings

## 🚀 Getting Started

### Prerequisites

- Node.js >= 16
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development)
- SIP Server (Asterisk, FreePBX, Kamailio, etc.)

### Installation

1. **Clone and install dependencies**:
```bash
cd LinphoneSIPClient
yarn install
```

2. **iOS Setup** (if targeting iOS):
```bash
cd ios
pod install
cd ..
```

3. **Android Setup**:
   - Ensure Android SDK is installed
   - Connect Android device or start emulator

### Running the App

#### Android
```bash
npx react-native run-android
```

#### iOS
```bash
npx react-native run-ios
```

## 🔧 Configuration

### SIP Server Setup

You'll need a SIP server to test the app. Popular options:

1. **Asterisk**: Open-source PBX
2. **FreePBX**: Web-based Asterisk management
3. **Kamailio**: High-performance SIP server
4. **3CX**: Commercial PBX solution

### Test Accounts

Create test SIP accounts on your server:
- Username: `1001`, `1002`, `1003`, etc.
- Password: Set secure passwords
- Domain: Your SIP server IP or domain

### App Configuration

Update the default domain in the following files if needed:
- `src/screens/DialerScreen.tsx` (line 45)
- `src/screens/ContactsScreen.tsx` (line 67)
- `src/screens/MessagesScreen.tsx` (line 156)

## 📚 Usage Guide

### 1. Login
1. Open the app
2. Enter your SIP credentials:
   - Username: Your SIP username
   - Password: Your SIP password  
   - Domain: Your SIP server domain/IP
   - Transport: Choose UDP, TCP, or TLS
3. Tap "Login"

### 2. Making Calls
1. Go to "Dialer" tab
2. Enter phone number or SIP extension
3. Tap the green call button
4. Use call controls (mute, speaker, hangup)

### 3. Messaging
1. Go to "Messages" tab
2. Tap "+" to start new conversation
3. Enter SIP address of recipient
4. Send and receive real-time messages

### 4. Managing Contacts
1. Go to "Contacts" tab
2. Tap "+" to add new contact
3. Enter name and SIP address
4. Use quick actions to call or message

### 5. Settings
1. Go to "Settings" tab
2. Configure call preferences
3. Manage notifications
4. View account information
5. Logout when needed

## 🔧 Development

### Project Structure

```
src/
├── screens/           # Main app screens
│   ├── LoginScreen.tsx
│   ├── DialerScreen.tsx
│   ├── CallHistoryScreen.tsx
│   ├── MessagesScreen.tsx
│   ├── ContactsScreen.tsx
│   └── SettingsScreen.tsx
├── navigation/        # Navigation configuration
│   └── MainTabNavigator.tsx
├── components/        # Reusable components
├── utils/            # Utility functions
└── types/            # TypeScript type definitions
```

### Key Dependencies

- `react-native-linphone-voip`: SIP functionality
- `@react-navigation/native`: Navigation
- `@react-navigation/bottom-tabs`: Tab navigation
- `react-native-vector-icons`: Icons
- `react-native-gesture-handler`: Touch handling
- `react-native-safe-area-context`: Safe area support

### Adding Features

1. **New Screen**: Create in `src/screens/`
2. **Navigation**: Update `MainTabNavigator.tsx`
3. **SIP Features**: Use `react-native-linphone-voip` API
4. **Styling**: Follow existing design patterns

## 🐛 Troubleshooting

### Common Issues

1. **Metro bundler issues**:
```bash
npx react-native start --reset-cache
```

2. **Android build issues**:
```bash
cd android
./gradlew clean
cd ..
npx react-native run-android
```

3. **iOS build issues**:
```bash
cd ios
pod install
cd ..
npx react-native run-ios
```

4. **SIP Registration fails**:
   - Check server connectivity
   - Verify credentials
   - Try different transport (UDP/TCP/TLS)
   - Check firewall settings

### Debugging

- Use React Native Debugger
- Check Metro logs for JavaScript errors
- Use `adb logcat` for Android native logs
- Use Xcode console for iOS native logs

## 📄 License

This project is for educational and development purposes. Please ensure you have proper licensing for production use of Linphone SDK.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📞 Support

For issues related to:
- **App functionality**: Check this README and troubleshooting section
- **SIP server setup**: Consult your SIP server documentation
- **Linphone SDK**: Visit [Linphone documentation](https://www.linphone.org/technical-corner/linphone)

---

**Built with ❤️ using React Native and Linphone SDK**
