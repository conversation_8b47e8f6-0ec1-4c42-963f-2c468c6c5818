import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';

const DemoApp = () => {
  const [currentScreen, setCurrentScreen] = useState('login');

  const showAlert = (title: string, message: string) => {
    Alert.alert(title, message);
  };

  const LoginDemo = () => (
    <View style={styles.screen}>
      <Text style={styles.title}>SIP Login Demo</Text>
      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Username</Text>
          <View style={styles.input}>
            <Text style={styles.inputText}>demo_user</Text>
          </View>
        </View>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Password</Text>
          <View style={styles.input}>
            <Text style={styles.inputText}>••••••••</Text>
          </View>
        </View>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Domain</Text>
          <View style={styles.input}>
            <Text style={styles.inputText}>sip.example.com</Text>
          </View>
        </View>
        <TouchableOpacity 
          style={styles.button}
          onPress={() => {
            showAlert('Success', 'Login successful!');
            setCurrentScreen('main');
          }}
        >
          <Text style={styles.buttonText}>Login</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const MainDemo = () => (
    <View style={styles.screen}>
      <Text style={styles.title}>SIP Client Demo</Text>
      <ScrollView style={styles.demoContainer}>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📞 Dialer</Text>
          <TouchableOpacity 
            style={styles.demoButton}
            onPress={() => showAlert('Dialer', 'Numeric keypad for making calls')}
          >
            <Text style={styles.demoButtonText}>Open Dialer</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📋 Call History</Text>
          <TouchableOpacity 
            style={styles.demoButton}
            onPress={() => showAlert('Call History', 'View recent calls:\n• Outgoing to 1002 (2 min)\n• Missed from 1003')}
          >
            <Text style={styles.demoButtonText}>View History</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💬 Messages</Text>
          <TouchableOpacity 
            style={styles.demoButton}
            onPress={() => showAlert('Messages', 'Real-time SIP messaging with chat rooms')}
          >
            <Text style={styles.demoButtonText}>Open Messages</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👥 Contacts</Text>
          <TouchableOpacity 
            style={styles.demoButton}
            onPress={() => showAlert('Contacts', 'Manage SIP contacts:\n• John Doe (1001)\n• Jane Smith (1002)\n• Bob Johnson (1003)')}
          >
            <Text style={styles.demoButtonText}>View Contacts</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>⚙️ Settings</Text>
          <TouchableOpacity 
            style={styles.demoButton}
            onPress={() => showAlert('Settings', 'Configure:\n• Call preferences\n• Audio settings\n• Account management')}
          >
            <Text style={styles.demoButtonText}>Open Settings</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎥 Conference</Text>
          <TouchableOpacity 
            style={styles.demoButton}
            onPress={() => showAlert('Conference', 'Multi-party calling with participant management')}
          >
            <Text style={styles.demoButtonText}>Start Conference</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity 
          style={[styles.button, styles.logoutButton]}
          onPress={() => {
            showAlert('Logout', 'Logged out successfully');
            setCurrentScreen('login');
          }}
        >
          <Text style={styles.buttonText}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {currentScreen === 'login' ? <LoginDemo /> : <MainDemo />}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  screen: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 30,
  },
  form: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#FAFAFA',
  },
  inputText: {
    fontSize: 16,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  demoContainer: {
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  demoButton: {
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  demoButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
  },
  logoutButton: {
    backgroundColor: '#FF3B30',
    marginTop: 20,
    marginBottom: 40,
  },
});

export default DemoApp;
