<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Native SIP Client Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #F8F8F8;
            color: #333;
        }

        .phone-container {
            max-width: 375px;
            margin: 20px auto;
            background: #000;
            border-radius: 25px;
            padding: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .screen {
            background: #F8F8F8;
            border-radius: 20px;
            height: 667px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            background: #007AFF;
            color: white;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
        }

        .login-screen, .main-screen {
            padding: 20px;
            height: calc(100% - 40px);
            overflow-y: auto;
        }

        .main-screen {
            display: none;
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .form {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .label {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .input {
            width: 100%;
            border: 1px solid #DDD;
            border-radius: 8px;
            padding: 12px;
            font-size: 16px;
            background: #FAFAFA;
        }

        .transport-container {
            display: flex;
            gap: 8px;
        }

        .transport-option {
            flex: 1;
            padding: 12px;
            border: 1px solid #DDD;
            border-radius: 8px;
            text-align: center;
            background: #FAFAFA;
            cursor: pointer;
            transition: all 0.2s;
        }

        .transport-option.selected {
            background: #007AFF;
            color: white;
            border-color: #007AFF;
        }

        .button {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: background 0.2s;
        }

        .button:hover {
            background: #0056CC;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .demo-button {
            background: #F2F2F7;
            color: #007AFF;
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            cursor: pointer;
            transition: background 0.2s;
        }

        .demo-button:hover {
            background: #E5E5EA;
        }

        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #E5E5EA;
            display: flex;
            height: 60px;
            display: none;
        }

        .tab-bar.show {
            display: flex;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #8E8E93;
            font-size: 12px;
            font-weight: 500;
        }

        .tab-item.active {
            color: #007AFF;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        .logout-button {
            background: #FF3B30;
            margin-top: 20px;
        }

        .logout-button:hover {
            background: #D70015;
        }

        .hidden {
            display: none;
        }

        .dialer-screen {
            text-align: center;
            padding: 20px;
        }

        .phone-display {
            font-size: 32px;
            font-weight: 300;
            margin: 40px 0;
            min-height: 50px;
            color: #333;
        }

        .dialpad {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .dialpad-button {
            width: 70px;
            height: 70px;
            border-radius: 35px;
            background: white;
            border: none;
            font-size: 24px;
            font-weight: 400;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.1s;
            margin: 0 auto;
        }

        .dialpad-button:active {
            transform: scale(0.95);
        }

        .call-button {
            width: 70px;
            height: 70px;
            border-radius: 35px;
            background: #34C759;
            border: none;
            color: white;
            font-size: 28px;
            cursor: pointer;
            margin: 20px auto;
            display: block;
            box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
        }

        .call-button:disabled {
            background: #CCC;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <div class="status-bar">React Native SIP Client</div>
            
            <!-- Login Screen -->
            <div class="login-screen" id="loginScreen">
                <h1 class="title">SIP Login</h1>
                <div class="form">
                    <div class="input-group">
                        <label class="label">Username *</label>
                        <input type="text" class="input" id="username" placeholder="Enter username" value="demo_user">
                    </div>
                    <div class="input-group">
                        <label class="label">Password *</label>
                        <input type="password" class="input" id="password" placeholder="Enter password" value="••••••••">
                    </div>
                    <div class="input-group">
                        <label class="label">Domain *</label>
                        <input type="text" class="input" id="domain" placeholder="sip.example.com" value="sip.example.com">
                    </div>
                    <div class="input-group">
                        <label class="label">Transport</label>
                        <div class="transport-container">
                            <div class="transport-option selected" data-transport="UDP">UDP</div>
                            <div class="transport-option" data-transport="TCP">TCP</div>
                            <div class="transport-option" data-transport="TLS">TLS</div>
                        </div>
                    </div>
                    <button class="button" onclick="login()">Login</button>
                </div>
            </div>

            <!-- Main Screen -->
            <div class="main-screen" id="mainScreen">
                <!-- Dialer Tab -->
                <div class="tab-content" id="dialerTab">
                    <div class="dialer-screen">
                        <div class="phone-display" id="phoneDisplay">Enter number</div>
                        <div class="dialpad">
                            <button class="dialpad-button" onclick="addDigit('1')">1</button>
                            <button class="dialpad-button" onclick="addDigit('2')">2<br><small>ABC</small></button>
                            <button class="dialpad-button" onclick="addDigit('3')">3<br><small>DEF</small></button>
                            <button class="dialpad-button" onclick="addDigit('4')">4<br><small>GHI</small></button>
                            <button class="dialpad-button" onclick="addDigit('5')">5<br><small>JKL</small></button>
                            <button class="dialpad-button" onclick="addDigit('6')">6<br><small>MNO</small></button>
                            <button class="dialpad-button" onclick="addDigit('7')">7<br><small>PQRS</small></button>
                            <button class="dialpad-button" onclick="addDigit('8')">8<br><small>TUV</small></button>
                            <button class="dialpad-button" onclick="addDigit('9')">9<br><small>WXYZ</small></button>
                            <button class="dialpad-button" onclick="addDigit('*')">*</button>
                            <button class="dialpad-button" onclick="addDigit('0')">0<br><small>+</small></button>
                            <button class="dialpad-button" onclick="addDigit('#')">#</button>
                        </div>
                        <button class="call-button" id="callButton" onclick="makeCall()" disabled>📞</button>
                    </div>
                </div>

                <!-- Other Tabs -->
                <div class="tab-content hidden" id="historyTab">
                    <h2 class="title">Call History</h2>
                    <div class="section">
                        <div class="section-title">📞 Recent Calls</div>
                        <div style="margin-bottom: 10px;">
                            <strong>John Doe (1001)</strong><br>
                            <small>Outgoing • 2 min • 10:30 AM</small>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>Jane Smith (1002)</strong><br>
                            <small>Missed • 1:45 PM</small>
                        </div>
                        <div>
                            <strong>Bob Johnson (1003)</strong><br>
                            <small>Incoming • 5 min • Yesterday</small>
                        </div>
                    </div>
                </div>

                <div class="tab-content hidden" id="messagesTab">
                    <h2 class="title">Messages</h2>
                    <div class="section">
                        <div class="section-title">💬 Recent Chats</div>
                        <div style="margin-bottom: 10px;">
                            <strong>John Doe</strong><br>
                            <small>Hey, are you available for a call?</small>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>Jane Smith</strong><br>
                            <small>Thanks for the meeting today!</small>
                        </div>
                    </div>
                </div>

                <div class="tab-content hidden" id="contactsTab">
                    <h2 class="title">Contacts</h2>
                    <div class="section">
                        <div class="section-title">👥 SIP Contacts</div>
                        <div style="margin-bottom: 10px;">
                            <strong>John Doe</strong><br>
                            <small>sip:<EMAIL> (1001)</small>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>Jane Smith</strong><br>
                            <small>sip:<EMAIL> (1002)</small>
                        </div>
                        <div>
                            <strong>Bob Johnson</strong><br>
                            <small>sip:<EMAIL> (1003)</small>
                        </div>
                    </div>
                </div>

                <div class="tab-content hidden" id="settingsTab">
                    <h2 class="title">Settings</h2>
                    <div class="section">
                        <div class="section-title">⚙️ Call Settings</div>
                        <button class="demo-button" onclick="showAlert('Auto Answer', 'Configure automatic call answering')">Auto Answer</button>
                    </div>
                    <div class="section">
                        <div class="section-title">🔊 Audio Settings</div>
                        <button class="demo-button" onclick="showAlert('Audio Codecs', 'Configure audio quality and codecs')">Audio Codecs</button>
                    </div>
                    <div class="section">
                        <div class="section-title">👤 Account</div>
                        <button class="demo-button" onclick="showAlert('Account Info', 'Username: demo_user\\nDomain: sip.example.com\\nTransport: UDP')">Account Information</button>
                    </div>
                    <button class="button logout-button" onclick="logout()">Logout</button>
                </div>
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar" id="tabBar">
                <div class="tab-item active" onclick="switchTab('dialer')">
                    <div class="tab-icon">📞</div>
                    <div>Dialer</div>
                </div>
                <div class="tab-item" onclick="switchTab('history')">
                    <div class="tab-icon">📋</div>
                    <div>History</div>
                </div>
                <div class="tab-item" onclick="switchTab('messages')">
                    <div class="tab-icon">💬</div>
                    <div>Messages</div>
                </div>
                <div class="tab-item" onclick="switchTab('contacts')">
                    <div class="tab-icon">👥</div>
                    <div>Contacts</div>
                </div>
                <div class="tab-item" onclick="switchTab('settings')">
                    <div class="tab-icon">⚙️</div>
                    <div>Settings</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentNumber = '';

        // Transport selection
        document.querySelectorAll('.transport-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.transport-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        function login() {
            showAlert('Success', 'SIP registration successful!');
            setTimeout(() => {
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('mainScreen').style.display = 'block';
                document.getElementById('tabBar').classList.add('show');
            }, 1000);
        }

        function logout() {
            showAlert('Logout', 'Logged out successfully');
            setTimeout(() => {
                document.getElementById('loginScreen').style.display = 'block';
                document.getElementById('mainScreen').style.display = 'none';
                document.getElementById('tabBar').classList.remove('show');
                currentNumber = '';
                updateDisplay();
            }, 1000);
        }

        function addDigit(digit) {
            currentNumber += digit;
            updateDisplay();
        }

        function updateDisplay() {
            const display = document.getElementById('phoneDisplay');
            const callButton = document.getElementById('callButton');
            
            if (currentNumber) {
                display.textContent = currentNumber;
                callButton.disabled = false;
            } else {
                display.textContent = 'Enter number';
                callButton.disabled = true;
            }
        }

        function makeCall() {
            if (currentNumber) {
                showAlert('Calling', `Calling ${currentNumber}...`);
                currentNumber = '';
                updateDisplay();
            }
        }

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.remove('hidden');
            
            // Add active class to selected tab
            event.target.closest('.tab-item').classList.add('active');
        }

        function showAlert(title, message) {
            alert(title + '\n\n' + message);
        }

        // Initialize display
        updateDisplay();
    </script>
</body>
</html>
