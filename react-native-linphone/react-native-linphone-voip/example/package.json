{"name": "react-native-linphone-voip-example", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "expo": "~53.0.16", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-web": "~0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "react-native-builder-bob": "^0.40.8", "react-native-monorepo-config": "^0.1.9"}, "private": true}