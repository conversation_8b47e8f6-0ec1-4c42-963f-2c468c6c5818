name: Setup
description: Setup Node.js and install dependencies

runs:
  using: composite
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version-file: .nvmrc

    - name: Restore dependencies
      id: yarn-cache
      uses: actions/cache/restore@v4
      with:
        path: |
          **/node_modules
          .yarn/install-state.gz
        key: ${{ runner.os }}-yarn-${{ hashFiles('yarn.lock') }}-${{ hashFiles('**/package.json', '!node_modules/**') }}
        restore-keys: |
          ${{ runner.os }}-yarn-${{ hashFiles('yarn.lock') }}
          ${{ runner.os }}-yarn-

    - name: Install dependencies
      if: steps.yarn-cache.outputs.cache-hit != 'true'
      run: yarn install --immutable
      shell: bash

    - name: Cache dependencies
      if: steps.yarn-cache.outputs.cache-hit != 'true'
      uses: actions/cache/save@v4
      with:
        path: |
          **/node_modules
          .yarn/install-state.gz
        key: ${{ steps.yarn-cache.outputs.cache-primary-key }}
