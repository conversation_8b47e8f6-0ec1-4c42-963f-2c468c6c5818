name: CI
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  merge_group:
    types:
      - checks_requested

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Lint files
        run: yarn lint

      - name: Typecheck files
        run: yarn typecheck

  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run unit tests
        run: yarn test --maxWorkers=2 --coverage

  build-library:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Build package
        run: yarn prepare

  build-web:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Build example for Web
        run: |
          yarn example expo export --platform web
