import linphone, { LinphoneVoip, SipConfig } from '../index';

// Mock the native module
jest.mock('react-native', () => ({
  NativeModules: {
    LinphoneModule: {
      register: jest.fn(() => Promise.resolve({ status: 'success', message: 'Registration initiated' })),
      unregister: jest.fn(() => Promise.resolve({ status: 'success', message: 'Unregistration completed' })),
      call: jest.fn(() => Promise.resolve({ status: 'success', message: 'Call initiated' })),
      hangup: jest.fn(() => Promise.resolve({ status: 'success', message: 'Call terminated' })),
      accept: jest.fn(() => Promise.resolve({ status: 'success', message: 'Call accepted' })),
      decline: jest.fn(() => Promise.resolve({ status: 'success', message: 'Call declined' })),
      mute: jest.fn(() => Promise.resolve({ status: 'success', muted: true })),
      toggleSpeaker: jest.fn(() => Promise.resolve({ status: 'success', speakerEnabled: true })),
      sendMessage: jest.fn(() => Promise.resolve({ status: 'success', message: 'Message sent' })),
      createChatRoom: jest.fn(() => Promise.resolve({ status: 'success', message: 'Chat room created' })),
      getCallHistory: jest.fn(() => Promise.resolve({ status: 'success', history: [] })),
      startConference: jest.fn(() => Promise.resolve({ status: 'success', message: 'Conference started' })),
      inviteToConference: jest.fn(() => Promise.resolve({ status: 'success', message: 'Participant invited' })),
    },
  },
  NativeEventEmitter: jest.fn(() => ({
    addListener: jest.fn(() => ({ remove: jest.fn() })),
    removeListener: jest.fn(),
    removeAllListeners: jest.fn(),
  })),
  Platform: {
    select: jest.fn(() => ''),
  },
}));

describe('LinphoneVoip', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should export default instance', () => {
    expect(linphone).toBeInstanceOf(LinphoneVoip);
  });

  it('should register SIP account', async () => {
    const config: SipConfig = {
      username: '1000 0',
      password: 'hoangcv',
      domain: '**************:5060',
      transport: 'UDP',
    };

    const result = await linphone.register(config);
    expect(result.status).toBe('success');
    expect(result.message).toBe('Registration initiated');
  });

  it('should unregister SIP account', async () => {
    const result = await linphone.unregister();
    expect(result.status).toBe('success');
    expect(result.message).toBe('Unregistration completed');
  });

  it('should make a call', async () => {
    const uri = 'sip:1002@**************:5060';
    const result = await linphone.call(uri);
    expect(result.status).toBe('success');
    expect(result.message).toBe('Call initiated');
  });

  it('should hangup call', async () => {
    const result = await linphone.hangup();
    expect(result.status).toBe('success');
    expect(result.message).toBe('Call terminated');
  });

  it('should accept incoming call', async () => {
    const result = await linphone.accept();
    expect(result.status).toBe('success');
    expect(result.message).toBe('Call accepted');
  });

  it('should decline incoming call', async () => {
    const result = await linphone.decline();
    expect(result.status).toBe('success');
    expect(result.message).toBe('Call declined');
  });

  it('should toggle mute', async () => {
    const result = await linphone.mute();
    expect(result.status).toBe('success');
    expect(result.muted).toBe(true);
  });

  it('should toggle speaker', async () => {
    const result = await linphone.toggleSpeaker();
    expect(result.status).toBe('success');
    expect(result.speakerEnabled).toBe(true);
  });

  it('should send message', async () => {
    const to = 'sip:<EMAIL>';
    const content = 'Hello, World!';
    const result = await linphone.sendMessage(to, content);
    expect(result.status).toBe('success');
    expect(result.message).toBe('Message sent');
  });

  it('should create chat room', async () => {
    const uri = 'sip:<EMAIL>';
    const result = await linphone.createChatRoom(uri);
    expect(result.status).toBe('success');
    expect(result.message).toBe('Chat room created');
  });

  it('should get call history', async () => {
    const result = await linphone.getCallHistory();
    expect(result.status).toBe('success');
    expect(Array.isArray(result.history)).toBe(true);
  });

  it('should start conference', async () => {
    const result = await linphone.startConference();
    expect(result.status).toBe('success');
    expect(result.message).toBe('Conference started');
  });

  it('should invite to conference', async () => {
    const participant = 'sip:<EMAIL>';
    const result = await linphone.inviteToConference(participant);
    expect(result.status).toBe('success');
    expect(result.message).toBe('Participant invited');
  });

  it('should handle event listeners', () => {
    const callback = jest.fn();
    const unsubscribe = linphone.onRegistrationStateChanged(callback);

    expect(typeof unsubscribe).toBe('function');
    unsubscribe();
  });
});
