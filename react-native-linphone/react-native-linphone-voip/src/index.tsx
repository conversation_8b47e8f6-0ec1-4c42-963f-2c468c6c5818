import { NativeModules, NativeEventEmitter, Platform } from 'react-native';

const LINKING_ERROR =
  `The package 'react-native-linphone-voip' doesn't seem to be linked. Make sure: \n\n` +
  Platform.select({ ios: "- You have run 'pod install'\n", default: '' }) +
  '- You rebuilt the app after installing the package\n' +
  '- You are not using Expo Go\n';

const LinphoneModule = NativeModules.LinphoneModule
  ? NativeModules.LinphoneModule
  : new Proxy(
      {},
      {
        get() {
          throw new Error(LINKING_ERROR);
        },
      }
    );

const eventEmitter = new NativeEventEmitter(LinphoneModule);

// Types
export interface SipConfig {
  username: string;
  password: string;
  domain: string;
  transport?: 'UDP' | 'TCP' | 'TLS';
}

export interface CallHistoryItem {
  remoteAddress: string;
  direction: 'Incoming' | 'Outgoing';
  startTime: number;
  duration: number;
  status: string;
}

export interface LinphoneResponse {
  status: 'success' | 'error';
  message: string;
  [key: string]: any;
}

// Event types
export type LinphoneEventType =
  | 'RegistrationStateChanged'
  | 'CallStateChanged'
  | 'MessageReceived'
  | 'CallIncoming'
  | 'CallConnected'
  | 'CallEnded'
  | 'ConferenceJoined';

export interface RegistrationEvent {
  state: string;
  message: string;
}

export interface CallEvent {
  callId: string;
  state: string;
  message: string;
  remoteAddress: string;
}

export interface MessageEvent {
  from: string;
  to: string;
  content: string;
  timestamp: number;
}

// Main Linphone class
class LinphoneVoip {
  private eventEmitter: NativeEventEmitter;

  constructor() {
    this.eventEmitter = eventEmitter;
  }

  // SIP Registration
  async register(config: SipConfig): Promise<LinphoneResponse> {
    return LinphoneModule.register(config);
  }

  async unregister(): Promise<LinphoneResponse> {
    return LinphoneModule.unregister();
  }

  // Call Management
  async call(uri: string): Promise<LinphoneResponse> {
    return LinphoneModule.call(uri);
  }

  async hangup(): Promise<LinphoneResponse> {
    return LinphoneModule.hangup();
  }

  async accept(): Promise<LinphoneResponse> {
    return LinphoneModule.accept();
  }

  async decline(): Promise<LinphoneResponse> {
    return LinphoneModule.decline();
  }

  async mute(): Promise<LinphoneResponse> {
    return LinphoneModule.mute();
  }

  async toggleSpeaker(): Promise<LinphoneResponse> {
    return LinphoneModule.toggleSpeaker();
  }

  // Messaging
  async sendMessage(to: string, content: string): Promise<LinphoneResponse> {
    return LinphoneModule.sendMessage(to, content);
  }

  async createChatRoom(uri: string): Promise<LinphoneResponse> {
    return LinphoneModule.createChatRoom(uri);
  }

  // Call History
  async getCallHistory(): Promise<LinphoneResponse & { history: CallHistoryItem[] }> {
    return LinphoneModule.getCallHistory();
  }

  // Conference
  async startConference(): Promise<LinphoneResponse> {
    return LinphoneModule.startConference();
  }

  async inviteToConference(participant: string): Promise<LinphoneResponse> {
    return LinphoneModule.inviteToConference(participant);
  }

  // Event Handling
  on(eventType: LinphoneEventType, callback: (data: any) => void): () => void {
    const subscription = this.eventEmitter.addListener(eventType, callback);
    return () => subscription.remove();
  }

  off(eventType: LinphoneEventType, callback?: (data: any) => void): void {
    this.eventEmitter.removeAllListeners(eventType);
  }

  // Convenience methods for specific events
  onRegistrationStateChanged(callback: (event: RegistrationEvent) => void): () => void {
    return this.on('RegistrationStateChanged', callback);
  }

  onCallStateChanged(callback: (event: CallEvent) => void): () => void {
    return this.on('CallStateChanged', callback);
  }

  onMessageReceived(callback: (event: MessageEvent) => void): () => void {
    return this.on('MessageReceived', callback);
  }

  onCallIncoming(callback: (event: CallEvent) => void): () => void {
    return this.on('CallIncoming', callback);
  }

  onCallConnected(callback: (event: CallEvent) => void): () => void {
    return this.on('CallConnected', callback);
  }

  onCallEnded(callback: (event: CallEvent) => void): () => void {
    return this.on('CallEnded', callback);
  }

  onConferenceJoined(callback: (data: any) => void): () => void {
    return this.on('ConferenceJoined', callback);
  }
}

// Create singleton instance
const linphone = new LinphoneVoip();

// Export both the class and the singleton instance
export { LinphoneVoip };
export default linphone;
