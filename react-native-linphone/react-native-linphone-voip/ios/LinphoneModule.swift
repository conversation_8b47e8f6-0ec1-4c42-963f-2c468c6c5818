import Foundation
import React
import linphonesw

@objc(LinphoneModule)
class LinphoneModule: RCTEventEmitter {
    
    private var linphoneCore: Core?
    private var coreDelegate: CoreDelegateStub?
    
    override init() {
        super.init()
        initializeLinphone()
    }
    
    override func supportedEvents() -> [String]! {
        return [
            "RegistrationStateChanged",
            "CallStateChanged", 
            "MessageReceived",
            "CallIncoming",
            "CallConnected",
            "CallEnded",
            "ConferenceJoined"
        ]
    }
    
    override static func requiresMainQueueSetup() -> Bool {
        return true
    }
    
    private func initializeLinphone() {
        DispatchQueue.main.async {
            do {
                print("Initializing Linphone SDK...")

                let factory = Factory.Instance
                self.linphoneCore = try factory.createCore(configPath: nil, factoryConfigPath: nil, systemContext: nil)

                self.coreDelegate = CoreDelegateStub(onRegistrationStateChanged: { (core, proxyConfig, state, message) in
                    self.sendEvent(withName: "RegistrationStateChanged", body: [
                        "state": state.rawValue,
                        "message": message
                    ])
                }, onCallStateChanged: { (core, call, state, message) in
                    self.sendEvent(withName: "CallStateChanged", body: [
                        "callId": call.callLog?.callId ?? "",
                        "state": state.rawValue,
                        "message": message,
                        "remoteAddress": call.remoteAddress?.asString() ?? ""
                    ])
                }, onMessageReceived: { (core, chatRoom, message) in
                    self.sendEvent(withName: "MessageReceived", body: [
                        "from": message.fromAddress?.asString() ?? "",
                        "to": message.toAddress?.asString() ?? "",
                        "content": message.textContent ?? "",
                        "timestamp": message.time
                    ])
                })

                self.linphoneCore?.addDelegate(delegate: self.coreDelegate!)
                try self.linphoneCore?.start()

                print("Linphone SDK initialized successfully")
            } catch {
                print("Failed to initialize Linphone SDK: \(error)")
            }
        }
    }
    
    @objc(register:withResolver:withRejecter:)
    func register(config: NSDictionary, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                guard let username = config["username"] as? String,
                      let password = config["password"] as? String,
                      let domain = config["domain"] as? String else {
                    reject("INVALID_CONFIG", "Missing required configuration", nil)
                    return
                }
                
                let transport = config["transport"] as? String ?? "UDP"
                
                print("Registering SIP account: \(username)@\(domain)")

                let factory = Factory.Instance
                let authInfo = try factory.createAuthInfo(username: username, userid: nil, passwd: password, ha1: nil, realm: nil, domain: domain)
                self.linphoneCore?.addAuthInfo(info: authInfo)

                let proxyConfig = try self.linphoneCore?.createProxyConfig()
                let identity = "sip:\(username)@\(domain)"
                let proxy = "sip:\(domain)"

                try proxyConfig?.setIdentityAddress(newValue: factory.createAddress(addr: identity))
                try proxyConfig?.setServerAddress(newValue: factory.createAddress(addr: proxy))
                proxyConfig?.route = proxy
                proxyConfig?.registerEnabled = true

                try self.linphoneCore?.addProxyConfig(config: proxyConfig!)
                self.linphoneCore?.defaultProxyConfig = proxyConfig
                resolve([
                    "status": "success",
                    "message": "Registration initiated"
                ])
                
            } catch {
                print("Registration failed: \(error)")
                reject("REGISTRATION_ERROR", error.localizedDescription, error)
            }
        }
    }
    
    @objc(unregister:withRejecter:)
    func unregister(resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Unregistering SIP account")
                
                // TODO: Implement actual unregistration when SDK is integrated
                
                let proxyConfig = self.linphoneCore?.defaultProxyConfig
                proxyConfig?.registerEnabled = false
                
                
                resolve([
                    "status": "success",
                    "message": "Unregistration completed"
                ])
                
            } catch {
                print("Unregistration failed: \(error)")
                reject("UNREGISTRATION_ERROR", error.localizedDescription, error)
            }
        }
    }
    
    @objc(call:withResolver:withRejecter:)
    func call(uri: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Making call to: \(uri)")

                let factory = Factory.Instance
                let address = try factory.createAddress(addr: uri)
                let callParams = try self.linphoneCore?.createCallParams(call: nil)
                let call = self.linphoneCore?.inviteAddressWithParams(addr: address, params: callParams)
                
                resolve([
                    "status": "success",
                    "message": "Call initiated",
                    "uri": uri
                ])
                
            } catch {
                print("Call failed: \(error)")
                reject("CALL_ERROR", error.localizedDescription, error)
            }
        }
    }
    
    @objc(hangup:withRejecter:)
    func hangup(resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Hanging up call")
                
                // TODO: Implement actual hangup when SDK is integrated
                
                let currentCall = self.linphoneCore?.currentCall
                try currentCall?.terminate()
                
                
                resolve([
                    "status": "success",
                    "message": "Call terminated"
                ])
                
            } catch {
                print("Hangup failed: \(error)")
                reject("HANGUP_ERROR", error.localizedDescription, error)
            }
        }
    }
    
    @objc(accept:withRejecter:)
    func accept(resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Accepting incoming call")
                
                // TODO: Implement actual call acceptance when SDK is integrated
                
                let currentCall = self.linphoneCore?.currentCall
                try currentCall?.accept()
                
                
                resolve([
                    "status": "success",
                    "message": "Call accepted"
                ])
                
            } catch {
                print("Accept failed: \(error)")
                reject("ACCEPT_ERROR", error.localizedDescription, error)
            }
        }
    }
    
    @objc(decline:withRejecter:)
    func decline(resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Declining incoming call")
                
                // TODO: Implement actual call decline when SDK is integrated
                
                let currentCall = self.linphoneCore?.currentCall
                try currentCall?.decline(reason: Reason.Declined)
                
                
                resolve([
                    "status": "success",
                    "message": "Call declined"
                ])
                
            } catch {
                print("Decline failed: \(error)")
                reject("DECLINE_ERROR", error.localizedDescription, error)
            }
        }
    }
    
    @objc(mute:withRejecter:)
    func mute(resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Toggling mute")
                
                // TODO: Implement actual mute when SDK is integrated
                
                let isMuted = self.linphoneCore?.micEnabled ?? false
                self.linphoneCore?.micEnabled = !isMuted
                
                
                resolve([
                    "status": "success",
                    "muted": true // Simulate muted state
                ])
                
            } catch {
                print("Mute failed: \(error)")
                reject("MUTE_ERROR", error.localizedDescription, error)
            }
        }
    }
    
    @objc(toggleSpeaker:withRejecter:)
    func toggleSpeaker(resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Toggling speaker")

                // TODO: Implement actual speaker toggle when SDK is integrated
                
                let currentCall = self.linphoneCore?.currentCall
                let currentDevice = currentCall?.outputAudioDevice
                let audioDevices = self.linphoneCore?.audioDevices
                // Logic to switch between earpiece and speaker
                

                resolve([
                    "status": "success",
                    "speakerEnabled": true // Simulate speaker state
                ])

            } catch {
                print("Speaker toggle failed: \(error)")
                reject("SPEAKER_ERROR", error.localizedDescription, error)
            }
        }
    }

    @objc(sendMessage:content:withResolver:withRejecter:)
    func sendMessage(to: String, content: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Sending message to: \(to)")

                // TODO: Implement actual message sending when SDK is integrated
                
                let factory = Factory.Instance
                let toAddress = try factory.createAddress(addr: to)
                let chatRoom = self.linphoneCore?.getChatRoom(peerAddr: toAddress)
                let message = try chatRoom?.createMessage(message: content)
                message?.send()
                

                resolve([
                    "status": "success",
                    "message": "Message sent",
                    "to": to,
                    "content": content
                ])

            } catch {
                print("Send message failed: \(error)")
                reject("SEND_MESSAGE_ERROR", error.localizedDescription, error)
            }
        }
    }

    @objc(createChatRoom:withResolver:withRejecter:)
    func createChatRoom(uri: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Creating chat room with: \(uri)")

                // TODO: Implement actual chat room creation when SDK is integrated
                /*
                let factory = Factory.Instance
                let address = try factory.createAddress(addr: uri)
                let chatRoom = self.linphoneCore?.getChatRoom(peerAddr: address)
                */

                resolve([
                    "status": "success",
                    "message": "Chat room created",
                    "uri": uri
                ])

            } catch {
                print("Create chat room failed: \(error)")
                reject("CREATE_CHAT_ROOM_ERROR", error.localizedDescription, error)
            }
        }
    }

    @objc(getCallHistory:withRejecter:)
    func getCallHistory(resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Getting call history")

                // TODO: Implement actual call history retrieval when SDK is integrated
                /*
                let callLogs = self.linphoneCore?.callLogs ?? []
                var history: [[String: Any]] = []

                for callLog in callLogs {
                    let call: [String: Any] = [
                        "remoteAddress": callLog.remoteAddress?.asString() ?? "",
                        "direction": callLog.dir.rawValue,
                        "startTime": callLog.startDate,
                        "duration": callLog.duration,
                        "status": callLog.status.rawValue
                    ]
                    history.append(call)
                }
                */

                // For now, return mock data
                resolve([
                    "status": "success",
                    "history": [] // Empty array for now
                ])

            } catch {
                print("Get call history failed: \(error)")
                reject("GET_CALL_HISTORY_ERROR", error.localizedDescription, error)
            }
        }
    }

    @objc(startConference:withRejecter:)
    func startConference(resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Starting conference")

                // TODO: Implement actual conference start when SDK is integrated
                /*
                let conference = try self.linphoneCore?.createConference(params: nil)
                */

                resolve([
                    "status": "success",
                    "message": "Conference started"
                ])

            } catch {
                print("Start conference failed: \(error)")
                reject("START_CONFERENCE_ERROR", error.localizedDescription, error)
            }
        }
    }

    @objc(inviteToConference:withResolver:withRejecter:)
    func inviteToConference(participant: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            do {
                print("Inviting to conference: \(participant)")

                // TODO: Implement actual conference invitation when SDK is integrated
                /*
                let factory = Factory.Instance
                let participantAddress = try factory.createAddress(addr: participant)
                let conference = self.linphoneCore?.conference
                try conference?.addParticipant(uri: participantAddress)
                */

                resolve([
                    "status": "success",
                    "message": "Participant invited",
                    "participant": participant
                ])

            } catch {
                print("Invite to conference failed: \(error)")
                reject("INVITE_TO_CONFERENCE_ERROR", error.localizedDescription, error)
            }
        }
    }
}
