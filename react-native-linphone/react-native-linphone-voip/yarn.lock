# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@0no-co/graphql.web@npm:^1.0.13, @0no-co/graphql.web@npm:^1.0.8":
  version: 1.1.2
  resolution: "@0no-co/graphql.web@npm:1.1.2"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0
  peerDependenciesMeta:
    graphql:
      optional: true
  checksum: ddf4f073c9f03c41a5672b9285ad5573f34ad6d40ed73691c128d5332ff6186222ff909949cf6ef07bad8b417bbb5b609636e049700d3727a196111019a7aab4
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: d3ad7b89d973df059c4e8e6d7c972cbeb1bb2f18f002a3bd04ae0707da214cb06cc06929b65aa2313b9347463df2914772298bae8b1d7973f246bb3f2ab3e8f0
  languageName: node
  linkType: hard

"@ark/schema@npm:0.46.0":
  version: 0.46.0
  resolution: "@ark/schema@npm:0.46.0"
  dependencies:
    "@ark/util": 0.46.0
  checksum: a4e7bc0e1c23009c7702ada7cfcbb1638af76f9721c43f96432844ec8616da6fc8121057fb87b0b80142558cf5c3e6141f40443cf43dd026ada8fd4acd635565
  languageName: node
  linkType: hard

"@ark/util@npm:0.46.0":
  version: 0.46.0
  resolution: "@ark/util@npm:0.46.0"
  checksum: 0c0ceeb10aa0806860f7a7922586a05cda2945f7f598b414b4ebf268a6b45b00f9a854d6afd6b59df58c733e90d00b230194dd6a180a3a23d0eb64612be1b0e0
  languageName: node
  linkType: hard

"@babel/code-frame@npm:7.10.4, @babel/code-frame@npm:~7.10.4":
  version: 7.10.4
  resolution: "@babel/code-frame@npm:7.10.4"
  dependencies:
    "@babel/highlight": ^7.10.4
  checksum: feb4543c8a509fe30f0f6e8d7aa84f82b41148b963b826cd330e34986f649a85cb63b2f13dd4effdf434ac555d16f14940b8ea5f4433297c2f5ff85486ded019
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.24.7, @babel/code-frame@npm:^7.26.2, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": ^7.27.1
    js-tokens: ^4.0.0
    picocolors: ^1.1.1
  checksum: 5874edc5d37406c4a0bb14cf79c8e51ad412fb0423d176775ac14fc0259831be1bf95bdda9c2aa651126990505e09a9f0ed85deaa99893bc316d2682c5115bdc
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2, @babel/compat-data@npm:^7.27.7, @babel/compat-data@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/compat-data@npm:7.28.0"
  checksum: 37a40d4ea10a32783bc24c4ad374200f5db864c8dfa42f82e76f02b8e84e4c65e6a017fc014d165b08833f89333dff4cb635fce30f03c333ea3525ea7e20f0a2
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.6, @babel/core@npm:^7.12.3, @babel/core@npm:^7.20.0, @babel/core@npm:^7.23.9, @babel/core@npm:^7.24.7, @babel/core@npm:^7.25.2":
  version: 7.28.0
  resolution: "@babel/core@npm:7.28.0"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.27.1
    "@babel/generator": ^7.28.0
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-module-transforms": ^7.27.3
    "@babel/helpers": ^7.27.6
    "@babel/parser": ^7.28.0
    "@babel/template": ^7.27.2
    "@babel/traverse": ^7.28.0
    "@babel/types": ^7.28.0
    convert-source-map: ^2.0.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.3
    semver: ^6.3.1
  checksum: 86da9e26c96e22d96deca0509969d273476f61c30464f262dec5e5a163422e07d5ab690ed54619d10fcab784abd10567022ce3d90f175b40279874f5288215e3
  languageName: node
  linkType: hard

"@babel/eslint-parser@npm:^7.25.1":
  version: 7.28.0
  resolution: "@babel/eslint-parser@npm:7.28.0"
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals": 5.1.1-v1
    eslint-visitor-keys: ^2.1.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.11.0
    eslint: ^7.5.0 || ^8.0.0 || ^9.0.0
  checksum: ccfc4b9b9fdca2b8df95da3827b70231e9588a71447ff7b2de76c4f36710e4e0a7dc5e2e98623f398a737c2429c46500cb11d4ccdfeb98271e067d0bf0eec9b5
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.20.5, @babel/generator@npm:^7.25.0, @babel/generator@npm:^7.28.0, @babel/generator@npm:^7.7.2":
  version: 7.28.0
  resolution: "@babel/generator@npm:7.28.0"
  dependencies:
    "@babel/parser": ^7.28.0
    "@babel/types": ^7.28.0
    "@jridgewell/gen-mapping": ^0.3.12
    "@jridgewell/trace-mapping": ^0.3.28
    jsesc: ^3.0.2
  checksum: 3fc9ecca7e7a617cf7b7357e11975ddfaba4261f374ab915f5d9f3b1ddc8fd58da9f39492396416eb08cf61972d1aa13c92d4cca206533c553d8651c2740f07f
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1, @babel/helper-annotate-as-pure@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.3"
  dependencies:
    "@babel/types": ^7.27.3
  checksum: 63863a5c936ef82b546ca289c9d1b18fabfc24da5c4ee382830b124e2e79b68d626207febc8d4bffc720f50b2ee65691d7d12cc0308679dee2cd6bdc926b7190
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.1, @babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": ^7.27.2
    "@babel/helper-validator-option": ^7.27.1
    browserslist: ^4.24.0
    lru-cache: ^5.1.1
    semver: ^6.3.1
  checksum: 7b95328237de85d7af1dea010a4daa28e79f961dda48b652860d5893ce9b136fc8b9ea1f126d8e0a24963b09ba5c6631dcb907b4ce109b04452d34a6ae979807
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-member-expression-to-functions": ^7.27.1
    "@babel/helper-optimise-call-expression": ^7.27.1
    "@babel/helper-replace-supers": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
    "@babel/traverse": ^7.27.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 406954b455e5b20924e7d1b41cf932e6e98e95c3a5224c7a70c3ad96a84e8fbde915ceff7ddbf9c7d121397c4e9274f061241648475122cf6fe54e0a95caae15
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    regexpu-core: ^6.2.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 2ede6bbad0016a9262fd281ce8f1a5d69e6179dcec4ea282830e924c29a29b66b0544ecb92e4ef4acdaf2c4c990931d7dc442dbcd6a8bcec4bad73923ef70934
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.5":
  version: 0.6.5
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.5"
  dependencies:
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-plugin-utils": ^7.27.1
    debug: ^4.4.1
    lodash.debounce: ^4.0.8
    resolve: ^1.22.10
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 9fd3b09b209c8ed0d3d8bc1f494f1368b9e1f6e46195af4ce948630fe97d7dafde4882eedace270b319bf6555ddf35e220c77505f6d634f621766cdccbba0aae
  languageName: node
  linkType: hard

"@babel/helper-globals@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/helper-globals@npm:7.28.0"
  checksum: d8d7b91c12dad1ee747968af0cb73baf91053b2bcf78634da2c2c4991fb45ede9bd0c8f9b5f3254881242bc0921218fcb7c28ae885477c25177147e978ce4397
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": ^7.27.1
    "@babel/types": ^7.27.1
  checksum: b13a3d120015a6fd2f6e6c2ff789cd12498745ef028710cba612cfb751b91ace700c3f96c1689228d1dcb41e9d4cf83d6dff8627dcb0c8da12d79440e783c6b8
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.25.9, @babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": ^7.27.1
    "@babel/types": ^7.27.1
  checksum: 92d01c71c0e4aacdc2babce418a9a1a27a8f7d770a210ffa0f3933f321befab18b655bc1241bebc40767516731de0b85639140c42e45a8210abe1e792f115b28
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1, @babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": ^7.27.1
    "@babel/helper-validator-identifier": ^7.27.1
    "@babel/traverse": ^7.27.3
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: c611d42d3cb7ba23b1a864fcf8d6cde0dc99e876ca1c9a67e4d7919a70706ded4aaa45420de2bf7f7ea171e078e59f0edcfa15a56d74b9485e151b95b93b946e
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": ^7.27.1
  checksum: 0fb7ee824a384529d6b74f8a58279f9b56bfe3cce332168067dddeab2552d8eeb56dc8eaf86c04a3a09166a316cb92dfc79c4c623cd034ad4c563952c98b464f
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.27.1, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 5d715055301badab62bdb2336075a77f8dc8bd290cad2bc1b37ea3bf1b3efc40594d308082229f239deb4d6b5b80b0a73bce000e595ea74416e0339c11037047
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-wrap-function": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 0747397ba013f87dbf575454a76c18210d61c7c9af0f697546b4bcac670b54ddc156330234407b397f0c948738c304c228e0223039bc45eab4fbf46966a5e8cc
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": ^7.27.1
    "@babel/helper-optimise-call-expression": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 3690266c304f21008690ba68062f889a363583cabc13c3d033b94513953147af3e0a3fdb48fa1bb9fa3734b64e221fc65e5222ab70837f02321b7225f487c6ef
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": ^7.27.1
    "@babel/types": ^7.27.1
  checksum: 4f380c5d0e0769fa6942a468b0c2d7c8f0c438f941aaa88f785f8752c103631d0904c7b4e76207a3b0e6588b2dec376595370d92ca8f8f1b422c14a69aa146d4
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 0a8464adc4b39b138aedcb443b09f4005d86207d7126e5e079177e05c3116107d856ec08282b365e9a79a9872f40f4092a6127f8d74c8a01c1ef789dacfc25d6
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9, @babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 3c7e8391e59d6c85baeefe9afb86432f2ab821c6232b00ea9082a51d3e7e95a2f3fb083d74dc1f49ac82cf238e1d2295dafcb001f7b0fab479f3f56af5eaaa47
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: db73e6a308092531c629ee5de7f0d04390835b21a263be2644276cb27da2384b64676cab9f22cd8d8dbd854c92b1d7d56fc8517cf0070c35d1c14a8c828b0903
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-wrap-function@npm:7.27.1"
  dependencies:
    "@babel/template": ^7.27.1
    "@babel/traverse": ^7.27.1
    "@babel/types": ^7.27.1
  checksum: b0427765766494cb5455a188d4cdef5e6167f2835a8ed76f3c25fa3bbe2ec2a716588fa326c52fab0d184a9537200d76e48656e516580a914129d74528322821
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.6":
  version: 7.27.6
  resolution: "@babel/helpers@npm:7.27.6"
  dependencies:
    "@babel/template": ^7.27.2
    "@babel/types": ^7.27.6
  checksum: 12f96a5800ff677481dbc0a022c617303e945210cac4821ad5377a31201ffd8d9c4d00f039ed1487cf2a3d15868fb2d6cabecdb1aba334bd40a846f1938053a2
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.10.4":
  version: 7.25.9
  resolution: "@babel/highlight@npm:7.25.9"
  dependencies:
    "@babel/helper-validator-identifier": ^7.25.9
    chalk: ^2.4.2
    js-tokens: ^4.0.0
    picocolors: ^1.0.0
  checksum: a6e0ac0a1c4bef7401915ca3442ab2b7ae4adf360262ca96b91396bfb9578abb28c316abf5e34460b780696db833b550238d9256bdaca60fade4ba7a67645064
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.23.9, @babel/parser@npm:^7.24.7, @babel/parser@npm:^7.25.3, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/parser@npm:7.28.0"
  dependencies:
    "@babel/types": ^7.28.0
  bin:
    parser: ./bin/babel-parser.js
  checksum: 718e4ce9b0914701d6f74af610d3e7d52b355ef1dcf34a7dedc5930e96579e387f04f96187e308e601828b900b8e4e66d2fe85023beba2ac46587023c45b01cf
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 72f24b9487e445fa61cf8be552aad394a648c2bb445c38d39d1df003186d9685b87dd8d388c950f438ea0ca44c82099d9c49252fb681c719cc72edf02bbe0304
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: eb7f4146dc01f1198ce559a90b077e58b951a07521ec414e3c7d4593bf6c4ab5c2af22242a7e9fec085e20299e0ba6ea97f44a45e84ab148141bf9eb959ad25e
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 621cfddfcc99a81e74f8b6f9101fd260b27500cb1a568e3ceae9cc8afe9aee45ac3bca3900a2b66c612b1a2366d29ef67d4df5a1c975be727eaad6906f98c2c6
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
    "@babel/plugin-transform-optional-chaining": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: f07aa80272bd7a46b7ba11a4644da6c9b6a5a64e848dfaffdad6f02663adefd512e1aaebe664c4dd95f7ed4f80c872c7f8db8d8e34b47aae0930b412a28711a0
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 4d6792ccade2d6b9d5577b0a879ab22d05ac8a1206b1a636b6ffdb53a0c0bacaf0f7947e46de254f228ffd75456f4b95ccd82fdeaefc0b92d88af3c5991863ad
  languageName: node
  linkType: hard

"@babel/plugin-proposal-decorators@npm:^7.12.9":
  version: 7.28.0
  resolution: "@babel/plugin-proposal-decorators@npm:7.28.0"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/plugin-syntax-decorators": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b134907b09ba58f202c219de4eb6246a560441f9166f67b154c1dac32e8a6233e0f59ae9b4909dbd413e0c7dd0afb803a90a7a2fabc194e1b7543211a159eb87
  languageName: node
  linkType: hard

"@babel/plugin-proposal-export-default-from@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-proposal-export-default-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cf9eb3c80bcee3ee82d28f1053db97fa6c6e4dea819f73df5a3cb9155d45efc29914e86353572eab36adfe691ca1573e6e2cddae4edbdd475253044575eb7a24
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d97745d098b835d55033ff3a7fb2b895b9c5295b08a5759e4f20df325aa385a3e0bc9bd5ad8f2ec554a44d4e6525acfc257b8c5848a1345cb40f26a30e277e91
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a10849d83e47aec50f367a9e56a6b22d662ddce643334b087f9828f4c3dd73bdc5909aaeabe123fed78515767f9ca43498a0e621c438d1cd2802d7fae3c9648
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": ^7.12.13
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3e80814b5b6d4fe17826093918680a351c2d34398a914ce6e55d8083d72a9bdde4fbaf6a2dcea0e23a03de26dc2917ae3efd603d27099e2b98380345703bf948
  languageName: node
  linkType: hard

"@babel/plugin-syntax-decorators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-decorators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c085b6083d9ce71f47563e05dddfff18a7611e376297b5a9eb35ef70e5919822f87bfba5b25276dfa55bdb6465943ba5d8d9a00f870611d63eaa1a148adc275e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ce307af83cf433d4ec42932329fad25fa73138ab39c7436882ea28742e1c0066626d224e0ad2988724c82644e41601cef607b36194f695cb78a1fcdc959637bd
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-default-from@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-export-default-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d9a6a9c51f644a5ed139dbe1e8cf5a38c9b390af27ad2fc6f0eba579ac543b039efff34200744bfc8523132c06aa6de921238bd2088948bb4dce4571cea43438
  languageName: node
  linkType: hard

"@babel/plugin-syntax-flow@npm:^7.12.1, @babel/plugin-syntax-flow@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-flow@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7baca3171ed595d04c865b0ce46fca7f21900686df9d7fcd1017036ce78bb5483e33803de810831e68d39cf478953db69f49ae3f3de2e3207bc4ba49a96b6739
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fb661d630808d67ecb85eabad25aac4e9696a20464bad4c4a6a0d3d40e4dc22557d47e9be3d591ec06429cf048cfe169b8891c373606344d51c4f3ac0f91d6d0
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.24.7, @babel/plugin-syntax-import-attributes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 97973982fff1bbf86b3d1df13380567042887c50e2ae13a400d02a8ff2c9742a60a75e279bfb73019e1cd9710f04be5e6ab81f896e6678dcfcec8b135e8896cf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 166ac1125d10b9c0c430e4156249a13858c0366d38844883d75d27389621ebe651115cb2ceb6dc011534d5055719fa1727b59f39e1ab3ca97820eef3dcab5b9b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.27.1, @babel/plugin-syntax-jsx@npm:^7.7.2":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c6d1324cff286a369aa95d99b8abd21dd07821b5d3affd5fe7d6058c84cff9190743287826463ee57a7beecd10fa1e4bc99061df532ee14e188c1c8937b13e3a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aff33577037e34e515911255cdbb1fd39efee33658aa00b8a5fd3a4b903585112d037cce1cc9e4632f0487dc554486106b79ccd5ea63a2e00df4363f6d4ff886
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87aca4918916020d1fedba54c0e232de408df2644a425d153be368313fdde40d96088feed6c4e5ab72aac89be5d07fef2ddf329a15109c5eb65df006bf2580d1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 01ec5547bd0497f76cc903ff4d6b02abc8c05f301c88d2622b6d834e33a5651aa7c7a3d80d8d57656a4588f7276eba357f6b7e006482f5b564b7a6488de493a1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: eef94d53a1453361553c1f98b68d17782861a04a392840341bc91780838dd4e695209c783631cf0de14c635758beafb6a3a65399846ffa4386bff90639347f30
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b317174783e6e96029b743ccff2a67d63d38756876e7e5d0ba53a322e38d9ca452c13354a57de1ad476b4c066dbae699e0ca157441da611117a47af88985ecda
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbd1a56b095be7820029b209677b194db9b1d26691fe999856462e66b25b281f031f3dfd91b1619e9dcf95bebe336211833b854d0fb8780d618e35667c2d0d7e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.27.1, @babel/plugin-syntax-typescript@npm:^7.7.2":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87836f7e32af624c2914c73cd6b9803cf324e07d43f61dbb973c6a86f75df725e12540d91fac7141c14b697aa9268fd064220998daced156e96ac3062d7afb41
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.24.7, @babel/plugin-transform-arrow-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 62c2cc0ae2093336b1aa1376741c5ed245c0987d9e4b4c5313da4a38155509a7098b5acce582b6781cc0699381420010da2e3086353344abe0a6a0ec38961eb7
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.25.4, @babel/plugin-transform-async-generator-functions@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-remap-async-to-generator": ^7.27.1
    "@babel/traverse": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 174aaccd7a8386fd7f32240c3f65a93cf60dcc5f6a2123cfbff44c0d22b424cd41de3a0c6d136b6a2fa60a8ca01550c261677284cb18a0daeab70730b2265f1d
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.24.7, @babel/plugin-transform-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-remap-async-to-generator": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d79d7a7ae7d416f6a48200017d027a6ba94c09c7617eea8b4e9c803630f00094c1a4fc32bf20ce3282567824ce3fcbda51653aac4003c71ea4e681b331338979
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7fb4988ca80cf1fc8345310d5edfe38e86b3a72a302675cdd09404d5064fe1d1fe1283ebe658ad2b71445ecef857bfb29a748064306b5f6c628e0084759c2201
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.25.0, @babel/plugin-transform-block-scoping@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-block-scoping@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6d740f9a386e5fbdffd9e7c5a8400bff8d54068241a78b8e71aba6f1f46eff0c4297902f5f1543bee1ed076ec88d0dc4ceed19e98a466802c14d3c20f178f712
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.24.7, @babel/plugin-transform-class-properties@npm:^7.25.4, @babel/plugin-transform-class-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 475a6e5a9454912fe1bdc171941976ca10ea4e707675d671cdb5ce6b6761d84d1791ac61b6bca81a2e5f6430cb7b9d8e4b2392404110e69c28207a754e196294
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-static-block@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 69688fe1641ae0ea025b916b8c2336e8b5643a5ec292e8f546ecd35d9d9d4bb301d738910822a79d867098cf687d550d92cd906ae4cda03c0f69b1ece2149a58
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.25.4, @babel/plugin-transform-classes@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-classes@npm:7.28.0"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.3
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-globals": ^7.28.0
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-replace-supers": ^7.27.1
    "@babel/traverse": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0b47188046a4f1579123354ee30d08874b4b585d45128a3d492fa1cba7e26c8039d8c44d38d85f4eaa9b5a53064c66f032cfc35526c73c74a865a11edf3a0c28
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.24.7, @babel/plugin-transform-computed-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/template": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 48bd20f7d631b08c51155751bf75b698d4a22cca36f41c22921ab92e53039c9ec5c3544e5282e18692325ef85d2e4a18c27e12c62b5e20c26fb0c92447e35224
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.24.8, @babel/plugin-transform-destructuring@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-destructuring@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/traverse": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5b464d6a03c6eaa1327b60ffc1630ca977db0256938b34e281e65c81c965680e930a6bac043272942d6d4bbd7d1eddded0b7231779429ba51275e092e7367859
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2173e5b13f403538ffc6bd57b190cedf4caf320abc13a99e5b2721864e7148dbd3bd7c82d92377136af80432818f665fdd9a1fd33bc5549a4c91e24e5ce2413c
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ef2112d658338e3ff0827f39a53c0cfa211f1cbbe60363bca833a5269df389598ec965e7283600b46533c39cdca82307d0d69c0f518290ec5b00bb713044715b
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 2a109613535e6ac79240dced71429e988affd6a5b3d0cd0f563c8d6c208c51ce7bf2c300bc1150502376b26a51f279119b3358f1c0f2d2f8abca3bcd62e1ae46
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7a9fbc8d17148b7f11a1d1ca3990d2c2cd44bd08a45dcaf14f20a017721235b9044b20e6168b6940282bb1b48fb78e6afbdfb9dd9d82fde614e15baa7d579932
  languageName: node
  linkType: hard

"@babel/plugin-transform-explicit-resource-management@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-explicit-resource-management@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/plugin-transform-destructuring": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a44140097ed4854883c426613f4e8763237cd0fdab1c780514f4315f6c148d6b528d7a57fe6fdec4dbce28a21b70393ef3507b72dfec2e30bfc8d7db1ff19474
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4ff4a0f30babc457a5ae8564deda209599627c2ce647284a0e8e66f65b44f6d968cf77761a4cc31b45b61693f0810479248c79e681681d8ccb39d0c52944c1fd
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.25.9, @babel/plugin-transform-export-namespace-from@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85082923eca317094f08f4953d8ea2a6558b3117826c0b740676983902b7236df1f4213ad844cb38c2dae104753dbe8f1cc51f01567835d476d32f5f544a4385
  languageName: node
  linkType: hard

"@babel/plugin-transform-flow-strip-types@npm:^7.25.2, @babel/plugin-transform-flow-strip-types@npm:^7.26.5, @babel/plugin-transform-flow-strip-types@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-flow-strip-types@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/plugin-syntax-flow": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0885028866fadefef35292d5a27f878d6a12b6f83778f8731481d4503b49c258507882a7de2aafda9b62d5f6350042f1a06355b998d5ed5e85d693bfcb77b939
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.24.7, @babel/plugin-transform-for-of@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-for-of@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c9224e08de5d80b2c834383d4359aa9e519db434291711434dd996a4f86b7b664ad67b45d65459b7ec11fa582e3e11a3c769b8a8ca71594bdd4e2f0503f84126
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.25.1, @babel/plugin-transform-function-name@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 26a2a183c3c52a96495967420a64afc5a09f743a230272a131668abf23001e393afa6371e6f8e6c60f4182bea210ed31d1caf866452d91009c1daac345a52f23
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-json-strings@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2c05a02f63b49f47069271b3405a66c3c8038de5b995b0700b1bd9a5e2bb3e67abd01e4604629302a521f4d8122a4233944aefa16559fd4373d256cc5d3da57f
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.25.2, @babel/plugin-transform-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0a76d12ab19f32dd139964aea7da48cecdb7de0b75e207e576f0f700121fe92367d788f328bf4fb44b8261a0f605c97b44e62ae61cddbb67b14e94c88b411f95
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.24.7, @babel/plugin-transform-logical-assignment-operators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2757955d81d65cc4701c17b83720745f6858f7a1d1d58117e379c204f47adbeb066b778596b6168bdbf4a22c229aab595d79a9abc261d0c6bfd62d4419466e73
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 804121430a6dcd431e6ffe99c6d1fbbc44b43478113b79c677629e7f877b4f78a06b69c6bfb2747fd84ee91879fe2eb32e4620b53124603086cf5b727593ebe8
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-amd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8bb36d448e438d5d30f4faf19120e8c18aa87730269e65d805bf6032824d175ed738057cc392c2c8a650028f1ae0f346cad8d6b723f31a037b586e2092a7be18
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.24.7, @babel/plugin-transform-modules-commonjs@npm:^7.24.8, @babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bc45c1beff9b145c982bd6a614af338893d38bce18a9df7d658c9084e0d8114b286dcd0e015132ae7b15dd966153cb13321e4800df9766d0ddd892d22bf09d2a
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-validator-identifier": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7c17a8973676c18525d87f277944616596f1b154cc2b9263bfd78ecdbf5f4288ec46c7f58017321ca3e3d6dfeb96875467b95311a39719b475d42a157525d87f
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-umd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b007dd89231f2eeccf1c71a85629bcb692573303977a4b1c5f19a835ea6b5142c18ef07849bc6d752b874a11bc0ddf3c67468b77c8ee8310290b688a4f01ef31
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.24.7, @babel/plugin-transform-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a711c92d9753df26cefc1792481e5cbff4fe4f32b383d76b25e36fa865d8023b1b9aa6338cf18f5c0e864c71a7fbe8115e840872ccd61a914d9953849c68de7d
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-new-target@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 32c8078d843bda001244509442d68fd3af088d7348ba883f45c262b2c817a27ffc553b0d78e7f7a763271b2ece7fac56151baad7a91fb21f5bb1d2f38e5acad7
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.24.7, @babel/plugin-transform-nullish-coalescing-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1c6b3730748782d2178cc30f5cc37be7d7666148260f3f2dfc43999908bdd319bdfebaaf19cf04ac1f9dee0f7081093d3fa730cda5ae1b34bcd73ce406a78be7
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.24.7, @babel/plugin-transform-numeric-separator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 049b958911de86d32408cd78017940a207e49c054ae9534ab53a32a57122cc592c0aae3c166d6f29bd1a7d75cc779d71883582dd76cb28b2fbb493e842d8ffca
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.24.7, @babel/plugin-transform-object-rest-spread@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.28.0"
  dependencies:
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/plugin-transform-destructuring": ^7.28.0
    "@babel/plugin-transform-parameters": ^7.27.7
    "@babel/traverse": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7c32c988b4b040d0091d0210b6b946249571858b2f33f3a5105f41c28ee0b8440a9dfb2aa46f3ae0d3014f86ddf16aee9a0cbf4229daf8e013235352b8f31fc9
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-object-super@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-replace-supers": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 46b819cb9a6cd3cfefe42d07875fee414f18d5e66040366ae856116db560ad4e16f3899a0a7fddd6773e0d1458444f94b208b67c0e3b6977a27ea17a5c13dbf6
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.24.7, @babel/plugin-transform-optional-catch-binding@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f4356b04cf21a98480f9788ea50f1f13ee88e89bb6393ba4b84d1f39a4a84c7928c9a4328e8f4c5b6deb218da68a8fd17bf4f46faec7653ddc20ffaaa5ba49f4
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.24.7, @babel/plugin-transform-optional-chaining@npm:^7.24.8, @babel/plugin-transform-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c4428d31f182d724db6f10575669aad3dbccceb0dea26aa9071fa89f11b3456278da3097fcc78937639a13c105a82cd452dc0218ce51abdbcf7626a013b928a5
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.24.7, @babel/plugin-transform-parameters@npm:^7.27.7":
  version: 7.27.7
  resolution: "@babel/plugin-transform-parameters@npm:7.27.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d51f195e1d6ac5d9fce583e9a70a5bfe403e62386e5eb06db9fbc6533f895a98ff7e7c3dcaa311a8e6fa7a9794466e81cdabcba6af9f59d787fb767bfe7868b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.24.7, @babel/plugin-transform-private-methods@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-methods@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c76f8f6056946466116e67eb9d8014a2d748ade2062636ab82045c1dac9c233aff10e597777bc5af6f26428beb845ceb41b95007abef7d0484da95789da56662
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.24.7, @babel/plugin-transform-private-property-in-object@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: af539af1bd423aa46b9da83d649be716494ca80783841f47094b6741fa24e11141446027fd152ddff791dede9d4a76d0d5eb467402a2e584d7f5ea90e2673c7e
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-property-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7caec27d5ed8870895c9faf4f71def72745d69da0d8e77903146a4e135fd7bed5778f5f9cebb36c5fba86338e6194dd67a08c033fc84b4299b7eceab6d9630cb
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.24.7, @babel/plugin-transform-react-display-name@npm:^7.27.1":
  version: 7.28.0
  resolution: "@babel/plugin-transform-react-display-name@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 268b1a9192974439d17949e170b01cac2a2aa003c844e2fe3b8361146f42f66487178cffdfa8ce862aa9e6c814bc37f879a70300cb3f067815d15fa6aad04e6d
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.27.1"
  dependencies:
    "@babel/plugin-transform-react-jsx": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b88865d5b8c018992f2332da939faa15c4d4a864c9435a5937beaff3fe43781432cc42e0a5d5631098e0bd4066fc33f5fa72203b388b074c3545fe7aaa21e474
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 72cbae66a58c6c36f7e12e8ed79f292192d858dd4bb00e9e89d8b695e4c5cb6ef48eec84bffff421a5db93fd10412c581f1cccdb00264065df76f121995bdb68
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e2843362adb53692be5ee9fa07a386d2d8883daad2063a3575b3c373fc14cdf4ea7978c67a183cb631b4c9c8d77b2f48c24c088f8e65cc3600cb8e97d72a7161
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.25.2, @babel/plugin-transform-react-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-module-imports": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/plugin-syntax-jsx": ^7.27.1
    "@babel/types": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 960d36e5d11ba68e4fbf1e2b935c153cb6ea7b0004f838aaee8baf7de30462b8f0562743a39ce3c370cc70b8f79d3c549104a415a615b2b0055b71fd025df0f3
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a6f591c5e85a1ab0685d4a25afe591fe8d11dc0b73c677cf9560ff8d540d036a1cce9efcb729fc9092def4d854dc304ffdc063a89a9247900b69c516bf971a4c
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.24.7, @babel/plugin-transform-regenerator@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-regenerator@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 40c76d47333caee8e30dbed9c54c56366f9caa2b5946070ea87317843ccd3ffb0b4008b64b667b08feca917e07f235af2af1ab9de097eaea9af60bb01a8cd5dd
  languageName: node
  linkType: hard

"@babel/plugin-transform-regexp-modifiers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-regexp-modifiers@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: f6cb385fe0e798bff7e9b20cf5912bf40e180895ff3610b1ccdce260f3c20daaebb3a99dc087c8168a99151cd3e16b94f4689fd5a4b01cf1834b45c133e620b2
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-reserved-words@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: dea0b66742d2863b369c06c053e11e15ba785892ea19cccf7aef3c1bdaa38b6ab082e19984c5ea7810d275d9445c5400fcc385ad71ce707ed9256fadb102af3b
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.24.7":
  version: 7.28.0
  resolution: "@babel/plugin-transform-runtime@npm:7.28.0"
  dependencies:
    "@babel/helper-module-imports": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    babel-plugin-polyfill-corejs2: ^0.4.14
    babel-plugin-polyfill-corejs3: ^0.13.0
    babel-plugin-polyfill-regenerator: ^0.6.5
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8d324eb312636efe706917a5d44f867538654453c9bf4efd34b0dbd712c6d80e604092b98acbfcb318f42bec707b590c28ae95c659ff359a64a4ccb7621dc400
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.24.7, @babel/plugin-transform-shorthand-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fbba6e2aef0b69681acb68202aa249c0598e470cc0853d7ff5bd0171fd6a7ec31d77cfabcce9df6360fc8349eded7e4a65218c32551bd3fc0caaa1ac899ac6d4
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.24.7, @babel/plugin-transform-spread@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 58b08085ee9c29955ac3b68d61c1a79728d44d19a69cb5eb669794aeaf54c57c6647af7b979c1297e81ede3d08b3ddcb1936ef39a533f28ff3e399a9be54dab1
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.24.7, @babel/plugin-transform-sticky-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e1414a502efba92c7974681767e365a8cda6c5e9e5f33472a9eaa0ce2e75cea0a9bef881ff8dda37c7810ad902f98d3c00ead92a3ac3b73a79d011df85b5a189
  languageName: node
  linkType: hard

"@babel/plugin-transform-strict-mode@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-strict-mode@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0c5bbc9f913b2cea1a1f5697bac320f11575016eed2eee16d2430af5ddceff5382ad3f1b70bf7158ff458db38568a903fab03b308150753453354785365667ec
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-template-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 93aad782503b691faef7c0893372d5243df3219b07f1f22cfc32c104af6a2e7acd6102c128439eab15336d048f1b214ca134b87b0630d8cd568bf447f78b25ce
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ed8048c8de72c60969a64cf2273cc6d9275d8fa8db9bd25a1268273a00fb9cbd79931140311411bda1443aa56cb3961fb911d1795abacde7f0482f1d8fdf0356
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.25.2, @babel/plugin-transform-typescript@npm:^7.27.1":
  version: 7.28.0
  resolution: "@babel/plugin-transform-typescript@npm:7.28.0"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.3
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
    "@babel/plugin-syntax-typescript": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 14c1024bcd57fcd469d90cf0c15c3cd4e771e2eb2cd9afee3aa79b59c8ed103654f7c5c71cdb3bfe31c1d0cb08bfad8c80f5aa1d24b4b454bd21301d5925533d
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d817154bc10758ddd85b716e0bc1af1a1091e088400289ab6b78a1a4d609907ce3d2f1fd51a6fd0e0c8ecbb5f8e3aab4957e0747776d132d2379e85c3ef0520a
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5d99c89537d1ebaac3f526c04b162cf95a47d363d4829f78c6701a2c06ab78a48da66a94f853f85f44a3d72153410ba923e072bed4b7166fa097f503eb14131d
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.24.7, @babel/plugin-transform-unicode-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a34d89a2b75fb78e66d97c3dc90d4877f7e31f43316b52176f95a5dee20e9bb56ecf158eafc42a001676ddf7b393d9e67650bad6b32f5405780f25fb83cd68e3
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 295126074c7388ab05c82ef3ed0907a1ee4666bbdd763477ead9aba6eb2c74bdf65669416861ac93d337a4a27640963bb214acadc2697275ce95aab14868d57f
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.25.2":
  version: 7.28.0
  resolution: "@babel/preset-env@npm:7.28.0"
  dependencies:
    "@babel/compat-data": ^7.28.0
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-validator-option": ^7.27.1
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key": ^7.27.1
    "@babel/plugin-bugfix-safari-class-field-initializer-scope": ^7.27.1
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": ^7.27.1
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": ^7.27.1
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": ^7.27.1
    "@babel/plugin-proposal-private-property-in-object": 7.21.0-placeholder-for-preset-env.2
    "@babel/plugin-syntax-import-assertions": ^7.27.1
    "@babel/plugin-syntax-import-attributes": ^7.27.1
    "@babel/plugin-syntax-unicode-sets-regex": ^7.18.6
    "@babel/plugin-transform-arrow-functions": ^7.27.1
    "@babel/plugin-transform-async-generator-functions": ^7.28.0
    "@babel/plugin-transform-async-to-generator": ^7.27.1
    "@babel/plugin-transform-block-scoped-functions": ^7.27.1
    "@babel/plugin-transform-block-scoping": ^7.28.0
    "@babel/plugin-transform-class-properties": ^7.27.1
    "@babel/plugin-transform-class-static-block": ^7.27.1
    "@babel/plugin-transform-classes": ^7.28.0
    "@babel/plugin-transform-computed-properties": ^7.27.1
    "@babel/plugin-transform-destructuring": ^7.28.0
    "@babel/plugin-transform-dotall-regex": ^7.27.1
    "@babel/plugin-transform-duplicate-keys": ^7.27.1
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex": ^7.27.1
    "@babel/plugin-transform-dynamic-import": ^7.27.1
    "@babel/plugin-transform-explicit-resource-management": ^7.28.0
    "@babel/plugin-transform-exponentiation-operator": ^7.27.1
    "@babel/plugin-transform-export-namespace-from": ^7.27.1
    "@babel/plugin-transform-for-of": ^7.27.1
    "@babel/plugin-transform-function-name": ^7.27.1
    "@babel/plugin-transform-json-strings": ^7.27.1
    "@babel/plugin-transform-literals": ^7.27.1
    "@babel/plugin-transform-logical-assignment-operators": ^7.27.1
    "@babel/plugin-transform-member-expression-literals": ^7.27.1
    "@babel/plugin-transform-modules-amd": ^7.27.1
    "@babel/plugin-transform-modules-commonjs": ^7.27.1
    "@babel/plugin-transform-modules-systemjs": ^7.27.1
    "@babel/plugin-transform-modules-umd": ^7.27.1
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.27.1
    "@babel/plugin-transform-new-target": ^7.27.1
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.27.1
    "@babel/plugin-transform-numeric-separator": ^7.27.1
    "@babel/plugin-transform-object-rest-spread": ^7.28.0
    "@babel/plugin-transform-object-super": ^7.27.1
    "@babel/plugin-transform-optional-catch-binding": ^7.27.1
    "@babel/plugin-transform-optional-chaining": ^7.27.1
    "@babel/plugin-transform-parameters": ^7.27.7
    "@babel/plugin-transform-private-methods": ^7.27.1
    "@babel/plugin-transform-private-property-in-object": ^7.27.1
    "@babel/plugin-transform-property-literals": ^7.27.1
    "@babel/plugin-transform-regenerator": ^7.28.0
    "@babel/plugin-transform-regexp-modifiers": ^7.27.1
    "@babel/plugin-transform-reserved-words": ^7.27.1
    "@babel/plugin-transform-shorthand-properties": ^7.27.1
    "@babel/plugin-transform-spread": ^7.27.1
    "@babel/plugin-transform-sticky-regex": ^7.27.1
    "@babel/plugin-transform-template-literals": ^7.27.1
    "@babel/plugin-transform-typeof-symbol": ^7.27.1
    "@babel/plugin-transform-unicode-escapes": ^7.27.1
    "@babel/plugin-transform-unicode-property-regex": ^7.27.1
    "@babel/plugin-transform-unicode-regex": ^7.27.1
    "@babel/plugin-transform-unicode-sets-regex": ^7.27.1
    "@babel/preset-modules": 0.1.6-no-external-plugins
    babel-plugin-polyfill-corejs2: ^0.4.14
    babel-plugin-polyfill-corejs3: ^0.13.0
    babel-plugin-polyfill-regenerator: ^0.6.5
    core-js-compat: ^3.43.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 90399ed6350ac413fb507dc5c9e29e98d10684c4b7c7c6ae7b204bb91a7a9cd3bf8f944167a931a73112c8c820d0d1f42d4c15d7c4a7cf19196bf11c19663513
  languageName: node
  linkType: hard

"@babel/preset-flow@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/preset-flow@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-validator-option": ^7.27.1
    "@babel/plugin-transform-flow-strip-types": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f3f25b390debf72a6ff0170a2d5198aea344ba96f05eaca0bae2c7072119706fd46321604d89646bda1842527cfc6eab8828a983ec90149218d2120b9cd26596
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/types": ^7.4.4
    esutils: ^2.0.2
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 4855e799bc50f2449fb5210f78ea9e8fd46cf4f242243f1e2ed838e2bd702e25e73e822e7f8447722a5f4baa5e67a8f7a0e403f3e7ce04540ff743a9c411c375
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.22.15, @babel/preset-react@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/preset-react@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-validator-option": ^7.27.1
    "@babel/plugin-transform-react-display-name": ^7.27.1
    "@babel/plugin-transform-react-jsx": ^7.27.1
    "@babel/plugin-transform-react-jsx-development": ^7.27.1
    "@babel/plugin-transform-react-pure-annotations": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 00bc146f9c742eed804c598d3f31b7d889c1baf8c768989b7f84a93ca527dd1518d3b86781e89ca45cae6dbee136510d3a121658e01416c5578aecf751517bb5
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.23.0, @babel/preset-typescript@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/preset-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-validator-option": ^7.27.1
    "@babel/plugin-syntax-jsx": ^7.27.1
    "@babel/plugin-transform-modules-commonjs": ^7.27.1
    "@babel/plugin-transform-typescript": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 38020f1b23e88ec4fbffd5737da455d8939244bddfb48a2516aef93fb5947bd9163fb807ce6eff3e43fa5ffe9113aa131305fef0fb5053998410bbfcfe6ce0ec
  languageName: node
  linkType: hard

"@babel/register@npm:^7.24.6":
  version: 7.27.1
  resolution: "@babel/register@npm:7.27.1"
  dependencies:
    clone-deep: ^4.0.1
    find-cache-dir: ^2.0.0
    make-dir: ^2.1.0
    pirates: ^4.0.6
    source-map-support: ^0.5.16
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 154ab3075f245466bbd7a3f0cf972328365961a6f621ecb7795ba67e70243596138c264ac7cb79df4a93527318021b5edbab1df39b669afc83159a9e6e560770
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.18.6, @babel/runtime@npm:^7.20.0, @babel/runtime@npm:^7.25.0":
  version: 7.27.6
  resolution: "@babel/runtime@npm:7.27.6"
  checksum: 3f7b879df1823c0926bd5dbc941c62f5d60faa790c1aab9758c04799e1f04ee8d93553be9ec059d4e5882f19fe03cbe8933ee4f46212dced0f6d8205992c9c9a
  languageName: node
  linkType: hard

"@babel/template@npm:^7.25.0, @babel/template@npm:^7.27.1, @babel/template@npm:^7.27.2, @babel/template@npm:^7.3.3":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": ^7.27.1
    "@babel/parser": ^7.27.2
    "@babel/types": ^7.27.1
  checksum: ff5628bc066060624afd970616090e5bba91c6240c2e4b458d13267a523572cbfcbf549391eec8217b94b064cf96571c6273f0c04b28a8567b96edc675c28e27
  languageName: node
  linkType: hard

"@babel/traverse--for-generate-function-map@npm:@babel/traverse@^7.25.3, @babel/traverse@npm:^7.25.3, @babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/traverse@npm:7.28.0"
  dependencies:
    "@babel/code-frame": ^7.27.1
    "@babel/generator": ^7.28.0
    "@babel/helper-globals": ^7.28.0
    "@babel/parser": ^7.28.0
    "@babel/template": ^7.27.2
    "@babel/types": ^7.28.0
    debug: ^4.3.1
  checksum: f1b6ed2a37f593ee02db82521f8d54c8540a7ec2735c6c127ba687de306d62ac5a7c6471819783128e0b825c4f7e374206ebbd1daf00d07f05a4528f5b1b4c07
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.25.2, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.6, @babel/types@npm:^7.28.0, @babel/types@npm:^7.3.3, @babel/types@npm:^7.4.4":
  version: 7.28.0
  resolution: "@babel/types@npm:7.28.0"
  dependencies:
    "@babel/helper-string-parser": ^7.27.1
    "@babel/helper-validator-identifier": ^7.27.1
  checksum: 3cb33bbe79e9629c3e4ed1592340f936481e7aef2c3df11f8b1f91e54b45e89b3ad92f2d20f8acdb5a7e00157174ffe8b1d174069bb839303e7f39f579d60969
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 850f9305536d0f2bd13e9e0881cb5f02e4f93fad1189f7b2d4bebf694e3206924eadee1068130d43c11b750efcc9405f88a8e42ef098b6d75239c0f047de1a27
  languageName: node
  linkType: hard

"@commitlint/cli@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/cli@npm:19.8.1"
  dependencies:
    "@commitlint/format": ^19.8.1
    "@commitlint/lint": ^19.8.1
    "@commitlint/load": ^19.8.1
    "@commitlint/read": ^19.8.1
    "@commitlint/types": ^19.8.1
    tinyexec: ^1.0.0
    yargs: ^17.0.0
  bin:
    commitlint: ./cli.js
  checksum: 0ad393d0a7be57044c9822de065f35375fe0c2331f9a6c044ce20f0316aa3ab1a1b679040ba130ff60b180d54738c26d81d7afb47d9da355ba25161b1a5a91dd
  languageName: node
  linkType: hard

"@commitlint/config-conventional@npm:^19.6.0":
  version: 19.8.1
  resolution: "@commitlint/config-conventional@npm:19.8.1"
  dependencies:
    "@commitlint/types": ^19.8.1
    conventional-changelog-conventionalcommits: ^7.0.2
  checksum: f17e855b7293391655b7d05cf2e0ed43f5d03e48ad050a19c2a21ef1fc6516e8b9f2a386ed53ce93a2d2edc4464fc5081319710bd9134ec6feb6ccfcb2e7616c
  languageName: node
  linkType: hard

"@commitlint/config-validator@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/config-validator@npm:19.8.1"
  dependencies:
    "@commitlint/types": ^19.8.1
    ajv: ^8.11.0
  checksum: 26eee15c1c0564fc8857b4bbc4f06305a32e049a724ede73753f66fc15316eb79a5dde4c8e2765bd75952a27b138cd80cffc49491281f122b834f8467c658d80
  languageName: node
  linkType: hard

"@commitlint/ensure@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/ensure@npm:19.8.1"
  dependencies:
    "@commitlint/types": ^19.8.1
    lodash.camelcase: ^4.3.0
    lodash.kebabcase: ^4.1.1
    lodash.snakecase: ^4.1.1
    lodash.startcase: ^4.4.0
    lodash.upperfirst: ^4.3.1
  checksum: af342f61b246c301937cc03477c64b86ca6dea47de23f94d237181d346d020ec23c8a458f56aec8bfe9cdcb80a06adcc34964f32c05a2649282a959ce6fae39d
  languageName: node
  linkType: hard

"@commitlint/execute-rule@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/execute-rule@npm:19.8.1"
  checksum: a39d9a87c0962c290e4f7d7438e8fca7642384a5aa97ec84c0b3dbbf91dc048496dd25447ba3dbec37b00006eec1951f8f22f30a98448e90e22d44d585d8a68f
  languageName: node
  linkType: hard

"@commitlint/format@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/format@npm:19.8.1"
  dependencies:
    "@commitlint/types": ^19.8.1
    chalk: ^5.3.0
  checksum: 5af80e489c1470e20519780867145492c145690bd8e6b0dc049f53d317b045fa39ba012faed2715307e105ca439e6b16bdd4fe9c39c146d38bb5d93f1542fc5f
  languageName: node
  linkType: hard

"@commitlint/is-ignored@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/is-ignored@npm:19.8.1"
  dependencies:
    "@commitlint/types": ^19.8.1
    semver: ^7.6.0
  checksum: a70631bb7825ed49f2d6164c7547d025ca184a5e65eb7b1bd63f041ae7aa9189991c2dbef18b1160951aeb59595307b75d5ba151ea10e0de4d36f22709b9c877
  languageName: node
  linkType: hard

"@commitlint/lint@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/lint@npm:19.8.1"
  dependencies:
    "@commitlint/is-ignored": ^19.8.1
    "@commitlint/parse": ^19.8.1
    "@commitlint/rules": ^19.8.1
    "@commitlint/types": ^19.8.1
  checksum: adf5fb6e68c9b6301243dce251be47884e4c2d6ee1f43e6aa0a31a054d2bd85880b4f2941781e13290e3b88b4f6da4b9b1978b9117444a8c89beb6f310e95951
  languageName: node
  linkType: hard

"@commitlint/load@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/load@npm:19.8.1"
  dependencies:
    "@commitlint/config-validator": ^19.8.1
    "@commitlint/execute-rule": ^19.8.1
    "@commitlint/resolve-extends": ^19.8.1
    "@commitlint/types": ^19.8.1
    chalk: ^5.3.0
    cosmiconfig: ^9.0.0
    cosmiconfig-typescript-loader: ^6.1.0
    lodash.isplainobject: ^4.0.6
    lodash.merge: ^4.6.2
    lodash.uniq: ^4.5.0
  checksum: e78c997ef529f80f8b62f686e553d0f2cb33d88b8b907d2e3890195851cd783fd44bd780addaa49f1cceb12ed073c10bb10e11dc082f51e4fdc54640f5ac1cca
  languageName: node
  linkType: hard

"@commitlint/message@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/message@npm:19.8.1"
  checksum: e365590dd539fe2519a15bd99ee8499c3ffbd80852839783ae6fd0b65feef08b26d2134a4e9ea32e006c2b3aa04447a38b011e73975b4fc3d7c7380a0fbf2377
  languageName: node
  linkType: hard

"@commitlint/parse@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/parse@npm:19.8.1"
  dependencies:
    "@commitlint/types": ^19.8.1
    conventional-changelog-angular: ^7.0.0
    conventional-commits-parser: ^5.0.0
  checksum: f6264bb30399b420a875532905e18049b4ab6f24d79f42d20fa06e64b9f355649ac18a33874e02643f0a826f3cec69423d6bc96cf852fa692338603ce910a95f
  languageName: node
  linkType: hard

"@commitlint/read@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/read@npm:19.8.1"
  dependencies:
    "@commitlint/top-level": ^19.8.1
    "@commitlint/types": ^19.8.1
    git-raw-commits: ^4.0.0
    minimist: ^1.2.8
    tinyexec: ^1.0.0
  checksum: ee0f42e2e5a3ade673b2d14f3b2056a86804afe7d09b6703d51b41edc099b33b9c09dc715b30d7113879999381a198d78b4fcbc649785ed3beb9c3f7d1dc2bb2
  languageName: node
  linkType: hard

"@commitlint/resolve-extends@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/resolve-extends@npm:19.8.1"
  dependencies:
    "@commitlint/config-validator": ^19.8.1
    "@commitlint/types": ^19.8.1
    global-directory: ^4.0.1
    import-meta-resolve: ^4.0.0
    lodash.mergewith: ^4.6.2
    resolve-from: ^5.0.0
  checksum: d1415e1bff196a2f1ee18e2ba41764cb2855adda2e8221bb0d20d8d365c9a4777ad99b8babd0959aec8ac6fe8de6be7b928d5e3c38cb458c92c73a195b52bff7
  languageName: node
  linkType: hard

"@commitlint/rules@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/rules@npm:19.8.1"
  dependencies:
    "@commitlint/ensure": ^19.8.1
    "@commitlint/message": ^19.8.1
    "@commitlint/to-lines": ^19.8.1
    "@commitlint/types": ^19.8.1
  checksum: dc3a90b4561369991b851224c5cc1c0e2297c68ce148e21a7a5893a0556fffced192d59bf491a6c80270da012840fafdb34d991b7048170f4b2e7b0122211cee
  languageName: node
  linkType: hard

"@commitlint/to-lines@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/to-lines@npm:19.8.1"
  checksum: 47f33d5e0d77aa0cc2fc14daa3e73661c64c9cffb5fc9c723714ced4fcfc758bf5ba2e084143fa55bc512ad896d115b9983a308a97a005200484f04f2ed0fd90
  languageName: node
  linkType: hard

"@commitlint/top-level@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/top-level@npm:19.8.1"
  dependencies:
    find-up: ^7.0.0
  checksum: c875b6c1be495675c77d86e80419d27fd5eb70fc061ef412d041541219c3222d9c4dbd6f0353247d49e9b2cd6d86a7ffc9df1ba20f96c77726c1f9a0edeeb8fe
  languageName: node
  linkType: hard

"@commitlint/types@npm:^19.8.1":
  version: 19.8.1
  resolution: "@commitlint/types@npm:19.8.1"
  dependencies:
    "@types/conventional-commits-parser": ^5.0.0
    chalk: ^5.3.0
  checksum: d1943a5789a02c75b0c72755673ab8d50c850b025abb7806b7eef83b373591948f5d1d9cd22022f89302a256546934d797445913c5c495d8e92711cf17b0fbf0
  languageName: node
  linkType: hard

"@conventional-changelog/git-client@npm:^1.0.0":
  version: 1.0.1
  resolution: "@conventional-changelog/git-client@npm:1.0.1"
  dependencies:
    "@types/semver": ^7.5.5
    semver: ^7.5.2
  peerDependencies:
    conventional-commits-filter: ^5.0.0
    conventional-commits-parser: ^6.0.0
  peerDependenciesMeta:
    conventional-commits-filter:
      optional: true
    conventional-commits-parser:
      optional: true
  checksum: 4be45d4d1335878772fe0ad6e279970569c9526b544af3f58d31d70199f40c8051459a22f02d87c458a7c95f0ba68cd9a839da19504c5c40045c5b0691354305
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: ^3.4.3
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: b177e3b75c0b8d0e5d71f1c532edb7e40b31313db61f0c879f9bf19c3abb2783c6c372b5deb2396dab4432f2946b9972122ac682e77010376c029dfd0149c681
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 0d628680e204bc316d545b4993d3658427ca404ae646ce541fcc65306b8c712c340e5e573e30fb9f85f4855c0c5f6dca9868931f2fcced06417fbe1a0c6cd2d6
  languageName: node
  linkType: hard

"@eslint/compat@npm:^1.2.7":
  version: 1.3.1
  resolution: "@eslint/compat@npm:1.3.1"
  peerDependencies:
    eslint: ^8.40 || 9
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 1df5594c02bbba3c1d7f7887e40e76a53967836a72048b9094bf501efe75c12fd5de6318ca005d97672d6590eb46d24f2197505015b52ae9c7c66c2ae19d9d35
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.21.0":
  version: 0.21.0
  resolution: "@eslint/config-array@npm:0.21.0"
  dependencies:
    "@eslint/object-schema": ^2.1.6
    debug: ^4.3.1
    minimatch: ^3.1.2
  checksum: 84d3ae7cb755af94dc158a74389f4c560757b13f2bb908f598f927b87b70a38e8152015ea2e9557c1b4afc5130ee1356f6cad682050d67aae0468bbef98bc3a8
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.3.0":
  version: 0.3.0
  resolution: "@eslint/config-helpers@npm:0.3.0"
  checksum: d4fe8242ef580806ddaa88309f4bb2d3e6be5524cc6d6197675106c6d048f766a3f9cdc2e8e33bbc97a123065792cac8314fc85ac2b3cf72610e8df59301d63a
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.14.0":
  version: 0.14.0
  resolution: "@eslint/core@npm:0.14.0"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: d68b8282b6f38c5145234f812f18f491d12d716240875591bd54bf5ac32858d979bdf6d38e521997a6e01f2c4223a3e66049714151da7278d0a95ff15b5d46c8
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.15.1":
  version: 0.15.1
  resolution: "@eslint/core@npm:0.15.1"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: 9215f00466d60764453466604443a491b0ea8263c148836fef723354d6ef1d550991e931d3df2780c99cee2cab14c4f41f97d5341ab12a8443236c961bb6f664
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.3.0, @eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^10.0.1
    globals: ^14.0.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: 8241f998f0857abf5a615072273b90b1244d75c1c45d217c6a8eb444c6e12bbb5506b4879c14fb262eb72b7d8e3d2f0542da2db1a7f414a12496ebb790fb4d62
  languageName: node
  linkType: hard

"@eslint/js@npm:9.30.1, @eslint/js@npm:^9.22.0":
  version: 9.30.1
  resolution: "@eslint/js@npm:9.30.1"
  checksum: 596adcd4336f098121b4f3f336169dabe86ca8d34b9fb4e30c9c44ccbb10def931bdbbd92cd910776c4030a05ae614fbc89fc8d09f69f5bad2795cd7157678e8
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: e32e565319f6544d36d3fa69a3e163120722d12d666d1a4525c9a6f02e9b54c29d9b1f03139e25d7e759e08dda8da433590bc23c09db8d511162157ef1b86a4c
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.1":
  version: 0.3.3
  resolution: "@eslint/plugin-kit@npm:0.3.3"
  dependencies:
    "@eslint/core": ^0.15.1
    levn: ^0.4.1
  checksum: c9dc7b83ed011dce35ccc66dc53aaaa87e9fb2bd7c8a11231f7624334d82c9a53552e4b1a1cb60b74073fcc49a2661be874e503aae14cf2f6ac6b1c7faeb7080
  languageName: node
  linkType: hard

"@evilmartians/lefthook@npm:^1.5.0":
  version: 1.11.14
  resolution: "@evilmartians/lefthook@npm:1.11.14"
  bin:
    lefthook: bin/index.js
  checksum: e934095f95a2ed93f18b859677ef35a630ce0d950f439c1191ca94b574373329fa5fa5272e330d3b6c6a3dde4296e9909370d0465e26dac60136dc2a0b8ecc0d
  conditions: (os=darwin | os=linux | os=win32) & (cpu=x64 | cpu=arm64 | cpu=ia32)
  languageName: node
  linkType: hard

"@expo/cli@npm:0.24.17":
  version: 0.24.17
  resolution: "@expo/cli@npm:0.24.17"
  dependencies:
    "@0no-co/graphql.web": ^1.0.8
    "@babel/runtime": ^7.20.0
    "@expo/code-signing-certificates": ^0.0.5
    "@expo/config": ~11.0.11
    "@expo/config-plugins": ~10.1.0
    "@expo/devcert": ^1.1.2
    "@expo/env": ~1.0.6
    "@expo/image-utils": ^0.7.5
    "@expo/json-file": ^9.1.4
    "@expo/metro-config": ~0.20.16
    "@expo/osascript": ^2.2.4
    "@expo/package-manager": ^1.8.5
    "@expo/plist": ^0.3.4
    "@expo/prebuild-config": ^9.0.9
    "@expo/spawn-async": ^1.7.2
    "@expo/ws-tunnel": ^1.0.1
    "@expo/xcpretty": ^4.3.0
    "@react-native/dev-middleware": 0.79.5
    "@urql/core": ^5.0.6
    "@urql/exchange-retry": ^1.3.0
    accepts: ^1.3.8
    arg: ^5.0.2
    better-opn: ~3.0.2
    bplist-creator: 0.1.0
    bplist-parser: ^0.3.1
    chalk: ^4.0.0
    ci-info: ^3.3.0
    compression: ^1.7.4
    connect: ^3.7.0
    debug: ^4.3.4
    env-editor: ^0.4.1
    freeport-async: ^2.0.0
    getenv: ^2.0.0
    glob: ^10.4.2
    lan-network: ^0.1.6
    minimatch: ^9.0.0
    node-forge: ^1.3.1
    npm-package-arg: ^11.0.0
    ora: ^3.4.0
    picomatch: ^3.0.1
    pretty-bytes: ^5.6.0
    pretty-format: ^29.7.0
    progress: ^2.0.3
    prompts: ^2.3.2
    qrcode-terminal: 0.11.0
    require-from-string: ^2.0.2
    requireg: ^0.2.2
    resolve: ^1.22.2
    resolve-from: ^5.0.0
    resolve.exports: ^2.0.3
    semver: ^7.6.0
    send: ^0.19.0
    slugify: ^1.3.4
    source-map-support: ~0.5.21
    stacktrace-parser: ^0.1.10
    structured-headers: ^0.4.1
    tar: ^7.4.3
    terminal-link: ^2.1.1
    undici: ^6.18.2
    wrap-ansi: ^7.0.0
    ws: ^8.12.1
  bin:
    expo-internal: build/bin/cli
  checksum: 1b3c8c7f0d4a7b5ecf8615fc3df3d4cbd5d5278f42ffbef98eeefd36de3d2444bc8595657cac6df8d29d849134e6b26d45175c47f3bda96f1b57548981895292
  languageName: node
  linkType: hard

"@expo/code-signing-certificates@npm:^0.0.5":
  version: 0.0.5
  resolution: "@expo/code-signing-certificates@npm:0.0.5"
  dependencies:
    node-forge: ^1.2.1
    nullthrows: ^1.1.1
  checksum: 4a1c73a6bc74443284a45db698ede874c7d47f6ed58cf44adaa255139490c8754d81dc1556247f3782cdc5034382fb72f23b0033daa2117facad4eb13b841e37
  languageName: node
  linkType: hard

"@expo/config-plugins@npm:~10.0.3":
  version: 10.0.3
  resolution: "@expo/config-plugins@npm:10.0.3"
  dependencies:
    "@expo/config-types": ^53.0.4
    "@expo/json-file": ~9.1.4
    "@expo/plist": ^0.3.4
    "@expo/sdk-runtime-versions": ^1.0.0
    chalk: ^4.1.2
    debug: ^4.3.5
    getenv: ^2.0.0
    glob: ^10.4.2
    resolve-from: ^5.0.0
    semver: ^7.5.4
    slash: ^3.0.0
    slugify: ^1.6.6
    xcode: ^3.0.1
    xml2js: 0.6.0
  checksum: 05b46e1a6b24be341179ee2394da0fd3f907d58954e7e9d1872c4424ba98fe7752ede65e773961e9fcc11e7f25c3f1f201e4c9806783f7aa1e21e597b103c545
  languageName: node
  linkType: hard

"@expo/config-plugins@npm:~10.1.0":
  version: 10.1.0
  resolution: "@expo/config-plugins@npm:10.1.0"
  dependencies:
    "@expo/config-types": ^53.0.4
    "@expo/json-file": ~9.1.4
    "@expo/plist": ^0.3.4
    "@expo/sdk-runtime-versions": ^1.0.0
    chalk: ^4.1.2
    debug: ^4.3.5
    getenv: ^2.0.0
    glob: ^10.4.2
    resolve-from: ^5.0.0
    semver: ^7.5.4
    slash: ^3.0.0
    slugify: ^1.6.6
    xcode: ^3.0.1
    xml2js: 0.6.0
  checksum: 7c19b5fc65c4c8f9a4ad0f3503917e4354ce4919b74668e503b18e4795f93a23d7c37d8e66a6766dc9ea8f3da22f0082f7f2d24d6e77792cba4ecec6b91d84d2
  languageName: node
  linkType: hard

"@expo/config-types@npm:^53.0.4":
  version: 53.0.4
  resolution: "@expo/config-types@npm:53.0.4"
  checksum: 634373b182703603a62c744ac5d41061e957a14de6fc3b2924288257d391273f73f9722cd413e3e3006b2d6e84f0a552227ab09771b4f6bc93e0dccfb06a20aa
  languageName: node
  linkType: hard

"@expo/config@npm:~11.0.11, @expo/config@npm:~11.0.9":
  version: 11.0.11
  resolution: "@expo/config@npm:11.0.11"
  dependencies:
    "@babel/code-frame": ~7.10.4
    "@expo/config-plugins": ~10.0.3
    "@expo/config-types": ^53.0.4
    "@expo/json-file": ^9.1.4
    deepmerge: ^4.3.1
    getenv: ^2.0.0
    glob: ^10.4.2
    require-from-string: ^2.0.2
    resolve-from: ^5.0.0
    resolve-workspace-root: ^2.0.0
    semver: ^7.6.0
    slugify: ^1.3.4
    sucrase: 3.35.0
  checksum: 19aeff6f81bc4621d1314a6345791165702628663fa2cd5edeab6e3d083eeb684fb8461eee7c50e67ec923d38d074f31d6ac5dee4915aa940d23e7a4d465e956
  languageName: node
  linkType: hard

"@expo/devcert@npm:^1.1.2":
  version: 1.2.0
  resolution: "@expo/devcert@npm:1.2.0"
  dependencies:
    "@expo/sudo-prompt": ^9.3.1
    debug: ^3.1.0
    glob: ^10.4.2
  checksum: e35d63de8bd3512215b259be75dbb7836ecb8885f94b037fbca5923bf9b3b8391cb8cc28f85c0e4e175b0696d1ea18e720ceb72f21b50ffdab25d750edf99178
  languageName: node
  linkType: hard

"@expo/env@npm:~1.0.5, @expo/env@npm:~1.0.6":
  version: 1.0.6
  resolution: "@expo/env@npm:1.0.6"
  dependencies:
    chalk: ^4.0.0
    debug: ^4.3.4
    dotenv: ~16.4.5
    dotenv-expand: ~11.0.6
    getenv: ^2.0.0
  checksum: 7edad3a8f17193dbcba83a2596f05cc2770082ee5e13faf4cf5d1b3b26d4535afa8d89791a79106ea9b59c24bb1e859512bcc722c2888e582ab125a692b1f189
  languageName: node
  linkType: hard

"@expo/fingerprint@npm:0.13.3":
  version: 0.13.3
  resolution: "@expo/fingerprint@npm:0.13.3"
  dependencies:
    "@expo/spawn-async": ^1.7.2
    arg: ^5.0.2
    chalk: ^4.1.2
    debug: ^4.3.4
    find-up: ^5.0.0
    getenv: ^2.0.0
    glob: ^10.4.2
    ignore: ^5.3.1
    minimatch: ^9.0.0
    p-limit: ^3.1.0
    resolve-from: ^5.0.0
    semver: ^7.6.0
  bin:
    fingerprint: bin/cli.js
  checksum: 5b5d8c1c28cb7efd1f6330e4dbafa100997aed2f626b638eab19b2d115f7e348dd7e9f5725c8265279b6696416ed19cf3aaa4a57d70c76e8245a7eba9c391682
  languageName: node
  linkType: hard

"@expo/image-utils@npm:^0.7.5":
  version: 0.7.5
  resolution: "@expo/image-utils@npm:0.7.5"
  dependencies:
    "@expo/spawn-async": ^1.7.2
    chalk: ^4.0.0
    getenv: ^2.0.0
    jimp-compact: 0.16.1
    parse-png: ^2.1.0
    resolve-from: ^5.0.0
    semver: ^7.6.0
    temp-dir: ~2.0.0
    unique-string: ~2.0.0
  checksum: 4f2085c243a9066e0f8450b8ffea0151a0f5549181c7e54cb69de7f26c8d98afe396f957cf39b3c2bbc88b0266d114fbcc0c201fc8a22b2c79bccf28466e1360
  languageName: node
  linkType: hard

"@expo/json-file@npm:^9.1.4, @expo/json-file@npm:~9.1.4":
  version: 9.1.4
  resolution: "@expo/json-file@npm:9.1.4"
  dependencies:
    "@babel/code-frame": ~7.10.4
    json5: ^2.2.3
  checksum: 31337f829276c1c859993a168f067048ec60bc09d89e45aa774570e09a2edc843e78fb6dcdbc7d186d7dc62f3b84035659a6e5689093e5813bdaf08db6de5a40
  languageName: node
  linkType: hard

"@expo/metro-config@npm:0.20.16, @expo/metro-config@npm:~0.20.16":
  version: 0.20.16
  resolution: "@expo/metro-config@npm:0.20.16"
  dependencies:
    "@babel/core": ^7.20.0
    "@babel/generator": ^7.20.5
    "@babel/parser": ^7.20.0
    "@babel/types": ^7.20.0
    "@expo/config": ~11.0.11
    "@expo/env": ~1.0.6
    "@expo/json-file": ~9.1.4
    "@expo/spawn-async": ^1.7.2
    chalk: ^4.1.0
    debug: ^4.3.2
    dotenv: ~16.4.5
    dotenv-expand: ~11.0.6
    getenv: ^2.0.0
    glob: ^10.4.2
    jsc-safe-url: ^0.2.4
    lightningcss: ~1.27.0
    minimatch: ^9.0.0
    postcss: ~8.4.32
    resolve-from: ^5.0.0
  checksum: ddd4fd281c418c8a092d187a9ec6202611bd2d1ff426bedcff8931bde855abcff3fa1ecc0b62f1f6a9563f49efb6f0c7bffc1d9f2929444421cd2a33c364b48a
  languageName: node
  linkType: hard

"@expo/metro-runtime@npm:~5.0.4":
  version: 5.0.4
  resolution: "@expo/metro-runtime@npm:5.0.4"
  peerDependencies:
    react-native: "*"
  checksum: da624a75964a16b20ca06b1ab83a96b841f64fa2aa69ed25dd283959404ce8e5f2dbc1141c3c3f1bf031a0f3588b62d8293169d3eb057c14a768bcd5c1f8f954
  languageName: node
  linkType: hard

"@expo/osascript@npm:^2.2.4":
  version: 2.2.4
  resolution: "@expo/osascript@npm:2.2.4"
  dependencies:
    "@expo/spawn-async": ^1.7.2
    exec-async: ^2.2.0
  checksum: a0c24615e6cb6a8ae8b9d8c5df37156137ca9fb71e2a5e2bec9f98ab0bca5738554f5bbddcd5b73f03445f5f705c96437d3dc30221ac586f52f3f209a6893a25
  languageName: node
  linkType: hard

"@expo/package-manager@npm:^1.8.5":
  version: 1.8.5
  resolution: "@expo/package-manager@npm:1.8.5"
  dependencies:
    "@expo/json-file": ^9.1.4
    "@expo/spawn-async": ^1.7.2
    chalk: ^4.0.0
    npm-package-arg: ^11.0.0
    ora: ^3.4.0
    resolve-workspace-root: ^2.0.0
  checksum: 028ad9e8871133a9e8e249c885267893be7f80927f6893d29fae86e8e8d0bb7d078cf792b18600b1326dcb2a0d67145ff05d70ea4bfdd212b4032ea05d3ae0c5
  languageName: node
  linkType: hard

"@expo/plist@npm:^0.3.4":
  version: 0.3.4
  resolution: "@expo/plist@npm:0.3.4"
  dependencies:
    "@xmldom/xmldom": ^0.8.8
    base64-js: ^1.2.3
    xmlbuilder: ^15.1.1
  checksum: b39ba79f691e08b14cda4b0ec6352656bdc2762fabbed24c2e4e372b0c644bf86179e8bc51de1383cf4896d4ea3e1a68d9e5dbf3d3e0aa514f704cc87f6cd3a7
  languageName: node
  linkType: hard

"@expo/prebuild-config@npm:^9.0.9":
  version: 9.0.9
  resolution: "@expo/prebuild-config@npm:9.0.9"
  dependencies:
    "@expo/config": ~11.0.11
    "@expo/config-plugins": ~10.1.0
    "@expo/config-types": ^53.0.4
    "@expo/image-utils": ^0.7.5
    "@expo/json-file": ^9.1.4
    "@react-native/normalize-colors": 0.79.5
    debug: ^4.3.1
    resolve-from: ^5.0.0
    semver: ^7.6.0
    xml2js: 0.6.0
  checksum: a2593b40537e4b26af56a1de6f67271bce7153b1b54a2d0856784d0ec818879411bf757c8b86917b45d192f391a2aff30f3cccf3465391a73da454adf0dfeefa
  languageName: node
  linkType: hard

"@expo/sdk-runtime-versions@npm:^1.0.0":
  version: 1.0.0
  resolution: "@expo/sdk-runtime-versions@npm:1.0.0"
  checksum: 0942d5a356f590e8dc795761456cc48b3e2d6a38ad2a02d6774efcdc5a70424e05623b4e3e5d2fec0cdc30f40dde05c14391c781607eed3971bf8676518bfd9d
  languageName: node
  linkType: hard

"@expo/spawn-async@npm:^1.7.2":
  version: 1.7.2
  resolution: "@expo/spawn-async@npm:1.7.2"
  dependencies:
    cross-spawn: ^7.0.3
  checksum: d99e5ff6d303ec9b0105f97c4fa6c65bca526c7d4d0987997c35cc745fa8224adf009942d01808192ebb9fa30619a53316641958631e85cf17b773d9eeda2597
  languageName: node
  linkType: hard

"@expo/sudo-prompt@npm:^9.3.1":
  version: 9.3.2
  resolution: "@expo/sudo-prompt@npm:9.3.2"
  checksum: 5db5385d7ecaadee7ef768c56ed882ae1b266e4c390325f36967382edefaf6cc2e7845b12b35a91d3ad83b05868548acefe8a015aef3a47d91a2cfc5a0a3cc84
  languageName: node
  linkType: hard

"@expo/vector-icons@npm:^14.0.0":
  version: 14.1.0
  resolution: "@expo/vector-icons@npm:14.1.0"
  peerDependencies:
    expo-font: "*"
    react: "*"
    react-native: "*"
  checksum: 1704db7bc30cf0d8aa6b139bad5ec4fc4e6b3fc576e9bf37d6c40212f4c5c3160c54719f4ac3c3f2f2a59d7ff2a11f7cb23419b586e3bfb128e407943d7fbdfa
  languageName: node
  linkType: hard

"@expo/ws-tunnel@npm:^1.0.1":
  version: 1.0.6
  resolution: "@expo/ws-tunnel@npm:1.0.6"
  checksum: 0db9d5b94cfedfad7784cfd876bafbf9575d0cb00bb537f57954fa8fe6d7151f95b2fa0aa6071b7cc7ab49e3a68bdf647acbc323d7d6b23f07df21f97485ee4f
  languageName: node
  linkType: hard

"@expo/xcpretty@npm:^4.3.0":
  version: 4.3.2
  resolution: "@expo/xcpretty@npm:4.3.2"
  dependencies:
    "@babel/code-frame": 7.10.4
    chalk: ^4.1.0
    find-up: ^5.0.0
    js-yaml: ^4.1.0
  bin:
    excpretty: build/cli.js
  checksum: 8771b2812f0dfc49f6dab4338c986beaf4cf2ec20ed8fd598be6e3803fcbfc0a337dbb5b4dad9556b85ba2489f63c777735ad2c2ee6f5842ff68b9322e47f6a3
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 611e0545146f55ddfdd5c20239cfb7911f9d0e28258787c4fc1a1f6214250830c9367aaaeace0096ed90b6739bee1e9c52ad5ba8adaf74ab8b449119303babfe
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": ^0.19.1
    "@humanwhocodes/retry": ^0.3.0
  checksum: f9cb52bb235f8b9c6fcff43a7e500669a38f8d6ce26593404a9b56365a1644e0ed60c720dc65ff6a696b1f85f3563ab055bb554ec8674f2559085ba840e47710
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 7e5517bb51dbea3e02ab6cacef59a8f4b0ca023fc4b0b8cbc40de0ad29f46edd50b897c6e7fba79366a0217e3f48e2da8975056f6c35cfe19d9cc48f1d03c1dd
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: d423455b9d53cf01f778603404512a4246fb19b83e74fe3e28c70d9a80e9d4ae147d2411628907ca983e91a855a52535859a8bb218050bc3f6dbd7a553b7b442
  languageName: node
  linkType: hard

"@hutson/parse-repository-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "@hutson/parse-repository-url@npm:5.0.0"
  checksum: 8adce66fd62e339382191f32a90708fab4c65560124b67a06262c57815706944a2f894d33f9bd8dd97180fd80accc0c3d1d5b5138ab86ed10ee071cb487d5983
  languageName: node
  linkType: hard

"@iarna/toml@npm:2.2.5":
  version: 2.2.5
  resolution: "@iarna/toml@npm:2.2.5"
  checksum: b63b2b2c4fd67969a6291543ada0303d45593801ee744b60f5390f183c03d9192bc67a217abb24be945158f1935f02840d9ffff40c0142aa171b5d3b6b6a3ea5
  languageName: node
  linkType: hard

"@inquirer/figures@npm:^1.0.3":
  version: 1.0.12
  resolution: "@inquirer/figures@npm:1.0.12"
  checksum: db4446e45adb921686bda06ee3bfb0e96d0b656569392613042c67e7ba4b4b15c04459b22e2e2a9ef3750b34b7fcab6a784114c64922d3d211558cc8b5458027
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@isaacs/ttlcache@npm:^1.4.1":
  version: 1.4.1
  resolution: "@isaacs/ttlcache@npm:1.4.1"
  checksum: b99f0918faf1eba405b6bc3421584282b2edc46cca23f8d8e112a643bf6e4506c6c53a4525901118e229d19c5719bbec3028ec438d758fd71081f6c32af871ec
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: ^5.3.1
    find-up: ^4.1.0
    get-package-type: ^0.1.0
    js-yaml: ^3.13.1
    resolve-from: ^5.0.0
  checksum: d578da5e2e804d5c93228450a1380e1a3c691de4953acc162f387b717258512a3e07b83510a936d9fab03eac90817473917e24f5d16297af3867f59328d58568
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 5282759d961d61350f33d9118d16bcaed914ebf8061a52f4fa474b2cb08720c9c81d165e13b82f2e5a8a212cc5af482f0c6fc1ac27b9e067e5394c9a6ed186c9
  languageName: node
  linkType: hard

"@jest/console@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/console@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    jest-message-util: ^29.7.0
    jest-util: ^29.7.0
    slash: ^3.0.0
  checksum: 0e3624e32c5a8e7361e889db70b170876401b7d70f509a2538c31d5cd50deb0c1ae4b92dc63fe18a0902e0a48c590c21d53787a0df41a52b34fa7cab96c384d6
  languageName: node
  linkType: hard

"@jest/core@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/core@npm:29.7.0"
  dependencies:
    "@jest/console": ^29.7.0
    "@jest/reporters": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    ci-info: ^3.2.0
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    jest-changed-files: ^29.7.0
    jest-config: ^29.7.0
    jest-haste-map: ^29.7.0
    jest-message-util: ^29.7.0
    jest-regex-util: ^29.6.3
    jest-resolve: ^29.7.0
    jest-resolve-dependencies: ^29.7.0
    jest-runner: ^29.7.0
    jest-runtime: ^29.7.0
    jest-snapshot: ^29.7.0
    jest-util: ^29.7.0
    jest-validate: ^29.7.0
    jest-watcher: ^29.7.0
    micromatch: ^4.0.4
    pretty-format: ^29.7.0
    slash: ^3.0.0
    strip-ansi: ^6.0.0
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: af759c9781cfc914553320446ce4e47775ae42779e73621c438feb1e4231a5d4862f84b1d8565926f2d1aab29b3ec3dcfdc84db28608bdf5f29867124ebcfc0d
  languageName: node
  linkType: hard

"@jest/create-cache-key-function@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/create-cache-key-function@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
  checksum: 681bc761fa1d6fa3dd77578d444f97f28296ea80755e90e46d1c8fa68661b9e67f54dd38b988742db636d26cf160450dc6011892cec98b3a7ceb58cad8ff3aae
  languageName: node
  linkType: hard

"@jest/environment@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/environment@npm:29.7.0"
  dependencies:
    "@jest/fake-timers": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    jest-mock: ^29.7.0
  checksum: 6fb398143b2543d4b9b8d1c6dbce83fa5247f84f550330604be744e24c2bd2178bb893657d62d1b97cf2f24baf85c450223f8237cccb71192c36a38ea2272934
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect-utils@npm:29.7.0"
  dependencies:
    jest-get-type: ^29.6.3
  checksum: 75eb177f3d00b6331bcaa057e07c0ccb0733a1d0a1943e1d8db346779039cb7f103789f16e502f888a3096fb58c2300c38d1f3748b36a7fa762eb6f6d1b160ed
  languageName: node
  linkType: hard

"@jest/expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect@npm:29.7.0"
  dependencies:
    expect: ^29.7.0
    jest-snapshot: ^29.7.0
  checksum: a01cb85fd9401bab3370618f4b9013b90c93536562222d920e702a0b575d239d74cecfe98010aaec7ad464f67cf534a353d92d181646a4b792acaa7e912ae55e
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/fake-timers@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@sinonjs/fake-timers": ^10.0.2
    "@types/node": "*"
    jest-message-util: ^29.7.0
    jest-mock: ^29.7.0
    jest-util: ^29.7.0
  checksum: caf2bbd11f71c9241b458d1b5a66cbe95debc5a15d96442444b5d5c7ba774f523c76627c6931cca5e10e76f0d08761f6f1f01a608898f4751a0eee54fc3d8d00
  languageName: node
  linkType: hard

"@jest/globals@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/globals@npm:29.7.0"
  dependencies:
    "@jest/environment": ^29.7.0
    "@jest/expect": ^29.7.0
    "@jest/types": ^29.6.3
    jest-mock: ^29.7.0
  checksum: 97dbb9459135693ad3a422e65ca1c250f03d82b2a77f6207e7fa0edd2c9d2015fbe4346f3dc9ebff1678b9d8da74754d4d440b7837497f8927059c0642a22123
  languageName: node
  linkType: hard

"@jest/reporters@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/reporters@npm:29.7.0"
  dependencies:
    "@bcoe/v8-coverage": ^0.2.3
    "@jest/console": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    "@jridgewell/trace-mapping": ^0.3.18
    "@types/node": "*"
    chalk: ^4.0.0
    collect-v8-coverage: ^1.0.0
    exit: ^0.1.2
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    istanbul-lib-coverage: ^3.0.0
    istanbul-lib-instrument: ^6.0.0
    istanbul-lib-report: ^3.0.0
    istanbul-lib-source-maps: ^4.0.0
    istanbul-reports: ^3.1.3
    jest-message-util: ^29.7.0
    jest-util: ^29.7.0
    jest-worker: ^29.7.0
    slash: ^3.0.0
    string-length: ^4.0.1
    strip-ansi: ^6.0.0
    v8-to-istanbul: ^9.0.1
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 7eadabd62cc344f629024b8a268ecc8367dba756152b761bdcb7b7e570a3864fc51b2a9810cd310d85e0a0173ef002ba4528d5ea0329fbf66ee2a3ada9c40455
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/schemas@npm:29.6.3"
  dependencies:
    "@sinclair/typebox": ^0.27.8
  checksum: 910040425f0fc93cd13e68c750b7885590b8839066dfa0cd78e7def07bbb708ad869381f725945d66f2284de5663bbecf63e8fdd856e2ae6e261ba30b1687e93
  languageName: node
  linkType: hard

"@jest/source-map@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/source-map@npm:29.6.3"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.18
    callsites: ^3.0.0
    graceful-fs: ^4.2.9
  checksum: bcc5a8697d471396c0003b0bfa09722c3cd879ad697eb9c431e6164e2ea7008238a01a07193dfe3cbb48b1d258eb7251f6efcea36f64e1ebc464ea3c03ae2deb
  languageName: node
  linkType: hard

"@jest/test-result@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-result@npm:29.7.0"
  dependencies:
    "@jest/console": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/istanbul-lib-coverage": ^2.0.0
    collect-v8-coverage: ^1.0.0
  checksum: 67b6317d526e335212e5da0e768e3b8ab8a53df110361b80761353ad23b6aea4432b7c5665bdeb87658ea373b90fb1afe02ed3611ef6c858c7fba377505057fa
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-sequencer@npm:29.7.0"
  dependencies:
    "@jest/test-result": ^29.7.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.7.0
    slash: ^3.0.0
  checksum: 73f43599017946be85c0b6357993b038f875b796e2f0950487a82f4ebcb115fa12131932dd9904026b4ad8be131fe6e28bd8d0aa93b1563705185f9804bff8bd
  languageName: node
  linkType: hard

"@jest/transform@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/transform@npm:29.7.0"
  dependencies:
    "@babel/core": ^7.11.6
    "@jest/types": ^29.6.3
    "@jridgewell/trace-mapping": ^0.3.18
    babel-plugin-istanbul: ^6.1.1
    chalk: ^4.0.0
    convert-source-map: ^2.0.0
    fast-json-stable-stringify: ^2.1.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.7.0
    jest-regex-util: ^29.6.3
    jest-util: ^29.7.0
    micromatch: ^4.0.4
    pirates: ^4.0.4
    slash: ^3.0.0
    write-file-atomic: ^4.0.2
  checksum: 0f8ac9f413903b3cb6d240102db848f2a354f63971ab885833799a9964999dd51c388162106a807f810071f864302cdd8e3f0c241c29ce02d85a36f18f3f40ab
  languageName: node
  linkType: hard

"@jest/types@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/types@npm:29.6.3"
  dependencies:
    "@jest/schemas": ^29.6.3
    "@types/istanbul-lib-coverage": ^2.0.0
    "@types/istanbul-reports": ^3.0.0
    "@types/node": "*"
    "@types/yargs": ^17.0.8
    chalk: ^4.0.0
  checksum: a0bcf15dbb0eca6bdd8ce61a3fb055349d40268622a7670a3b2eb3c3dbafe9eb26af59938366d520b86907b9505b0f9b29b85cec11579a9e580694b87cd90fcc
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.12, @jridgewell/gen-mapping@npm:^0.3.2, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.12
  resolution: "@jridgewell/gen-mapping@npm:0.3.12"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.5.0
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: 56ee1631945084897f274e65348afbaca7970ce92e3c23b3a23b2fe5d0d2f0c67614f0df0f2bb070e585e944bbaaf0c11cee3a36318ab8a36af46f2fd566bc40
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.10
  resolution: "@jridgewell/source-map@npm:0.3.10"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
  checksum: 035d6e6df0e60744506b14033f1569fd5ddc269abeb68bf50c2911118e2a4fa50dab474d49a59a993e4ee6795c4ae5940381e0d09fc204972c5387788d22d010
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.4
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.4"
  checksum: 959093724bfbc7c1c9aadc08066154f5c1f2acc647b45bd59beec46922cbfc6a9eda4a2114656de5bc00bb3600e420ea9a4cb05e68dcf388619f573b77bd9f0c
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12, @jridgewell/trace-mapping@npm:^0.3.18, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25, @jridgewell/trace-mapping@npm:^0.3.28":
  version: 0.3.29
  resolution: "@jridgewell/trace-mapping@npm:0.3.29"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 5e92eeafa5131a4f6b7122063833d657f885cb581c812da54f705d7a599ff36a75a4a093a83b0f6c7e95642f5772dd94753f696915e8afea082237abf7423ca3
  languageName: node
  linkType: hard

"@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1":
  version: 5.1.1-v1
  resolution: "@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1"
  dependencies:
    eslint-scope: 5.1.1
  checksum: f2e3b2d6a6e2d9f163ca22105910c9f850dc4897af0aea3ef0a5886b63d8e1ba6505b71c99cb78a3bba24a09557d601eb21c8dede3f3213753fcfef364eb0e57
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@octokit/auth-token@npm:^4.0.0":
  version: 4.0.0
  resolution: "@octokit/auth-token@npm:4.0.0"
  checksum: d78f4dc48b214d374aeb39caec4fdbf5c1e4fd8b9fcb18f630b1fe2cbd5a880fca05445f32b4561f41262cb551746aeb0b49e89c95c6dd99299706684d0cae2f
  languageName: node
  linkType: hard

"@octokit/core@npm:^5.0.2":
  version: 5.2.1
  resolution: "@octokit/core@npm:5.2.1"
  dependencies:
    "@octokit/auth-token": ^4.0.0
    "@octokit/graphql": ^7.1.0
    "@octokit/request": ^8.4.1
    "@octokit/request-error": ^5.1.1
    "@octokit/types": ^13.0.0
    before-after-hook: ^2.2.0
    universal-user-agent: ^6.0.0
  checksum: a7076095ec1109bb9273764a2b561b323368e96ea2c1257cef1d48107fe6493f363cfa84539da2d182b065831667c1baa85b00e99712079e299e06b46ba8693b
  languageName: node
  linkType: hard

"@octokit/endpoint@npm:^9.0.6":
  version: 9.0.6
  resolution: "@octokit/endpoint@npm:9.0.6"
  dependencies:
    "@octokit/types": ^13.1.0
    universal-user-agent: ^6.0.0
  checksum: f853c08f0777a8cc7c3d2509835d478e11a76d722f807d4f2ad7c0e64bf4dd159536409f466b367a907886aa3b78574d3d09ed95ac462c769e4fccaaad81e72a
  languageName: node
  linkType: hard

"@octokit/graphql@npm:^7.1.0":
  version: 7.1.1
  resolution: "@octokit/graphql@npm:7.1.1"
  dependencies:
    "@octokit/request": ^8.4.1
    "@octokit/types": ^13.0.0
    universal-user-agent: ^6.0.0
  checksum: afb60d5dda6d365334480540610d67b0c5f8e3977dd895fe504ce988f8b7183f29f3b16b88d895a701a739cf29d157d49f8f9fbc71b6c57eb4fc9bd97e099f55
  languageName: node
  linkType: hard

"@octokit/openapi-types@npm:^24.2.0":
  version: 24.2.0
  resolution: "@octokit/openapi-types@npm:24.2.0"
  checksum: 3c2d2f4cafd21c8a1e6a6fe6b56df6a3c09bc52ab6f829c151f9397694d028aa183ae856f08e006ee7ecaa7bd7eb413a903fbc0ffa6403e7b284ddcda20b1294
  languageName: node
  linkType: hard

"@octokit/plugin-paginate-rest@npm:11.3.1":
  version: 11.3.1
  resolution: "@octokit/plugin-paginate-rest@npm:11.3.1"
  dependencies:
    "@octokit/types": ^13.5.0
  peerDependencies:
    "@octokit/core": 5
  checksum: 42c7c08e7287b4b85d2ae47852d2ffeb238c134ad6bcff18bddc154b15f6bec31778816c0763181401c370198390db7f6b0c3c44750fdfeec459594f7f4b5933
  languageName: node
  linkType: hard

"@octokit/plugin-request-log@npm:^4.0.0":
  version: 4.0.1
  resolution: "@octokit/plugin-request-log@npm:4.0.1"
  peerDependencies:
    "@octokit/core": 5
  checksum: fd8c0a201490cba00084689a0d1d54fc7b5ab5b6bdb7e447056b947b1754f78526e9685400eab10d3522bfa7b5bc49c555f41ec412c788610b96500b168f3789
  languageName: node
  linkType: hard

"@octokit/plugin-rest-endpoint-methods@npm:13.2.2":
  version: 13.2.2
  resolution: "@octokit/plugin-rest-endpoint-methods@npm:13.2.2"
  dependencies:
    "@octokit/types": ^13.5.0
  peerDependencies:
    "@octokit/core": ^5
  checksum: 347b3a891a561ed1dcc307a2dce42ca48c318c465ad91a26225d3d6493aef1b7ff868e6c56a0d7aa4170d028c7429ca1ec52aed6be34615a6ed701c3bcafdb17
  languageName: node
  linkType: hard

"@octokit/request-error@npm:^5.1.1":
  version: 5.1.1
  resolution: "@octokit/request-error@npm:5.1.1"
  dependencies:
    "@octokit/types": ^13.1.0
    deprecation: ^2.0.0
    once: ^1.4.0
  checksum: 17d0b3f59c2a8a285715bfe6a85168d9c417aa7a0ff553b9be4198a3bc8bb00384a3530221a448eb19f8f07ea9fc48d264869624f5f84fa63a948a7af8cddc8c
  languageName: node
  linkType: hard

"@octokit/request@npm:^8.4.1":
  version: 8.4.1
  resolution: "@octokit/request@npm:8.4.1"
  dependencies:
    "@octokit/endpoint": ^9.0.6
    "@octokit/request-error": ^5.1.1
    "@octokit/types": ^13.1.0
    universal-user-agent: ^6.0.0
  checksum: 0ba76728583543baeef9fda98690bc86c57e0a3ccac8c189d2b7d144d248c89167eb37a071ed8fead8f4da0a1c55c4dd98a8fc598769c263b95179fb200959de
  languageName: node
  linkType: hard

"@octokit/rest@npm:20.1.1":
  version: 20.1.1
  resolution: "@octokit/rest@npm:20.1.1"
  dependencies:
    "@octokit/core": ^5.0.2
    "@octokit/plugin-paginate-rest": 11.3.1
    "@octokit/plugin-request-log": ^4.0.0
    "@octokit/plugin-rest-endpoint-methods": 13.2.2
  checksum: c15a801c62a2e2104a4b443b3b43f73366d1220b43995d4ffe1358c4162021708e6625a64ea56bf7d85b870924b862b0d680e191160ceca11e6531b8b92299ca
  languageName: node
  linkType: hard

"@octokit/types@npm:^13.0.0, @octokit/types@npm:^13.1.0, @octokit/types@npm:^13.5.0":
  version: 13.10.0
  resolution: "@octokit/types@npm:13.10.0"
  dependencies:
    "@octokit/openapi-types": ^24.2.0
  checksum: fca3764548d5872535b9025c3b5fe6373fe588b287cb5b5259364796c1931bbe5e9ab8a86a5274ce43bb2b3e43b730067c3b86b6b1ade12a98cd59b2e8b3610d
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.2.4":
  version: 0.2.7
  resolution: "@pkgr/core@npm:0.2.7"
  checksum: b16959878940f3d3016b79a4b2c23fd518aaec6b47295baa3154fbcf6574fee644c51023bb69069fa3ea9cdcaca40432818f54695f11acc0ae326cf56676e4d1
  languageName: node
  linkType: hard

"@pnpm/config.env-replace@npm:^1.1.0":
  version: 1.1.0
  resolution: "@pnpm/config.env-replace@npm:1.1.0"
  checksum: a3d2b57e35eec9543d9eb085854f6e33e8102dac99fdef2fad2eebdbbfc345e93299f0c20e8eb61c1b4c7aa123bfd47c175678626f161cda65dd147c2b6e1fa0
  languageName: node
  linkType: hard

"@pnpm/network.ca-file@npm:^1.0.1":
  version: 1.0.2
  resolution: "@pnpm/network.ca-file@npm:1.0.2"
  dependencies:
    graceful-fs: 4.2.10
  checksum: d8d0884646500576bd5390464d13db1bb9a62e32a1069293e5bddb2ad8354b354b7e2d2a35e12850025651e795e6a80ce9e601c66312504667b7e3ee7b52becc
  languageName: node
  linkType: hard

"@pnpm/npm-conf@npm:^2.1.0":
  version: 2.3.1
  resolution: "@pnpm/npm-conf@npm:2.3.1"
  dependencies:
    "@pnpm/config.env-replace": ^1.1.0
    "@pnpm/network.ca-file": ^1.0.1
    config-chain: ^1.1.11
  checksum: 9e1e1ce5faa64719e866b02d10e28d727d809365eb3692ccfdc420ab6d2073b93abe403994691868f265e34a5601a8eee18ffff6562b27124d971418ba6bb815
  languageName: node
  linkType: hard

"@react-native/assets-registry@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/assets-registry@npm:0.79.5"
  checksum: fbf1c4ca18f2d16ce7eb2f321032f4923aacabde014e1318a2b0b76711eb7d4cb13afccef1018f10d3b4cfb12077542c3ac05a20cff01c199de49e24aed67a62
  languageName: node
  linkType: hard

"@react-native/babel-plugin-codegen@npm:0.78.2":
  version: 0.78.2
  resolution: "@react-native/babel-plugin-codegen@npm:0.78.2"
  dependencies:
    "@babel/traverse": ^7.25.3
    "@react-native/codegen": 0.78.2
  checksum: d00b246793b8019b3e831a28a9c3a3529c221284351137847080d58bf4aa7fb202eef5b8c2085b0516ad5579dd7bfe904752e943f2126f6c1fb7f0590c00f7ce
  languageName: node
  linkType: hard

"@react-native/babel-plugin-codegen@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/babel-plugin-codegen@npm:0.79.5"
  dependencies:
    "@babel/traverse": ^7.25.3
    "@react-native/codegen": 0.79.5
  checksum: 43a681cf480aead43131baa53ec3c28d24e796e120038b4264a524bbb36d76017a010964e70562a9bc965b23c5ae64dfd5b351d965b18bb3a9761b178153332e
  languageName: node
  linkType: hard

"@react-native/babel-preset@npm:0.78.2":
  version: 0.78.2
  resolution: "@react-native/babel-preset@npm:0.78.2"
  dependencies:
    "@babel/core": ^7.25.2
    "@babel/plugin-proposal-export-default-from": ^7.24.7
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-syntax-export-default-from": ^7.24.7
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-transform-arrow-functions": ^7.24.7
    "@babel/plugin-transform-async-generator-functions": ^7.25.4
    "@babel/plugin-transform-async-to-generator": ^7.24.7
    "@babel/plugin-transform-block-scoping": ^7.25.0
    "@babel/plugin-transform-class-properties": ^7.25.4
    "@babel/plugin-transform-classes": ^7.25.4
    "@babel/plugin-transform-computed-properties": ^7.24.7
    "@babel/plugin-transform-destructuring": ^7.24.8
    "@babel/plugin-transform-flow-strip-types": ^7.25.2
    "@babel/plugin-transform-for-of": ^7.24.7
    "@babel/plugin-transform-function-name": ^7.25.1
    "@babel/plugin-transform-literals": ^7.25.2
    "@babel/plugin-transform-logical-assignment-operators": ^7.24.7
    "@babel/plugin-transform-modules-commonjs": ^7.24.8
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.24.7
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.24.7
    "@babel/plugin-transform-numeric-separator": ^7.24.7
    "@babel/plugin-transform-object-rest-spread": ^7.24.7
    "@babel/plugin-transform-optional-catch-binding": ^7.24.7
    "@babel/plugin-transform-optional-chaining": ^7.24.8
    "@babel/plugin-transform-parameters": ^7.24.7
    "@babel/plugin-transform-private-methods": ^7.24.7
    "@babel/plugin-transform-private-property-in-object": ^7.24.7
    "@babel/plugin-transform-react-display-name": ^7.24.7
    "@babel/plugin-transform-react-jsx": ^7.25.2
    "@babel/plugin-transform-react-jsx-self": ^7.24.7
    "@babel/plugin-transform-react-jsx-source": ^7.24.7
    "@babel/plugin-transform-regenerator": ^7.24.7
    "@babel/plugin-transform-runtime": ^7.24.7
    "@babel/plugin-transform-shorthand-properties": ^7.24.7
    "@babel/plugin-transform-spread": ^7.24.7
    "@babel/plugin-transform-sticky-regex": ^7.24.7
    "@babel/plugin-transform-typescript": ^7.25.2
    "@babel/plugin-transform-unicode-regex": ^7.24.7
    "@babel/template": ^7.25.0
    "@react-native/babel-plugin-codegen": 0.78.2
    babel-plugin-syntax-hermes-parser: 0.25.1
    babel-plugin-transform-flow-enums: ^0.0.2
    react-refresh: ^0.14.0
  peerDependencies:
    "@babel/core": "*"
  checksum: 5ac8b00124a960de84305436be2fd285328508e63796dd4e584adb661b3665f7d3a1f71f48ee0a545eba938a88351a7bd557ab96a56fdd1c9d2f09c4d0339583
  languageName: node
  linkType: hard

"@react-native/babel-preset@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/babel-preset@npm:0.79.5"
  dependencies:
    "@babel/core": ^7.25.2
    "@babel/plugin-proposal-export-default-from": ^7.24.7
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-syntax-export-default-from": ^7.24.7
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-transform-arrow-functions": ^7.24.7
    "@babel/plugin-transform-async-generator-functions": ^7.25.4
    "@babel/plugin-transform-async-to-generator": ^7.24.7
    "@babel/plugin-transform-block-scoping": ^7.25.0
    "@babel/plugin-transform-class-properties": ^7.25.4
    "@babel/plugin-transform-classes": ^7.25.4
    "@babel/plugin-transform-computed-properties": ^7.24.7
    "@babel/plugin-transform-destructuring": ^7.24.8
    "@babel/plugin-transform-flow-strip-types": ^7.25.2
    "@babel/plugin-transform-for-of": ^7.24.7
    "@babel/plugin-transform-function-name": ^7.25.1
    "@babel/plugin-transform-literals": ^7.25.2
    "@babel/plugin-transform-logical-assignment-operators": ^7.24.7
    "@babel/plugin-transform-modules-commonjs": ^7.24.8
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.24.7
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.24.7
    "@babel/plugin-transform-numeric-separator": ^7.24.7
    "@babel/plugin-transform-object-rest-spread": ^7.24.7
    "@babel/plugin-transform-optional-catch-binding": ^7.24.7
    "@babel/plugin-transform-optional-chaining": ^7.24.8
    "@babel/plugin-transform-parameters": ^7.24.7
    "@babel/plugin-transform-private-methods": ^7.24.7
    "@babel/plugin-transform-private-property-in-object": ^7.24.7
    "@babel/plugin-transform-react-display-name": ^7.24.7
    "@babel/plugin-transform-react-jsx": ^7.25.2
    "@babel/plugin-transform-react-jsx-self": ^7.24.7
    "@babel/plugin-transform-react-jsx-source": ^7.24.7
    "@babel/plugin-transform-regenerator": ^7.24.7
    "@babel/plugin-transform-runtime": ^7.24.7
    "@babel/plugin-transform-shorthand-properties": ^7.24.7
    "@babel/plugin-transform-spread": ^7.24.7
    "@babel/plugin-transform-sticky-regex": ^7.24.7
    "@babel/plugin-transform-typescript": ^7.25.2
    "@babel/plugin-transform-unicode-regex": ^7.24.7
    "@babel/template": ^7.25.0
    "@react-native/babel-plugin-codegen": 0.79.5
    babel-plugin-syntax-hermes-parser: 0.25.1
    babel-plugin-transform-flow-enums: ^0.0.2
    react-refresh: ^0.14.0
  peerDependencies:
    "@babel/core": "*"
  checksum: de7e57ed4ccfc62deec2aac2ffab0257b620c8af34ffc4d0fab15a074383800f226f7f95b69ae4117305240c7dba1ec2fc61083dcc21459972eb2712adf70fbc
  languageName: node
  linkType: hard

"@react-native/codegen@npm:0.78.2":
  version: 0.78.2
  resolution: "@react-native/codegen@npm:0.78.2"
  dependencies:
    "@babel/parser": ^7.25.3
    glob: ^7.1.1
    hermes-parser: 0.25.1
    invariant: ^2.2.4
    jscodeshift: ^17.0.0
    nullthrows: ^1.1.1
    yargs: ^17.6.2
  peerDependencies:
    "@babel/preset-env": ^7.1.6
  checksum: e0e6e53d458d177fb9a767e5c8c3d28965575ab799beaf78111a43424270251e6a4a9dd420297eea90bf838b3f5cbf23a6e4dbebb5670b1fa501d012fcadee79
  languageName: node
  linkType: hard

"@react-native/codegen@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/codegen@npm:0.79.5"
  dependencies:
    glob: ^7.1.1
    hermes-parser: 0.25.1
    invariant: ^2.2.4
    nullthrows: ^1.1.1
    yargs: ^17.6.2
  peerDependencies:
    "@babel/core": "*"
  checksum: 2254bada67ebd4f88c6fff5568a33588062deca1f53f5a371577618bd8fd0d0ab813c4c44f226e54bef25fbf2f8784e7f702b76cd617b39baab24d138346c9e3
  languageName: node
  linkType: hard

"@react-native/community-cli-plugin@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/community-cli-plugin@npm:0.79.5"
  dependencies:
    "@react-native/dev-middleware": 0.79.5
    chalk: ^4.0.0
    debug: ^2.2.0
    invariant: ^2.2.4
    metro: ^0.82.0
    metro-config: ^0.82.0
    metro-core: ^0.82.0
    semver: ^7.1.3
  peerDependencies:
    "@react-native-community/cli": "*"
  peerDependenciesMeta:
    "@react-native-community/cli":
      optional: true
  checksum: 019fc2e3328063e0619dc8ebc511ac2285b4aa25038fd9ee9ff2e3674e039c9dcef5e2f6dc233b64d635a1e662681107c5512c8982d1b6663f3728e66d3b582e
  languageName: node
  linkType: hard

"@react-native/debugger-frontend@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/debugger-frontend@npm:0.79.5"
  checksum: 5f468a06ec4916fed8d4d865fe05c6fa0ba5ade7c8870bdede836f4b2210fd07974c6774f07b71098b50c625a45b141df7333aad174e43d8c607d64ff065d6e1
  languageName: node
  linkType: hard

"@react-native/dev-middleware@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/dev-middleware@npm:0.79.5"
  dependencies:
    "@isaacs/ttlcache": ^1.4.1
    "@react-native/debugger-frontend": 0.79.5
    chrome-launcher: ^0.15.2
    chromium-edge-launcher: ^0.2.0
    connect: ^3.6.5
    debug: ^2.2.0
    invariant: ^2.2.4
    nullthrows: ^1.1.1
    open: ^7.0.3
    serve-static: ^1.16.2
    ws: ^6.2.3
  checksum: 0d2bd347060021287a3b47f37f7f942cda37be7cd58b51fb0aaa5ab3fe1ecc1359adc7cc845c70f301ad9a87731eb3cf91a66b09c15519393013378f16285c95
  languageName: node
  linkType: hard

"@react-native/eslint-config@npm:^0.78.0":
  version: 0.78.2
  resolution: "@react-native/eslint-config@npm:0.78.2"
  dependencies:
    "@babel/core": ^7.25.2
    "@babel/eslint-parser": ^7.25.1
    "@react-native/eslint-plugin": 0.78.2
    "@typescript-eslint/eslint-plugin": ^7.1.1
    "@typescript-eslint/parser": ^7.1.1
    eslint-config-prettier: ^8.5.0
    eslint-plugin-eslint-comments: ^3.2.0
    eslint-plugin-ft-flow: ^2.0.1
    eslint-plugin-jest: ^27.9.0
    eslint-plugin-react: ^7.30.1
    eslint-plugin-react-hooks: ^4.6.0
    eslint-plugin-react-native: ^4.0.0
  peerDependencies:
    eslint: ">=8"
    prettier: ">=2"
  checksum: 4cbdf40717a3f51432f685364ad81a2fd8bf7746d9dcb1e285900ddafdea777b058c931daf2a23916f909498ae756f0f1baab4636596a90653cd9a0e59ac9b84
  languageName: node
  linkType: hard

"@react-native/eslint-plugin@npm:0.78.2":
  version: 0.78.2
  resolution: "@react-native/eslint-plugin@npm:0.78.2"
  checksum: d79285f702e25e467525cb55ae824c50c7333138cce1295b4a1c046f11c68e7bd43ffa0c78fb0e5b1a59be92a5cf44e59c2bfc16e6c2e5b2550ea251f3cb65c6
  languageName: node
  linkType: hard

"@react-native/gradle-plugin@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/gradle-plugin@npm:0.79.5"
  checksum: f459ec6af3f82047af87861f6024014bc7d01c92ba458980a5478326e3286ecbdb7713c10497525bd0253da2d678ffbb8c5b720f3099716844ae534c10cc5e3d
  languageName: node
  linkType: hard

"@react-native/js-polyfills@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/js-polyfills@npm:0.79.5"
  checksum: a51a289ee7147c968e3626b43e5602ecf2c8b1f00c551ba5c8db9fc67fb26ca88cdf1718eca0a1e67281f900897b5fb490367ed9ab10361a1005e1d5ba3c2e34
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/normalize-colors@npm:0.79.5"
  checksum: 0c62e8e4e2473e669f87e425e1ccf49471c00b88606a626c0addb9817f5a782b3115fb98a91b36f3bf4536fae8585fbf17a988e59ccc851f79999008cd64a4e4
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:^0.74.1":
  version: 0.74.89
  resolution: "@react-native/normalize-colors@npm:0.74.89"
  checksum: df62772f029dd132d3061a8ee7f90b6aaf5c525bb7e33c22908249daffa42995cddd91adc790ec9ce701636c14eeafc1809a9d0d879e3f8c71c9f340145abfce
  languageName: node
  linkType: hard

"@react-native/virtualized-lists@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/virtualized-lists@npm:0.79.5"
  dependencies:
    invariant: ^2.2.4
    nullthrows: ^1.1.1
  peerDependencies:
    "@types/react": ^19.0.0
    react: "*"
    react-native: "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: edde6d639e65ec4b1ee2477e2f1ae7c5f72769d75dcf8cb265e0be5495c143e631221a65dbf8e3f472ab396144605269b3e1e7a88137f56259b2de680f271189
  languageName: node
  linkType: hard

"@release-it/conventional-changelog@npm:^9.0.2":
  version: 9.0.4
  resolution: "@release-it/conventional-changelog@npm:9.0.4"
  dependencies:
    concat-stream: ^2.0.0
    conventional-changelog: ^6.0.0
    conventional-recommended-bump: ^10.0.0
    git-semver-tags: ^8.0.0
    semver: ^7.6.3
  peerDependencies:
    release-it: ^17.0.0
  checksum: fbe17cc1d83abd616fa1b02b3c52d964ba6bdc7e5d91984f5bd1aebcdde390b9d7d0e8e3d916dce64f658058429f65a92196d1c978018bb35973e15419272e65
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 00bd7362a3439021aa1ea51b0e0d0a0e8ca1351a3d54c606b115fdcc49b51b16db6e5f43b4fe7a28c38688523e22a94d49dd31168868b655f0d4d50f032d07a1
  languageName: node
  linkType: hard

"@sindresorhus/merge-streams@npm:^2.1.0":
  version: 2.3.0
  resolution: "@sindresorhus/merge-streams@npm:2.3.0"
  checksum: e989d53dee68d7e49b4ac02ae49178d561c461144cea83f66fa91ff012d981ad0ad2340cbd13f2fdb57989197f5c987ca22a74eb56478626f04e79df84291159
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^3.0.0":
  version: 3.0.1
  resolution: "@sinonjs/commons@npm:3.0.1"
  dependencies:
    type-detect: 4.0.8
  checksum: a7c3e7cc612352f4004873747d9d8b2d4d90b13a6d483f685598c945a70e734e255f1ca5dc49702515533c403b32725defff148177453b3f3915bcb60e9d4601
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^10.0.2":
  version: 10.3.0
  resolution: "@sinonjs/fake-timers@npm:10.3.0"
  dependencies:
    "@sinonjs/commons": ^3.0.0
  checksum: 614d30cb4d5201550c940945d44c9e0b6d64a888ff2cd5b357f95ad6721070d6b8839cd10e15b76bf5e14af0bcc1d8f9ec00d49a46318f1f669a4bec1d7f3148
  languageName: node
  linkType: hard

"@tootallnate/quickjs-emscripten@npm:^0.23.0":
  version: 0.23.0
  resolution: "@tootallnate/quickjs-emscripten@npm:0.23.0"
  checksum: c350a2947ffb80b22e14ff35099fd582d1340d65723384a0fd0515e905e2534459ad2f301a43279a37308a27c99273c932e64649abd57d0bb3ca8c557150eccc
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.1.14":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": ^7.20.7
    "@babel/types": ^7.20.7
    "@types/babel__generator": "*"
    "@types/babel__template": "*"
    "@types/babel__traverse": "*"
  checksum: a3226f7930b635ee7a5e72c8d51a357e799d19cbf9d445710fa39ab13804f79ab1a54b72ea7d8e504659c7dfc50675db974b526142c754398d7413aa4bc30845
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.27.0
  resolution: "@types/babel__generator@npm:7.27.0"
  dependencies:
    "@babel/types": ^7.0.0
  checksum: e6739cacfa276c1ad38e1d8a6b4b1f816c2c11564e27f558b68151728489aaf0f4366992107ee4ed7615dfa303f6976dedcdce93df2b247116d1bcd1607ee260
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": ^7.1.0
    "@babel/types": ^7.0.0
  checksum: d7a02d2a9b67e822694d8e6a7ddb8f2b71a1d6962dfd266554d2513eefbb205b33ca71a0d163b1caea3981ccf849211f9964d8bd0727124d18ace45aa6c9ae29
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.6":
  version: 7.20.7
  resolution: "@types/babel__traverse@npm:7.20.7"
  dependencies:
    "@babel/types": ^7.20.7
  checksum: 2a2e5ad29c34a8b776162b0fe81c9ccb6459b2b46bf230f756ba0276a0258fcae1cbcfdccbb93a1e8b1df44f4939784ee8a1a269f95afe0c78b24b9cb6d50dd1
  languageName: node
  linkType: hard

"@types/conventional-commits-parser@npm:^5.0.0":
  version: 5.0.1
  resolution: "@types/conventional-commits-parser@npm:5.0.1"
  dependencies:
    "@types/node": "*"
  checksum: b4eb4f22051d42e7ed9fd3bffe6ea0cf62ae493a3c6c775a16babbad977c934f4c09ec3fa93020894de2073d63cfcd3a27dd5f00984966161da6797dd88a0f0d
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: bd93e2e415b6f182ec4da1074e1f36c480f1d26add3e696d54fb30c09bc470897e41361c8fd957bf0985024f8fbf1e6e2aff977d79352ef7eb93a5c6dcff6c11
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.3":
  version: 4.1.9
  resolution: "@types/graceful-fs@npm:4.1.9"
  dependencies:
    "@types/node": "*"
  checksum: 79d746a8f053954bba36bd3d94a90c78de995d126289d656fb3271dd9f1229d33f678da04d10bce6be440494a5a73438e2e363e92802d16b8315b051036c5256
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 3feac423fd3e5449485afac999dcfcb3d44a37c830af898b689fadc65d26526460bedb889db278e0d4d815a670331796494d073a10ee6e3a6526301fe7415778
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.3
  resolution: "@types/istanbul-lib-report@npm:3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage": "*"
  checksum: b91e9b60f865ff08cb35667a427b70f6c2c63e88105eadd29a112582942af47ed99c60610180aa8dcc22382fa405033f141c119c69b95db78c4c709fbadfeeb4
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/istanbul-reports@npm:3.0.4"
  dependencies:
    "@types/istanbul-lib-report": "*"
  checksum: 93eb18835770b3431f68ae9ac1ca91741ab85f7606f310a34b3586b5a34450ec038c3eed7ab19266635499594de52ff73723a54a72a75b9f7d6a956f01edee95
  languageName: node
  linkType: hard

"@types/jest@npm:^29.5.5":
  version: 29.5.14
  resolution: "@types/jest@npm:29.5.14"
  dependencies:
    expect: ^29.0.0
    pretty-format: ^29.0.0
  checksum: 18dba4623f26661641d757c63da2db45e9524c9be96a29ef713c703a9a53792df9ecee9f7365a0858ddbd6440d98fe6b65ca67895ca5884b73cbc7ffc11f3838
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/minimist@npm:^1.2.2":
  version: 1.2.5
  resolution: "@types/minimist@npm:1.2.5"
  checksum: 477047b606005058ab0263c4f58097136268007f320003c348794f74adedc3166ffc47c80ec3e94687787f2ab7f4e72c468223946e79892cf0fd9e25e9970a90
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 24.0.10
  resolution: "@types/node@npm:24.0.10"
  dependencies:
    undici-types: ~7.8.0
  checksum: 04e4d11a711a683d6ed71f8db86b35035771ee9c1f110cb904ca67454d3478f19c71f1f0ccd0ba5350e5ce63bea70d08c569f798f6f5a5d2a1d05c038f1f729f
  languageName: node
  linkType: hard

"@types/normalize-package-data@npm:^2.4.0, @types/normalize-package-data@npm:^2.4.3":
  version: 2.4.4
  resolution: "@types/normalize-package-data@npm:2.4.4"
  checksum: 65dff72b543997b7be8b0265eca7ace0e34b75c3e5fee31de11179d08fa7124a7a5587265d53d0409532ecb7f7fba662c2012807963e1f9b059653ec2c83ee05
  languageName: node
  linkType: hard

"@types/react@npm:^19.0.12":
  version: 19.1.8
  resolution: "@types/react@npm:19.1.8"
  dependencies:
    csstype: ^3.0.2
  checksum: 17e0c74d9c01214938fa805aaa8b97925bf3c5514e88fdf94bec42c0a6d4abbc63d4e30255db176f46fd7f0aa89f8085b9b2b2fa5abaffbbf7e5009386ada892
  languageName: node
  linkType: hard

"@types/semver@npm:^7.3.12, @types/semver@npm:^7.5.5":
  version: 7.7.0
  resolution: "@types/semver@npm:7.7.0"
  checksum: d488eaeddb23879a0a8a759bed667e1a76cb0dd4d23e3255538e24c189db387357953ca9e7a3bda2bb7f95e84cac8fe0db4fbe6b3456e893043337732d1d23cc
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.3
  resolution: "@types/stack-utils@npm:2.0.3"
  checksum: 72576cc1522090fe497337c2b99d9838e320659ac57fa5560fcbdcbafcf5d0216c6b3a0a8a4ee4fdb3b1f5e3420aa4f6223ab57b82fef3578bec3206425c6cf5
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: ef236c27f9432983e91432d974243e6c4cdae227cb673740320eff32d04d853eed59c92ca6f1142a335cfdc0e17cccafa62e95886a8154ca8891cc2dec4ee6fc
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.33
  resolution: "@types/yargs@npm:17.0.33"
  dependencies:
    "@types/yargs-parser": "*"
  checksum: ee013f257472ab643cb0584cf3e1ff9b0c44bca1c9ba662395300a7f1a6c55fa9d41bd40ddff42d99f5d95febb3907c9ff600fbcb92dadbec22c6a76de7e1236
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^7.1.1":
  version: 7.18.0
  resolution: "@typescript-eslint/eslint-plugin@npm:7.18.0"
  dependencies:
    "@eslint-community/regexpp": ^4.10.0
    "@typescript-eslint/scope-manager": 7.18.0
    "@typescript-eslint/type-utils": 7.18.0
    "@typescript-eslint/utils": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
    graphemer: ^1.4.0
    ignore: ^5.3.1
    natural-compare: ^1.4.0
    ts-api-utils: ^1.3.0
  peerDependencies:
    "@typescript-eslint/parser": ^7.0.0
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dfcf150628ca2d4ccdfc20b46b0eae075c2f16ef5e70d9d2f0d746acf4c69a09f962b93befee01a529f14bbeb3e817b5aba287d7dd0edc23396bc5ed1f448c3d
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^7.1.1":
  version: 7.18.0
  resolution: "@typescript-eslint/parser@npm:7.18.0"
  dependencies:
    "@typescript-eslint/scope-manager": 7.18.0
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/typescript-estree": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 132b56ac3b2d90b588d61d005a70f6af322860974225b60201cbf45abf7304d67b7d8a6f0ade1c188ac4e339884e78d6dcd450417f1481998f9ddd155bab0801
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/scope-manager@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/visitor-keys": 5.62.0
  checksum: 6062d6b797fe1ce4d275bb0d17204c827494af59b5eaf09d8a78cdd39dadddb31074dded4297aaf5d0f839016d601032857698b0e4516c86a41207de606e9573
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/scope-manager@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
  checksum: b982c6ac13d8c86bb3b949c6b4e465f3f60557c2ccf4cc229799827d462df56b9e4d3eaed7711d79b875422fc3d71ec1ebcb5195db72134d07c619e3c5506b57
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/type-utils@npm:7.18.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 7.18.0
    "@typescript-eslint/utils": 7.18.0
    debug: ^4.3.4
    ts-api-utils: ^1.3.0
  peerDependencies:
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 68fd5df5146c1a08cde20d59b4b919acab06a1b06194fe4f7ba1b928674880249890785fbbc97394142f2ef5cff5a7fba9b8a940449e7d5605306505348e38bc
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/types@npm:5.62.0"
  checksum: 48c87117383d1864766486f24de34086155532b070f6264e09d0e6139449270f8a9559cfef3c56d16e3bcfb52d83d42105d61b36743626399c7c2b5e0ac3b670
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/types@npm:7.18.0"
  checksum: 7df2750cd146a0acd2d843208d69f153b458e024bbe12aab9e441ad2c56f47de3ddfeb329c4d1ea0079e2577fea4b8c1c1ce15315a8d49044586b04fedfe7a4d
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/typescript-estree@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/visitor-keys": 5.62.0
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    semver: ^7.3.7
    tsutils: ^3.21.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 3624520abb5807ed8f57b1197e61c7b1ed770c56dfcaca66372d584ff50175225798bccb701f7ef129d62c5989070e1ee3a0aa2d84e56d9524dcf011a2bb1a52
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/typescript-estree@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    minimatch: ^9.0.4
    semver: ^7.6.0
    ts-api-utils: ^1.3.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: c82d22ec9654973944f779eb4eb94c52f4a6eafaccce2f0231ff7757313f3a0d0256c3252f6dfe6d43f57171d09656478acb49a629a9d0c193fb959bc3f36116
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/utils@npm:7.18.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@typescript-eslint/scope-manager": 7.18.0
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/typescript-estree": 7.18.0
  peerDependencies:
    eslint: ^8.56.0
  checksum: 751dbc816dab8454b7dc6b26a56671dbec08e3f4ef94c2661ce1c0fc48fa2d05a64e03efe24cba2c22d03ba943cd3c5c7a5e1b7b03bbb446728aec1c640bd767
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:^5.10.0":
  version: 5.62.0
  resolution: "@typescript-eslint/utils@npm:5.62.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@types/json-schema": ^7.0.9
    "@types/semver": ^7.3.12
    "@typescript-eslint/scope-manager": 5.62.0
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/typescript-estree": 5.62.0
    eslint-scope: ^5.1.1
    semver: ^7.3.7
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: ee9398c8c5db6d1da09463ca7bf36ed134361e20131ea354b2da16a5fdb6df9ba70c62a388d19f6eebb421af1786dbbd79ba95ddd6ab287324fc171c3e28d931
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/visitor-keys@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    eslint-visitor-keys: ^3.3.0
  checksum: 976b05d103fe8335bef5c93ad3f76d781e3ce50329c0243ee0f00c0fcfb186c81df50e64bfdd34970148113f8ade90887f53e3c4938183afba830b4ba8e30a35
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/visitor-keys@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": 7.18.0
    eslint-visitor-keys: ^3.4.3
  checksum: 6e806a7cdb424c5498ea187a5a11d0fef7e4602a631be413e7d521e5aec1ab46ba00c76cfb18020adaa0a8c9802354a163bfa0deb74baa7d555526c7517bb158
  languageName: node
  linkType: hard

"@urql/core@npm:^5.0.6, @urql/core@npm:^5.1.2":
  version: 5.2.0
  resolution: "@urql/core@npm:5.2.0"
  dependencies:
    "@0no-co/graphql.web": ^1.0.13
    wonka: ^6.3.2
  checksum: 89c1959abd9230ba06ed5dc4cc5ea6585101c4b0c12894ddbdd1ec36d76b70799b819b999cbcaf154b6f9903eb6c2ba8eef6dff447bc5c9c1870878748868e85
  languageName: node
  linkType: hard

"@urql/exchange-retry@npm:^1.3.0":
  version: 1.3.2
  resolution: "@urql/exchange-retry@npm:1.3.2"
  dependencies:
    "@urql/core": ^5.1.2
    wonka: ^6.3.2
  peerDependencies:
    "@urql/core": ^5.0.0
  checksum: 57aa6dc47d5869aa45580236fa1ab85487886dd7bfef68e2e4271980c6a10332db1f8e8d750694e89f29ad94f7a650d00ef1a441b739215483b9a3f776edf45f
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.8":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 4c136aec31fb3b49aaa53b6fcbfe524d02a1dc0d8e17ee35bd3bf35e9ce1344560481cd1efd086ad1a4821541482528672306d5e37cdbd187f33d7fadd3e2cf0
  languageName: node
  linkType: hard

"JSONStream@npm:^1.3.5":
  version: 1.3.5
  resolution: "JSONStream@npm:1.3.5"
  dependencies:
    jsonparse: ^1.2.0
    through: ">=2.2.7 <3"
  bin:
    JSONStream: ./bin.js
  checksum: 2605fa124260c61bad38bb65eba30d2f72216a78e94d0ab19b11b4e0327d572b8d530c0c9cc3b0764f727ad26d39e00bf7ebad57781ca6368394d73169c59e46
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: e70b209f5f408dd3a3bbd0eec4b10a2ffd64704a4a3821d0969d84928cc490a8eb60f85b78a95622c1841113edac10161c62e52f5e7d0027aa26786a8136e02e
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: ^5.0.0
  checksum: 170bdba9b47b7e65906a28c8ce4f38a7a369d78e2271706f020849c1bfe0ee2067d4261df8bbb66eb84f79208fd5b710df759d64191db58cfba7ce8ef9c54b75
  languageName: node
  linkType: hard

"accepts@npm:^1.3.7, accepts@npm:^1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: ~2.1.34
    negotiator: 0.6.3
  checksum: 50c43d32e7b50285ebe84b613ee4a3aa426715a7d131b65b786e2ead0fd76b6b60091b9916d3478a75f11f162628a2139991b6c03ab3f1d9ab7c86075dc8eab4
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.15.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 309c6b49aedf1a2e34aaf266de06de04aab6eb097c02375c66fdeb0f64556a6a823540409914fb364d9a11bc30d79d485a2eba29af47992d3490e9886c4391c3
  languageName: node
  linkType: hard

"add-stream@npm:^1.0.0":
  version: 1.0.0
  resolution: "add-stream@npm:1.0.0"
  checksum: 3e9e8b0b8f0170406d7c3a9a39bfbdf419ccccb0fd2a396338c0fda0a339af73bf738ad414fc520741de74517acf0dd92b4a36fd3298a47fd5371eee8f2c5a06
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 87bb7ee54f5ecf0ccbfcba0b07473885c43ecd76cb29a8db17d6137a19d9f9cd443a2a7c5fd8a3f24d58ad8145f9eb49116344a66b107e1aeab82cf2383f4753
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"aggregate-error@npm:^4.0.0":
  version: 4.0.1
  resolution: "aggregate-error@npm:4.0.1"
  dependencies:
    clean-stack: ^4.0.0
    indent-string: ^5.0.0
  checksum: bb3ffdfd13447800fff237c2cba752c59868ee669104bb995dfbbe0b8320e967d679e683dabb640feb32e4882d60258165cde0baafc4cd467cc7d275a13ad6b5
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ajv@npm:^8.11.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: ^3.1.3
    fast-uri: ^3.0.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
  checksum: 1797bf242cfffbaf3b870d13565bd1716b73f214bb7ada9a497063aada210200da36e3ed40237285f3255acc4feeae91b1fb183625331bad27da95973f7253d9
  languageName: node
  linkType: hard

"anser@npm:^1.4.9":
  version: 1.4.10
  resolution: "anser@npm:1.4.10"
  checksum: 3823c64f8930d3d97f36e56cdf646fa6351f1227e25eee70c3a17697447cae4238fc3a309bb3bc2003cf930687fa72aed71426dbcf3c0a15565e120a7fee5507
  languageName: node
  linkType: hard

"ansi-align@npm:^3.0.1":
  version: 3.0.1
  resolution: "ansi-align@npm:3.0.1"
  dependencies:
    string-width: ^4.1.0
  checksum: 6abfa08f2141d231c257162b15292467081fa49a208593e055c866aa0455b57f3a86b5a678c190c618faa79b4c59e254493099cb700dd9cf2293c6be2c8f5d8d
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1, ansi-escapes@npm:^4.3.2":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: ^0.21.3
  checksum: 93111c42189c0a6bed9cdb4d7f2829548e943827ee8479c74d6e0b22ee127b2a21d3f8b5ca57723b8ef78ce011fbfc2784350eb2bde3ccfccf2f575fa8489815
  languageName: node
  linkType: hard

"ansi-regex@npm:^4.1.0":
  version: 4.1.1
  resolution: "ansi-regex@npm:4.1.1"
  checksum: b1a6ee44cb6ecdabaa770b2ed500542714d4395d71c7e5c25baa631f680fb2ad322eb9ba697548d498a6fd366949fc8b5bfcf48d49a32803611f648005b01888
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.0, ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: d7f4e97ce0623aea6bc0d90dcd28881ee04cba06c570b97fd3391bd7a268eedfd9d5e2dd4fdcbdd82b8105df5faf6f24aaedc08eaf3da898e702db5948f63469
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0, ansi-styles@npm:^6.2.1":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 0ee8a9bdbe882c90464d75d1f55cf027f5458650c4bd1f0467e65aec38ccccda07ca5844969ee77ed46d04e7dded3eaceb027e8d32f385688523fe305fa7e1de
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"arg@npm:^5.0.2":
  version: 5.0.2
  resolution: "arg@npm:5.0.2"
  checksum: 6c69ada1a9943d332d9e5382393e897c500908d91d5cb735a01120d5f71daf1b339b7b8980cbeaba8fd1afc68e658a739746179e4315a26e8a28951ff9930078
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"arktype@npm:^2.1.15":
  version: 2.1.20
  resolution: "arktype@npm:2.1.20"
  dependencies:
    "@ark/schema": 0.46.0
    "@ark/util": 0.46.0
  checksum: 5c02dda98606b83b35bbc66934259e3f30c4b4486c32e470e199da533c0af568951502173d7d7a5e64a2e53667eb36d10d772ce46c0bff204fab759430614c9b
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    is-array-buffer: ^3.0.5
  checksum: 0ae3786195c3211b423e5be8dd93357870e6fb66357d81da968c2c39ef43583ef6eece1f9cb1caccdae4806739c65dea832b44b8593414313cd76a89795fca63
  languageName: node
  linkType: hard

"array-ify@npm:^1.0.0":
  version: 1.0.0
  resolution: "array-ify@npm:1.0.0"
  checksum: c0502015b319c93dd4484f18036bcc4b654eb76a4aa1f04afbcef11ac918859bb1f5d71ba1f0f1141770db9eef1a4f40f1761753650873068010bbf7bcdae4a4
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.8":
  version: 3.1.9
  resolution: "array-includes@npm:3.1.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    define-properties: ^1.2.1
    es-abstract: ^1.24.0
    es-object-atoms: ^1.1.1
    get-intrinsic: ^1.3.0
    is-string: ^1.1.1
    math-intrinsics: ^1.1.0
  checksum: b58dc526fe415252e50319eaf88336e06e75aa673e3b58d252414739a4612dbe56e7b613fdcc7c90561dc9cf9202bbe5ca029ccd8c08362746459475ae5a8f3e
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-shim-unscopables: ^1.0.2
  checksum: 83ce4ad95bae07f136d316f5a7c3a5b911ac3296c3476abe60225bc4a17938bf37541972fcc37dd5adbc99cbb9c928c70bbbfc1c1ce549d41a415144030bb446
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-shim-unscopables: ^1.0.2
  checksum: 5d5a7829ab2bb271a8d30a1c91e6271cef0ec534593c0fe6d2fb9ebf8bb62c1e5326e2fddcbbcbbe5872ca04f5e6b54a1ecf092e0af704fb538da9b2bfd95b40
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-shim-unscopables: ^1.0.2
  checksum: 11b4de09b1cf008be6031bb507d997ad6f1892e57dc9153583de6ebca0f74ea403fffe0f203461d359de05048d609f3f480d9b46fed4099652d8b62cc972f284
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.3
    es-errors: ^1.3.0
    es-shim-unscopables: ^1.0.2
  checksum: e4142d6f556bcbb4f393c02e7dbaea9af8f620c040450c2be137c9cbbd1a17f216b9c688c5f2c08fbb038ab83f55993fa6efdd9a05881d84693c7bcb5422127a
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    is-array-buffer: ^3.0.4
  checksum: b1d1fd20be4e972a3779b1569226f6740170dca10f07aa4421d42cefeec61391e79c557cda8e771f5baefe47d878178cd4438f60916ce831813c08132bced765
  languageName: node
  linkType: hard

"arrify@npm:^1.0.1":
  version: 1.0.1
  resolution: "arrify@npm:1.0.1"
  checksum: 745075dd4a4624ff0225c331dacb99be501a515d39bcb7c84d24660314a6ec28e68131b137e6f7e16318170842ce97538cd298fc4cd6b2cc798e0b957f2747e7
  languageName: node
  linkType: hard

"asap@npm:~2.0.3, asap@npm:~2.0.6":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: b296c92c4b969e973260e47523207cd5769abd27c245a68c26dc7a0fe8053c55bb04360237cb51cab1df52be939da77150ace99ad331fb7fb13b3423ed73ff3d
  languageName: node
  linkType: hard

"ast-types@npm:^0.13.4":
  version: 0.13.4
  resolution: "ast-types@npm:0.13.4"
  dependencies:
    tslib: ^2.0.1
  checksum: 5a51f7b70588ecced3601845a0e203279ca2f5fdc184416a0a1640c93ec0a267241d6090a328e78eebb8de81f8754754e0a4f1558ba2a3d638f8ccbd0b1f0eff
  languageName: node
  linkType: hard

"ast-types@npm:^0.16.1":
  version: 0.16.1
  resolution: "ast-types@npm:0.16.1"
  dependencies:
    tslib: ^2.0.1
  checksum: 21c186da9fdb1d8087b1b7dabbc4059f91aa5a1e593a9776b4393cc1eaa857e741b2dda678d20e34b16727b78fef3ab59cf8f0c75ed1ba649c78fe194e5c114b
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 9102e246d1ed9b37ac36f57f0a6ca55226876553251a31fc80677e71471f463a54c872dc78d5d7f80740c8ba624395cccbe8b60f7b690c4418f487d8e9fd1106
  languageName: node
  linkType: hard

"async-limiter@npm:~1.0.0":
  version: 1.0.1
  resolution: "async-limiter@npm:1.0.1"
  checksum: 2b849695b465d93ad44c116220dee29a5aeb63adac16c1088983c339b0de57d76e82533e8e364a93a9f997f28bbfc6a92948cefc120652bd07f3b59f8d75cf2b
  languageName: node
  linkType: hard

"async-retry@npm:1.3.3":
  version: 1.3.3
  resolution: "async-retry@npm:1.3.3"
  dependencies:
    retry: 0.13.1
  checksum: 38a7152ff7265a9321ea214b9c69e8224ab1febbdec98efbbde6e562f17ff68405569b796b1c5271f354aef8783665d29953f051f68c1fc45306e61aec82fdc4
  languageName: node
  linkType: hard

"atomically@npm:^2.0.3":
  version: 2.0.3
  resolution: "atomically@npm:2.0.3"
  dependencies:
    stubborn-fs: ^1.2.5
    when-exit: ^2.1.1
  checksum: 4ee528fe35b4bc84cd626f6414cd2b51f04f94c2f6e8ab5c97d056779ef507bdd1e2671056957a031e6b487571fcc0a8627e8660645e6d61c84e561ae71cc8b6
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"babel-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "babel-jest@npm:29.7.0"
  dependencies:
    "@jest/transform": ^29.7.0
    "@types/babel__core": ^7.1.14
    babel-plugin-istanbul: ^6.1.1
    babel-preset-jest: ^29.6.3
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    slash: ^3.0.0
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: ee6f8e0495afee07cac5e4ee167be705c711a8cc8a737e05a587a131fdae2b3c8f9aa55dfd4d9c03009ac2d27f2de63d8ba96d3e8460da4d00e8af19ef9a83f7
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@istanbuljs/load-nyc-config": ^1.0.0
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-instrument: ^5.0.4
    test-exclude: ^6.0.0
  checksum: cb4fd95738219f232f0aece1116628cccff16db891713c4ccb501cddbbf9272951a5df81f2f2658dfdf4b3e7b236a9d5cbcf04d5d8c07dd5077297339598061a
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-plugin-jest-hoist@npm:29.6.3"
  dependencies:
    "@babel/template": ^7.3.3
    "@babel/types": ^7.3.3
    "@types/babel__core": ^7.1.14
    "@types/babel__traverse": ^7.0.6
  checksum: 51250f22815a7318f17214a9d44650ba89551e6d4f47a2dc259128428324b52f5a73979d010cefd921fd5a720d8c1d55ad74ff601cd94c7bd44d5f6292fde2d1
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.14":
  version: 0.4.14
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.14"
  dependencies:
    "@babel/compat-data": ^7.27.7
    "@babel/helper-define-polyfill-provider": ^0.6.5
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: d654334c1b4390d08282416144b7b6f3d74d2cab44b2bfa2b6405c828882c82907b8b67698dce1be046c218d2d4fe5bf7fb6d01879938f3129dad969e8cfc44d
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.13.0":
  version: 0.13.0
  resolution: "babel-plugin-polyfill-corejs3@npm:0.13.0"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.6.5
    core-js-compat: ^3.43.0
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: cf526031acd97ff2124e7c10e15047e6eeb0620d029c687f1dca99916a8fe6cac0e634b84c913db6cb68b7a024f82492ba8fdcc2a6266e7b05bdac2cba0c2434
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.5":
  version: 0.6.5
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.5"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.6.5
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: ed1932fa9a31e0752fd10ebf48ab9513a654987cab1182890839523cb898559d24ae0578fdc475d9f995390420e64eeaa4b0427045b56949dace3c725bc66dbb
  languageName: node
  linkType: hard

"babel-plugin-react-native-web@npm:~0.19.13":
  version: 0.19.13
  resolution: "babel-plugin-react-native-web@npm:0.19.13"
  checksum: 899165793b6e3416b87e830633d98b2bec6e29c89d838b86419a5a6e40b7042d3db98098393dfe3fc9be507054f5bcbf83c420cccfe5dc47c7d962acd1d313d5
  languageName: node
  linkType: hard

"babel-plugin-syntax-hermes-parser@npm:0.25.1, babel-plugin-syntax-hermes-parser@npm:^0.25.1":
  version: 0.25.1
  resolution: "babel-plugin-syntax-hermes-parser@npm:0.25.1"
  dependencies:
    hermes-parser: 0.25.1
  checksum: dc80fafde1aed8e60cf86ecd2e9920e7f35ffe02b33bd4e772daaa786167bcf508aac3fc1aea425ff4c7a0be94d82528f3fe8619b7f41dac853264272d640c04
  languageName: node
  linkType: hard

"babel-plugin-syntax-hermes-parser@npm:^0.28.0":
  version: 0.28.1
  resolution: "babel-plugin-syntax-hermes-parser@npm:0.28.1"
  dependencies:
    hermes-parser: 0.28.1
  checksum: 2cbc921e663463480ead9ccc8bb229a5196032367ba2b5ccb18a44faa3afa84b4dc493297749983b9a837a3d76b0b123664aecc06f9122618c3246f03e076a9d
  languageName: node
  linkType: hard

"babel-plugin-transform-flow-enums@npm:^0.0.2":
  version: 0.0.2
  resolution: "babel-plugin-transform-flow-enums@npm:0.0.2"
  dependencies:
    "@babel/plugin-syntax-flow": ^7.12.1
  checksum: fd52aef54448e01948a9d1cca0c8f87d064970c8682458962b7a222c372704bc2ce26ae8109e0ab2566e7ea5106856460f04c1a5ed794ab3bcd2f42cae1d9845
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0":
  version: 1.1.0
  resolution: "babel-preset-current-node-syntax@npm:1.1.0"
  dependencies:
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-bigint": ^7.8.3
    "@babel/plugin-syntax-class-properties": ^7.12.13
    "@babel/plugin-syntax-class-static-block": ^7.14.5
    "@babel/plugin-syntax-import-attributes": ^7.24.7
    "@babel/plugin-syntax-import-meta": ^7.10.4
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
    "@babel/plugin-syntax-top-level-await": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 9f93fac975eaba296c436feeca1031ca0539143c4066eaf5d1ba23525a31850f03b651a1049caea7287df837a409588c8252c15627ad3903f17864c8e25ed64b
  languageName: node
  linkType: hard

"babel-preset-expo@npm:~13.2.2":
  version: 13.2.2
  resolution: "babel-preset-expo@npm:13.2.2"
  dependencies:
    "@babel/helper-module-imports": ^7.25.9
    "@babel/plugin-proposal-decorators": ^7.12.9
    "@babel/plugin-proposal-export-default-from": ^7.24.7
    "@babel/plugin-syntax-export-default-from": ^7.24.7
    "@babel/plugin-transform-export-namespace-from": ^7.25.9
    "@babel/plugin-transform-flow-strip-types": ^7.25.2
    "@babel/plugin-transform-modules-commonjs": ^7.24.8
    "@babel/plugin-transform-object-rest-spread": ^7.24.7
    "@babel/plugin-transform-parameters": ^7.24.7
    "@babel/plugin-transform-private-methods": ^7.24.7
    "@babel/plugin-transform-private-property-in-object": ^7.24.7
    "@babel/plugin-transform-runtime": ^7.24.7
    "@babel/preset-react": ^7.22.15
    "@babel/preset-typescript": ^7.23.0
    "@react-native/babel-preset": 0.79.5
    babel-plugin-react-native-web: ~0.19.13
    babel-plugin-syntax-hermes-parser: ^0.25.1
    babel-plugin-transform-flow-enums: ^0.0.2
    debug: ^4.3.4
    react-refresh: ^0.14.2
    resolve-from: ^5.0.0
  peerDependencies:
    babel-plugin-react-compiler: ^19.0.0-beta-e993439-20250405
  peerDependenciesMeta:
    babel-plugin-react-compiler:
      optional: true
  checksum: 790e80ec2266e496012de2d6a21cd54358a4e074a8991cca440c8a39c2d14ed80c09918968410d9e27871801590031a20ddcef4f119978e3335f4ae94bd8f4c1
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-preset-jest@npm:29.6.3"
  dependencies:
    babel-plugin-jest-hoist: ^29.6.3
    babel-preset-current-node-syntax: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: aa4ff2a8a728d9d698ed521e3461a109a1e66202b13d3494e41eea30729a5e7cc03b3a2d56c594423a135429c37bf63a9fa8b0b9ce275298be3095a88c69f6fb
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base64-js@npm:^1.2.3, base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"basic-ftp@npm:^5.0.2":
  version: 5.0.5
  resolution: "basic-ftp@npm:5.0.5"
  checksum: bc82d1c1c61cd838eaca96d68ece888bacf07546642fb6b9b8328ed410756f5935f8cf43a42cb44bb343e0565e28e908adc54c298bd2f1a6e0976871fb11fec6
  languageName: node
  linkType: hard

"before-after-hook@npm:^2.2.0":
  version: 2.2.3
  resolution: "before-after-hook@npm:2.2.3"
  checksum: a1a2430976d9bdab4cd89cb50d27fa86b19e2b41812bf1315923b0cba03371ebca99449809226425dd3bcef20e010db61abdaff549278e111d6480034bebae87
  languageName: node
  linkType: hard

"better-opn@npm:~3.0.2":
  version: 3.0.2
  resolution: "better-opn@npm:3.0.2"
  dependencies:
    open: ^8.0.4
  checksum: 1471552fa7f733561e7f49e812be074b421153006ca744de985fb6d38939807959fc5fe9cb819cf09f864782e294704fd3b31711ea14c115baf3330a2f1135de
  languageName: node
  linkType: hard

"big-integer@npm:1.6.x":
  version: 1.6.52
  resolution: "big-integer@npm:1.6.52"
  checksum: 6e86885787a20fed96521958ae9086960e4e4b5e74d04f3ef7513d4d0ad631a9f3bde2730fc8aaa4b00419fc865f6ec573e5320234531ef37505da7da192c40b
  languageName: node
  linkType: hard

"bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: ^5.5.0
    inherits: ^2.0.4
    readable-stream: ^3.4.0
  checksum: 9e8521fa7e83aa9427c6f8ccdcba6e8167ef30cc9a22df26effcc5ab682ef91d2cbc23a239f945d099289e4bbcfae7a192e9c28c84c6202e710a0dfec3722662
  languageName: node
  linkType: hard

"boxen@npm:^8.0.1":
  version: 8.0.1
  resolution: "boxen@npm:8.0.1"
  dependencies:
    ansi-align: ^3.0.1
    camelcase: ^8.0.0
    chalk: ^5.3.0
    cli-boxes: ^3.0.0
    string-width: ^7.2.0
    type-fest: ^4.21.0
    widest-line: ^5.0.0
    wrap-ansi: ^9.0.0
  checksum: f42d9e628e03e5c84ac9cda3173f75cadbdf60ed94fc06aaeef79f7c84a8181c4d79a8f40253192a1613993036c81811ad6957f346e5aa6abb7e9d1d799cbfd5
  languageName: node
  linkType: hard

"bplist-creator@npm:0.1.0":
  version: 0.1.0
  resolution: "bplist-creator@npm:0.1.0"
  dependencies:
    stream-buffers: 2.2.x
  checksum: d4ccd88ea16c9d50c2e99f484a5f5bed34d172f6f704463585c0c9c993fd01ddb5b30d6ef486dd9393ffba3c686727f6296e8adf826ce01705bd3741477ce955
  languageName: node
  linkType: hard

"bplist-creator@npm:0.1.1":
  version: 0.1.1
  resolution: "bplist-creator@npm:0.1.1"
  dependencies:
    stream-buffers: 2.2.x
  checksum: b0d40d1d1623f1afdbb575cfc8075d742d2c4f0eb458574be809e3857752d1042a39553b3943d2d7f505dde92bcd43e1d7bdac61c9cd44475d696deb79f897ce
  languageName: node
  linkType: hard

"bplist-parser@npm:0.3.2, bplist-parser@npm:^0.3.1":
  version: 0.3.2
  resolution: "bplist-parser@npm:0.3.2"
  dependencies:
    big-integer: 1.6.x
  checksum: fad0f6eb155a9b636b4096a1725ce972a0386490d7d38df7be11a3a5645372446b7c44aacbc6626d24d2c17d8b837765361520ebf2960aeffcaf56765811620e
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: 12cb6d6310629e3048cadb003e1aca4d8c9bb5c67c3c321bafdd7e7a50155de081f78ea3e0ed92ecc75a9015e784f301efc8132383132f4f7904ad1ac529c562
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: ^1.0.0
  checksum: 01dff195e3646bc4b0d27b63d9bab84d2ebc06121ff5013ad6e5356daa5a9d6b60fa26cf73c74797f2dc3fbec112af13578d51f75228c1112b26c790a87b0488
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserslist@npm:^4.20.4, browserslist@npm:^4.24.0, browserslist@npm:^4.25.0":
  version: 4.25.1
  resolution: "browserslist@npm:4.25.1"
  dependencies:
    caniuse-lite: ^1.0.30001726
    electron-to-chromium: ^1.5.173
    node-releases: ^2.0.19
    update-browserslist-db: ^1.1.3
  bin:
    browserslist: cli.js
  checksum: 2a7e4317e809b09a436456221a1fcb8ccbd101bada187ed217f7a07a9e42ced822c7c86a0a4333d7d1b4e6e0c859d201732ffff1585d6bcacd8d226f6ddce7e3
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: ^0.4.0
  checksum: 9ba4dc58ce86300c862bffc3ae91f00b2a03b01ee07f3564beeeaf82aa243b8b03ba53f123b0b842c190d4399b94697970c8e7cf7b1ea44b61aa28c3526a4449
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer@npm:^5.4.3, buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.1.13
  checksum: e2cf8429e1c4c7b8cbd30834ac09bd61da46ce35f5c22a78e6c2f04497d6d25541b16881e30a019c6fd3154150650ccee27a308eff3e26229d788bbdeb08ab84
  languageName: node
  linkType: hard

"bundle-name@npm:^4.1.0":
  version: 4.1.0
  resolution: "bundle-name@npm:4.1.0"
  dependencies:
    run-applescript: ^7.0.0
  checksum: 1d966c8d2dbf4d9d394e53b724ac756c2414c45c01340b37743621f59cc565a435024b394ddcb62b9b335d1c9a31f4640eb648c3fec7f97ee74dc0694c9beb6c
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: e4bcd3948d289c5127591fbedf10c0b639ccbf00243504e4e127374a15c3bc8eed0d28d4aaab08ff6f1cf2abc0cce6ba3085ed32f4f90e82a5683ce0014e1b6e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: b2863d74fcf2a6948221f65d95b91b4b2d90cfe8927650b506141e669f7d5de65cea191bf788838bc40d13846b7886c5bc5c84ab96c3adbcf88ad69a72fcdc6b
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-define-property: ^1.0.0
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.2
  checksum: aa2899bce917a5392fd73bd32e71799c37c0b7ab454e0ed13af7f6727549091182aade8bbb7b55f304a5bc436d543241c14090fb8a3137e9875e23f444f4f5a9
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: ^1.0.2
    get-intrinsic: ^1.3.0
  checksum: 2f6399488d1c272f56306ca60ff696575e2b7f31daf23bc11574798c84d9f2759dceb0cb1f471a85b77f28962a7ac6411f51d283ea2e45319009a19b6ccab3b2
  languageName: node
  linkType: hard

"caller-callsite@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-callsite@npm:2.0.0"
  dependencies:
    callsites: ^2.0.0
  checksum: b685e9d126d9247b320cfdfeb3bc8da0c4be28d8fb98c471a96bc51aab3130099898a2fe3bf0308f0fe048d64c37d6d09f563958b9afce1a1e5e63d879c128a2
  languageName: node
  linkType: hard

"caller-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-path@npm:2.0.0"
  dependencies:
    caller-callsite: ^2.0.0
  checksum: 3e12ccd0c71ec10a057aac69e3ec175b721ca858c640df021ef0d25999e22f7c1d864934b596b7d47038e9b56b7ec315add042abbd15caac882998b50102fb12
  languageName: node
  linkType: hard

"callsites@npm:^2.0.0":
  version: 2.0.0
  resolution: "callsites@npm:2.0.0"
  checksum: be2f67b247df913732b7dec1ec0bbfcdbaea263e5a95968b19ec7965affae9496b970e3024317e6d4baa8e28dc6ba0cec03f46fdddc2fdcc51396600e53c2623
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase-keys@npm:^7.0.0":
  version: 7.0.2
  resolution: "camelcase-keys@npm:7.0.2"
  dependencies:
    camelcase: ^6.3.0
    map-obj: ^4.1.0
    quick-lru: ^5.1.1
    type-fest: ^1.2.1
  checksum: b5821cc48dd00e8398a30c5d6547f06837ab44de123f1b3a603d0a03399722b2fc67a485a7e47106eb02ef543c3b50c5ebaabc1242cde4b63a267c3258d2365b
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0, camelcase@npm:^6.3.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"camelcase@npm:^8.0.0":
  version: 8.0.0
  resolution: "camelcase@npm:8.0.0"
  checksum: 6da7abe997af29e80052f17aa21628c7cce14af364cef9f07a2a44d59614dd6f361d405f121938e673424d673697a8c53ad17be8c4b03b0a727307c4db8b5b5e
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001726":
  version: 1.0.30001726
  resolution: "caniuse-lite@npm:1.0.30001726"
  checksum: 6148fc2c2f46ae1faf74af932aa768031355e2394164d7ccba0caafd30310c9b4f2c723d19d5ae5fab05a3238d1bed7e98037c84199d29d40768168e848135f5
  languageName: node
  linkType: hard

"chalk@npm:5.4.1, chalk@npm:^5.3.0":
  version: 5.4.1
  resolution: "chalk@npm:5.4.1"
  checksum: 0c656f30b782fed4d99198825c0860158901f449a6b12b818b0aabad27ec970389e7e8767d0e00762175b23620c812e70c4fd92c0210e55fc2d993638b74e86e
  languageName: node
  linkType: hard

"chalk@npm:^2.0.1, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: b563e4b6039b15213114626621e7a3d12f31008bdce20f9c741d69987f62aeaace7ec30f6018890ad77b2e9b4d95324c9f5acfca58a9441e3b1dcdd1e2525d17
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 6fd5da1f5d18ff5712c1e0aed41da200d7c51c28f11b36ee3c7b483f3696dabc08927fc6b227735eb8f0e1215c9a8abd8154637f3eff8cada5959df7f58b024d
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"chrome-launcher@npm:^0.15.2":
  version: 0.15.2
  resolution: "chrome-launcher@npm:0.15.2"
  dependencies:
    "@types/node": "*"
    escape-string-regexp: ^4.0.0
    is-wsl: ^2.2.0
    lighthouse-logger: ^1.0.0
  bin:
    print-chrome-path: bin/print-chrome-path.js
  checksum: e1f8131b9f7bd931248ea85f413c6cdb93a0d41440ff5bf0987f36afb081d2b2c7b60ba6062ee7ae2dd9b052143f6b275b38c9eb115d11b49c3ea8829bad7db0
  languageName: node
  linkType: hard

"chromium-edge-launcher@npm:^0.2.0":
  version: 0.2.0
  resolution: "chromium-edge-launcher@npm:0.2.0"
  dependencies:
    "@types/node": "*"
    escape-string-regexp: ^4.0.0
    is-wsl: ^2.2.0
    lighthouse-logger: ^1.0.0
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: 9b56d1f8f18e84e34d6da89a4d97787ef323a1ade6551dcc83a6899af17c1bfc27a844c23422a29f51c6a315d1e04e2ad12595aaf07d3822335c2fce15914feb
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 3b374666a85ea3ca43fa49aa3a048d21c9b475c96eb13c133505d2324e7ae5efd6a454f41efe46a152269e9b6a00c9edbe63ec7fa1921957165aae16625acd67
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0, ci-info@npm:^3.3.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 6b19dc9b2966d1f8c2041a838217299718f15d6c4b63ae36e4674edd2bee48f780e94761286a56aa59eb305a85fbea4ddffb7630ec063e7ec7e7e5ad42549a87
  languageName: node
  linkType: hard

"ci-info@npm:^4.1.0":
  version: 4.2.0
  resolution: "ci-info@npm:4.2.0"
  checksum: 0e3726721526f54c5b17cf44ab2ed69b842c756bcb4d2b26ce279e595a80a856aec9fb38a2986a2baca3de73d15895f3a01d2771c4aad93c898aae7e3ca0ceb1
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^1.0.0":
  version: 1.4.3
  resolution: "cjs-module-lexer@npm:1.4.3"
  checksum: 221a1661a9ff4944b472c85ac7cd5029b2f2dc7f6c5f4ecf887f261503611110b43a48acb6c07f8f04109c772d1637fdb20b31252bf27058f35aa97bf5ad8b12
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"clean-stack@npm:^4.0.0":
  version: 4.2.0
  resolution: "clean-stack@npm:4.2.0"
  dependencies:
    escape-string-regexp: 5.0.0
  checksum: 373f656a31face5c615c0839213b9b542a0a48057abfb1df66900eab4dc2a5c6097628e4a0b5aa559cdfc4e66f8a14ea47be9681773165a44470ef5fb8ccc172
  languageName: node
  linkType: hard

"cli-boxes@npm:^3.0.0":
  version: 3.0.0
  resolution: "cli-boxes@npm:3.0.0"
  checksum: 637d84419d293a9eac40a1c8c96a2859e7d98b24a1a317788e13c8f441be052fc899480c6acab3acc82eaf1bccda6b7542d7cdcf5c9c3cc39227175dc098d5b2
  languageName: node
  linkType: hard

"cli-cursor@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-cursor@npm:2.1.0"
  dependencies:
    restore-cursor: ^2.0.0
  checksum: d88e97bfdac01046a3ffe7d49f06757b3126559d7e44aa2122637eb179284dc6cd49fca2fac4f67c19faaf7e6dab716b6fe1dfcd309977407d8c7578ec2d044d
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: ^3.1.0
  checksum: 2692784c6cd2fd85cfdbd11f53aea73a463a6d64a77c3e098b2b4697a20443f430c220629e1ca3b195ea5ac4a97a74c2ee411f3807abf6df2b66211fec0c0a29
  languageName: node
  linkType: hard

"cli-cursor@npm:^5.0.0":
  version: 5.0.0
  resolution: "cli-cursor@npm:5.0.0"
  dependencies:
    restore-cursor: ^5.0.0
  checksum: 1eb9a3f878b31addfe8d82c6d915ec2330cec8447ab1f117f4aa34f0137fbb3137ec3466e1c9a65bcb7557f6e486d343f2da57f253a2f668d691372dfa15c090
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.0.0, cli-spinners@npm:^2.5.0, cli-spinners@npm:^2.9.2":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 1bd588289b28432e4676cb5d40505cfe3e53f2e4e10fbe05c8a710a154d6fe0ce7836844b00d6858f740f2ffe67cdc36e0fce9c7b6a8430e80e6388d5aa4956c
  languageName: node
  linkType: hard

"cli-width@npm:^4.1.0":
  version: 4.1.0
  resolution: "cli-width@npm:4.1.0"
  checksum: 0a79cff2dbf89ef530bcd54c713703ba94461457b11e5634bd024c78796ed21401e32349c004995954e06f442d82609287e7aabf6a5f02c919a1cf3b9b6854ff
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 79648b3b0045f2e285b76fb2e24e207c6db44323581e421c3acbd0e86454cba1b37aea976ab50195a49e7384b871e6dfb2247ad7dec53c02454ac6497394cb56
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: ^2.0.4
    kind-of: ^6.0.2
    shallow-clone: ^3.0.0
  checksum: 770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: d06418b7335897209e77bdd430d04f882189582e67bd1f75a04565f3f07f5b3f119a9d670c943b6697d0afb100f03b866b3b8a1f91d4d02d72c4ecf2bb64b5dd
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 5210d9223010eb95b29df06a91116f2cf7c8e0748a9013ed853b53f362ea0e822f1e5bb054fb3cefc645239a4cf966af1f6133a3b43f40d591f3b68ed6cf0510
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.0":
  version: 1.0.2
  resolution: "collect-v8-coverage@npm:1.0.2"
  checksum: c10f41c39ab84629d16f9f6137bc8a63d332244383fc368caf2d2052b5e04c20cd1fd70f66fcf4e2422b84c8226598b776d39d5f2d2a51867cc1ed5d1982b4da
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"commander@npm:^12.0.0":
  version: 12.1.0
  resolution: "commander@npm:12.1.0"
  checksum: 68e9818b00fc1ed9cdab9eb16905551c2b768a317ae69a5e3c43924c2b20ac9bb65b27e1cab36aeda7b6496376d4da908996ba2c0b5d79463e0fb1e77935d514
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:^4.0.0":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: d7b9913ff92cae20cb577a4ac6fcc121bd6223319e54a40f51a14740a681ad5c574fd29a57da478a5f234a6fa6c52cbf0b7c641353e03c648b1ae85ba670b977
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 53501cbeee61d5157546c0bef0fedb6cdfc763a882136284bed9a07225f09a14b82d2a84e7637edfd1a679fb35ed9502fd58ef1d091e6287f60d790147f68ddc
  languageName: node
  linkType: hard

"commitlint@npm:^19.6.1":
  version: 19.8.1
  resolution: "commitlint@npm:19.8.1"
  dependencies:
    "@commitlint/cli": ^19.8.1
    "@commitlint/types": ^19.8.1
  bin:
    commitlint: cli.js
  checksum: 23e9a34b074361ec66c89573b1eba3ab65e7fe8044e22c3f044db87071817d8fe32e9e63313703c65385f5db1ab8e204eb2b3fa5e5a9481bc2fdef56eab478c1
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 59715f2fc456a73f68826285718503340b9f0dd89bfffc42749906c5cf3d4277ef11ef1cca0350d0e79204f00f1f6d83851ececc9095dc88512a697ac0b9bdcb
  languageName: node
  linkType: hard

"compare-func@npm:^2.0.0":
  version: 2.0.0
  resolution: "compare-func@npm:2.0.0"
  dependencies:
    array-ify: ^1.0.0
    dot-prop: ^5.1.0
  checksum: fb71d70632baa1e93283cf9d80f30ac97f003aabee026e0b4426c9716678079ef5fea7519b84d012cbed938c476493866a38a79760564a9e21ae9433e40e6f0d
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: ">= 1.43.0 < 2"
  checksum: 58321a85b375d39230405654721353f709d0c1442129e9a17081771b816302a012471a9b8f4864c7dbe02eef7f2aaac3c614795197092262e94b409c9be108f0
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.8.0
  resolution: "compression@npm:1.8.0"
  dependencies:
    bytes: 3.1.2
    compressible: ~2.0.18
    debug: 2.6.9
    negotiator: ~0.6.4
    on-headers: ~1.0.2
    safe-buffer: 5.2.1
    vary: ~1.1.2
  checksum: 12ca3e326b4ccb6b6e51e1d14d96fafd058ddb3be08fe888487d367d42fb4f81f25d4bf77acc517ba724370e7d74469280688baf2da8cad61062bdf62eb9fd45
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"concat-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "concat-stream@npm:2.0.0"
  dependencies:
    buffer-from: ^1.0.0
    inherits: ^2.0.3
    readable-stream: ^3.0.2
    typedarray: ^0.0.6
  checksum: d7f75d48f0ecd356c1545d87e22f57b488172811b1181d96021c7c4b14ab8855f5313280263dca44bb06e5222f274d047da3e290a38841ef87b59719bde967c7
  languageName: node
  linkType: hard

"config-chain@npm:^1.1.11":
  version: 1.1.13
  resolution: "config-chain@npm:1.1.13"
  dependencies:
    ini: ^1.3.4
    proto-list: ~1.2.1
  checksum: 828137a28e7c2fc4b7fb229bd0cd6c1397bcf83434de54347e608154008f411749041ee392cbe42fab6307e02de4c12480260bf769b7d44b778fdea3839eafab
  languageName: node
  linkType: hard

"configstore@npm:^7.0.0":
  version: 7.0.0
  resolution: "configstore@npm:7.0.0"
  dependencies:
    atomically: ^2.0.3
    dot-prop: ^9.0.0
    graceful-fs: ^4.2.11
    xdg-basedir: ^5.1.0
  checksum: 1f8f1ca51d10d5ef54a346e12dd82c81918d28144ff5f41af0a6eb65c394c0e3a37d0f91931516d8964efff8fd8802c6478d13a35a6c7924e7a6c83f11d19c16
  languageName: node
  linkType: hard

"connect@npm:^3.6.5, connect@npm:^3.7.0":
  version: 3.7.0
  resolution: "connect@npm:3.7.0"
  dependencies:
    debug: 2.6.9
    finalhandler: 1.1.2
    parseurl: ~1.3.3
    utils-merge: 1.0.1
  checksum: 96e1c4effcf219b065c7823e57351c94366d2e2a6952fa95e8212bffb35c86f1d5a3f9f6c5796d4cd3a5fdda628368b1c3cc44bf19c66cfd68fe9f9cab9177e2
  languageName: node
  linkType: hard

"conventional-changelog-angular@npm:^7.0.0":
  version: 7.0.0
  resolution: "conventional-changelog-angular@npm:7.0.0"
  dependencies:
    compare-func: ^2.0.0
  checksum: 2478962ad7ce42878449ba3568347d704f22c5c9af1cd36916b5600734bd7f82c09712a338c649195c44e907f1b0372ce52d6cb51df643f495c89af05ad4bc48
  languageName: node
  linkType: hard

"conventional-changelog-angular@npm:^8.0.0":
  version: 8.0.0
  resolution: "conventional-changelog-angular@npm:8.0.0"
  dependencies:
    compare-func: ^2.0.0
  checksum: 71f492cb4dccd46174430517177054be2e2097f1264c55419a79aa94fe4d163f98aeab7da6836473470fbfc920051a9554f46498989bdd6438648c2d7e32b42c
  languageName: node
  linkType: hard

"conventional-changelog-atom@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-changelog-atom@npm:5.0.0"
  checksum: bc35ec5476b81544b534c3e31ff3a8f59b6484c3fd34c93303e6709c83870ea7f6923e0b97052bbbc118d4cc2d3de4501e9120c9704ff40e86c70e8831040610
  languageName: node
  linkType: hard

"conventional-changelog-codemirror@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-changelog-codemirror@npm:5.0.0"
  checksum: babb18b6cfc0609b8af5ba679b8c11bdb0efad68b2401e0c014df38f195ebed27a6c16d55ca07081aeae0121dd7293544acf341de6dd3f54ea6bd90a2fbf410a
  languageName: node
  linkType: hard

"conventional-changelog-conventionalcommits@npm:^7.0.2":
  version: 7.0.2
  resolution: "conventional-changelog-conventionalcommits@npm:7.0.2"
  dependencies:
    compare-func: ^2.0.0
  checksum: e17ac5970ae09d6e9b0c3a7edaed075b836c0c09c34c514589cbe06554f46ed525067fa8150a8467cc03b1cf9af2073e7ecf48790d4f5ea399921b1cbe313711
  languageName: node
  linkType: hard

"conventional-changelog-conventionalcommits@npm:^8.0.0":
  version: 8.0.0
  resolution: "conventional-changelog-conventionalcommits@npm:8.0.0"
  dependencies:
    compare-func: ^2.0.0
  checksum: af80a3294ec833b6ca6b13874c275952391319dd0ebb771dbcf0b837a2f8504c197e894a3fc5def44574a04daa038a94cae8d00f8222e843bc788b6911a1eff4
  languageName: node
  linkType: hard

"conventional-changelog-core@npm:^8.0.0":
  version: 8.0.0
  resolution: "conventional-changelog-core@npm:8.0.0"
  dependencies:
    "@hutson/parse-repository-url": ^5.0.0
    add-stream: ^1.0.0
    conventional-changelog-writer: ^8.0.0
    conventional-commits-parser: ^6.0.0
    git-raw-commits: ^5.0.0
    git-semver-tags: ^8.0.0
    hosted-git-info: ^7.0.0
    normalize-package-data: ^6.0.0
    read-package-up: ^11.0.0
    read-pkg: ^9.0.0
  checksum: ca295a0c68592fbdd80149a496ccddf4f4e852e88f60826213c22a34c05c825984ce6d305082467268887629aeb4abae0f00e724420896090c78475661dfccda
  languageName: node
  linkType: hard

"conventional-changelog-ember@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-changelog-ember@npm:5.0.0"
  checksum: a1476f149424dbc5b60420c41c1c1691a5b0e86448dca9f86c91474ee54ac404d3d59b3e75beb43da4db3c696a4189366f67c2431c6d8dc2276fad0d2f327a67
  languageName: node
  linkType: hard

"conventional-changelog-eslint@npm:^6.0.0":
  version: 6.0.0
  resolution: "conventional-changelog-eslint@npm:6.0.0"
  checksum: e508b44ab2acc32430a0ea75a724285eed5034fecade77f9e5aa89a176d31c3ed4cf2d54a111a8cfe0f99bd69e1aeb2a046eeddc7e035605976d4cf61d6ab911
  languageName: node
  linkType: hard

"conventional-changelog-express@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-changelog-express@npm:5.0.0"
  checksum: f344f057a8756a99637029b912d2c0eb569b68e34983e8948c790bb4bfef40758b2760c0ab720b3943354da3fa76d3d77d8f42f4f4564e07240b574c3bad5d6c
  languageName: node
  linkType: hard

"conventional-changelog-jquery@npm:^6.0.0":
  version: 6.0.0
  resolution: "conventional-changelog-jquery@npm:6.0.0"
  checksum: 845134cf5d15c455f84ac9425c7307608aaa44cc5c27abf2849a35c86c62cc7134307fa67bc412aee0c1d0ef42335423c18aca66a95119c971d9c5b4a1f44c42
  languageName: node
  linkType: hard

"conventional-changelog-jshint@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-changelog-jshint@npm:5.0.0"
  dependencies:
    compare-func: ^2.0.0
  checksum: 9db03b16610f2fbc448646cbb23f1ee28704ffa1175279ee39d51e8e0010bb82000385e662633900220f6834ad84b1ecf8ccbdebcf4ae0d7710a5599de9b0d52
  languageName: node
  linkType: hard

"conventional-changelog-preset-loader@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-changelog-preset-loader@npm:5.0.0"
  checksum: 7630c2826b43f8f546f0575b46d3eb8c2ac2b5bcfae60b7d1186e9a87f07b7a689d9463afc125a40ab84a030574c9ce7965dd96e6506323e5a7d1ac2b9f2df19
  languageName: node
  linkType: hard

"conventional-changelog-writer@npm:^8.0.0":
  version: 8.1.0
  resolution: "conventional-changelog-writer@npm:8.1.0"
  dependencies:
    conventional-commits-filter: ^5.0.0
    handlebars: ^4.7.7
    meow: ^13.0.0
    semver: ^7.5.2
  bin:
    conventional-changelog-writer: dist/cli/index.js
  checksum: cf3122058186f1e0ceaaa321fdad22c4ff2cbd830115747a695be153184692800e4bf51e54487884c3809c87b52f7933a968b53f44e2a25ad56993ffc2034cf1
  languageName: node
  linkType: hard

"conventional-changelog@npm:^6.0.0":
  version: 6.0.0
  resolution: "conventional-changelog@npm:6.0.0"
  dependencies:
    conventional-changelog-angular: ^8.0.0
    conventional-changelog-atom: ^5.0.0
    conventional-changelog-codemirror: ^5.0.0
    conventional-changelog-conventionalcommits: ^8.0.0
    conventional-changelog-core: ^8.0.0
    conventional-changelog-ember: ^5.0.0
    conventional-changelog-eslint: ^6.0.0
    conventional-changelog-express: ^5.0.0
    conventional-changelog-jquery: ^6.0.0
    conventional-changelog-jshint: ^5.0.0
    conventional-changelog-preset-loader: ^5.0.0
  checksum: 78a2a74a19385e45ea69a9ef410de7cc9627cb2bada8b26850ff55999dfc3e5600138ee636dbd0c17159dcdcd81499b64d557d34dfb641d82d1b0d107c684c10
  languageName: node
  linkType: hard

"conventional-commits-filter@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-commits-filter@npm:5.0.0"
  checksum: 2345546ea9e40412558d508311d7729b38f8d4c0fd554837c10721a432e8598ec1152320f6b601a9c11c023a31bccbb5a12067736b2227de8591f4de707e11a7
  languageName: node
  linkType: hard

"conventional-commits-parser@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-commits-parser@npm:5.0.0"
  dependencies:
    JSONStream: ^1.3.5
    is-text-path: ^2.0.0
    meow: ^12.0.1
    split2: ^4.0.0
  bin:
    conventional-commits-parser: cli.mjs
  checksum: bb92a0bfe41802330d2d14ddb0f912fd65dd355f1aa294e708f4891aac95c580919a70580b9f26563c24c3335baaed2ce003104394a8fa5ba61eeb3889e45df0
  languageName: node
  linkType: hard

"conventional-commits-parser@npm:^6.0.0":
  version: 6.2.0
  resolution: "conventional-commits-parser@npm:6.2.0"
  dependencies:
    meow: ^13.0.0
  bin:
    conventional-commits-parser: dist/cli/index.js
  checksum: 57fc957d80d46b575a6ed2b193da8ea84dc85c82c54632ad1de7dcb9f8c22c55bff046827f991944c4bbe446f84b8196dd6b062cd5461f238cf75c719d904e20
  languageName: node
  linkType: hard

"conventional-recommended-bump@npm:^10.0.0":
  version: 10.0.0
  resolution: "conventional-recommended-bump@npm:10.0.0"
  dependencies:
    "@conventional-changelog/git-client": ^1.0.0
    conventional-changelog-preset-loader: ^5.0.0
    conventional-commits-filter: ^5.0.0
    conventional-commits-parser: ^6.0.0
    meow: ^13.0.0
  bin:
    conventional-recommended-bump: dist/cli/index.js
  checksum: d4a72f48ceec9947bf6f4ae346574262c8c991930a4b8d6d5d43cfd03bcf9531f74200ce60d43a251fd537b5292668f6480c17fe4ed458b4f84418db2be3af85
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 63ae9933be5a2b8d4509daca5124e20c14d023c820258e484e32dc324d34c2754e71297c94a05784064ad27615037ef677e3f0c00469fb55f409d2bb21261035
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.43.0":
  version: 3.43.0
  resolution: "core-js-compat@npm:3.43.0"
  dependencies:
    browserslist: ^4.25.0
  checksum: 32d1383c3d6bf84b8bb41b5a1e0a45bf8144ea3f7b913a7e83dd51f1252e9db9c187c4e34877b39ac5661be51a03655694c3a01e03637b895c39f7908f440c77
  languageName: node
  linkType: hard

"cosmiconfig-typescript-loader@npm:^6.1.0":
  version: 6.1.0
  resolution: "cosmiconfig-typescript-loader@npm:6.1.0"
  dependencies:
    jiti: ^2.4.1
  peerDependencies:
    "@types/node": "*"
    cosmiconfig: ">=9"
    typescript: ">=5"
  checksum: 45114854faaa97178abd2ccad511363faa57c03321c7e39ad16619c63842b3f6147dd20118f9f07c9530a242a39c3107c791708bb0b987dad374e71f23f9468b
  languageName: node
  linkType: hard

"cosmiconfig@npm:9.0.0, cosmiconfig@npm:^9.0.0":
  version: 9.0.0
  resolution: "cosmiconfig@npm:9.0.0"
  dependencies:
    env-paths: ^2.2.1
    import-fresh: ^3.3.0
    js-yaml: ^4.1.0
    parse-json: ^5.2.0
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: a30c424b53d442ea0bdd24cb1b3d0d8687c8dda4a17ab6afcdc439f8964438801619cdb66e8e79f63b9caa3e6586b60d8bab9ce203e72df6c5e80179b971fe8f
  languageName: node
  linkType: hard

"cosmiconfig@npm:^5.0.5":
  version: 5.2.1
  resolution: "cosmiconfig@npm:5.2.1"
  dependencies:
    import-fresh: ^2.0.0
    is-directory: ^0.3.1
    js-yaml: ^3.13.1
    parse-json: ^4.0.0
  checksum: 8b6f1d3c8a5ffdf663a952f17af0761adf210b7a5933d0fe8988f3ca3a1f0e1e5cbbb74d5b419c15933dd2fdcaec31dbc5cc85cb8259a822342b93b529eff89c
  languageName: node
  linkType: hard

"create-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "create-jest@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    chalk: ^4.0.0
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    jest-config: ^29.7.0
    jest-util: ^29.7.0
    prompts: ^2.0.1
  bin:
    create-jest: bin/create-jest.js
  checksum: 1427d49458adcd88547ef6fa39041e1fe9033a661293aa8d2c3aa1b4967cb5bf4f0c00436c7a61816558f28ba2ba81a94d5c962e8022ea9a883978fc8e1f2945
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.5":
  version: 3.2.0
  resolution: "cross-fetch@npm:3.2.0"
  dependencies:
    node-fetch: ^2.7.0
  checksum: 8ded5ea35f705e81e569e7db244a3f96e05e95996ff51877c89b0c1ec1163c76bb5dad77d0f8fba6bb35a0abacb36403d7271dc586d8b1f636110ee7a8d959fd
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.3, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"crypto-random-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "crypto-random-string@npm:2.0.0"
  checksum: 0283879f55e7c16fdceacc181f87a0a65c53bc16ffe1d58b9d19a6277adcd71900d02bb2c4843dd55e78c51e30e89b0fec618a7f170ebcc95b33182c28f05fd6
  languageName: node
  linkType: hard

"css-in-js-utils@npm:^3.1.0":
  version: 3.1.0
  resolution: "css-in-js-utils@npm:3.1.0"
  dependencies:
    hyphenate-style-name: ^1.0.3
  checksum: 066318e918c04a5e5bce46b38fe81052ea6ac051bcc6d3c369a1d59ceb1546cb2b6086901ab5d22be084122ee3732169996a3dfb04d3406eaee205af77aec61b
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"dargs@npm:^8.0.0":
  version: 8.1.0
  resolution: "dargs@npm:8.1.0"
  checksum: 33f1b8f5f08e72c8a28355a87c0e1a9b6a0fec99252ecd9cf4735e65dd5f2e19747c860251ed5747b38e7204c7915fd7a7146aee5aaef5882c69169aae8b1d09
  languageName: node
  linkType: hard

"data-uri-to-buffer@npm:^6.0.2":
  version: 6.0.2
  resolution: "data-uri-to-buffer@npm:6.0.2"
  checksum: 8b6927c33f9b54037f442856be0aa20e5fd49fa6c9c8ceece408dc306445d593ad72d207d57037c529ce65f413b421da800c6827b1dbefb607b8056f17123a61
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 1e1cd509c3037ac0f8ba320da3d1f8bf1a9f09b0be09394b5e40781b8cc15ff9834967ba7c9f843a425b34f9fe14ce44cf055af6662c44263424c1eb8d65659b
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 3600c91ced1cfa935f19ef2abae11029e01738de8d229354d3b2a172bf0d7e4ed08ff8f53294b715569fdf72dfeaa96aa7652f479c0f60570878d88e7e8bddf6
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: 8dd492cd51d19970876626b5b5169fbb67ca31ec1d1d3238ee6a71820ca8b80cafb141c485999db1ee1ef02f2cc3b99424c5eda8d59e852d9ebb79ab290eb5ee
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.3.5, debug@npm:^4.4.0, debug@npm:^4.4.1":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: a43826a01cda685ee4cec00fb2d3322eaa90ccadbef60d9287debc2a886be3e835d9199c80070ede75a409ee57828c4c6cd80e4b154f2843f0dc95a570dc0729
  languageName: node
  linkType: hard

"debug@npm:^3.1.0":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"decamelize-keys@npm:^1.1.0":
  version: 1.1.1
  resolution: "decamelize-keys@npm:1.1.1"
  dependencies:
    decamelize: ^1.1.0
    map-obj: ^1.0.0
  checksum: fc645fe20b7bda2680bbf9481a3477257a7f9304b1691036092b97ab04c0ab53e3bf9fcc2d2ae382536568e402ec41fb11e1d4c3836a9abe2d813dd9ef4311e0
  languageName: node
  linkType: hard

"decamelize@npm:^1.1.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: ad8c51a7e7e0720c70ec2eeb1163b66da03e7616d7b98c9ef43cce2416395e84c1e9548dd94f5f6ffecfee9f8b94251fc57121a8b021f2ff2469b2bae247b8aa
  languageName: node
  linkType: hard

"decamelize@npm:^5.0.0":
  version: 5.0.1
  resolution: "decamelize@npm:5.0.1"
  checksum: 7c3b1ed4b3e60e7fbc00a35fb248298527c1cdfe603e41dfcf05e6c4a8cb9efbee60630deb677ed428908fb4e74e322966c687a094d1478ddc9c3a74e9dc7140
  languageName: node
  linkType: hard

"dedent@npm:^0.7.0":
  version: 0.7.0
  resolution: "dedent@npm:0.7.0"
  checksum: 87de191050d9a40dd70cad01159a0bcf05ecb59750951242070b6abf9569088684880d00ba92a955b4058804f16eeaf91d604f283929b4f614d181cd7ae633d2
  languageName: node
  linkType: hard

"dedent@npm:^1.0.0":
  version: 1.6.0
  resolution: "dedent@npm:1.6.0"
  peerDependencies:
    babel-plugin-macros: ^3.1.0
  peerDependenciesMeta:
    babel-plugin-macros:
      optional: true
  checksum: ecaa83968b3db4ffeadf8f679c01280f8679ec79993d7e203c0281d7926e883bb79f42b263ba0df1f78e146e4b0be1b9a5b922b1fe040cb89b09977bc9c25b38
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2, deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 2024c6a980a1b7128084170c4cf56b0fd58a63f2da1660dcfe977415f27b17dbe5888668b59d0b063753f3220719d5e400b7f113609489c90160bb9a5518d052
  languageName: node
  linkType: hard

"default-browser-id@npm:^5.0.0":
  version: 5.0.0
  resolution: "default-browser-id@npm:5.0.0"
  checksum: 185bfaecec2c75fa423544af722a3469b20704c8d1942794a86e4364fe7d9e8e9f63241a5b769d61c8151993bc65833a5b959026fa1ccea343b3db0a33aa6deb
  languageName: node
  linkType: hard

"default-browser@npm:^5.2.1":
  version: 5.2.1
  resolution: "default-browser@npm:5.2.1"
  dependencies:
    bundle-name: ^4.1.0
    default-browser-id: ^5.0.0
  checksum: afab7eff7b7f5f7a94d9114d1ec67273d3fbc539edf8c0f80019879d53aa71e867303c6f6d7cffeb10a6f3cfb59d4f963dba3f9c96830b4540cc7339a1bf9840
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: ^1.0.2
  checksum: 3a88b7a587fc076b84e60affad8b85245c01f60f38fc1d259e7ac1d89eb9ce6abb19e27215de46b98568dd5bc48471730b327637e6f20b0f1bc85cf00440c80a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 0115fdb065e0490918ba271d7339c42453d209d4cb619dfe635870d906731eff3e1ade8028bb461ea27ce8264ec5e22c6980612d332895977e89c1bbc80fcee2
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^3.0.0":
  version: 3.0.0
  resolution: "define-lazy-prop@npm:3.0.0"
  checksum: 54884f94caac0791bf6395a3ec530ce901cf71c47b0196b8754f3fd17edb6c0e80149c1214429d851873bb0d689dbe08dcedbb2306dc45c8534a5934723851b6
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"degenerator@npm:^5.0.0":
  version: 5.0.1
  resolution: "degenerator@npm:5.0.1"
  dependencies:
    ast-types: ^0.13.4
    escodegen: ^2.1.0
    esprima: ^4.0.1
  checksum: a64fa39cdf6c2edd75188157d32338ee9de7193d7dbb2aeb4acb1eb30fa4a15ed80ba8dae9bd4d7b085472cf174a5baf81adb761aaa8e326771392c922084152
  languageName: node
  linkType: hard

"del-cli@npm:^5.1.0":
  version: 5.1.0
  resolution: "del-cli@npm:5.1.0"
  dependencies:
    del: ^7.1.0
    meow: ^10.1.3
  bin:
    del: cli.js
    del-cli: cli.js
  checksum: 7a8953d3d22716d08080d7344ce9b66fe1608ac4aa32b6106ba825eb986ed2a31ba7826c2f269a2060f013885274c8935628bb6009336adc29a36413dc660741
  languageName: node
  linkType: hard

"del@npm:^6.1.1":
  version: 6.1.1
  resolution: "del@npm:6.1.1"
  dependencies:
    globby: ^11.0.1
    graceful-fs: ^4.2.4
    is-glob: ^4.0.1
    is-path-cwd: ^2.2.0
    is-path-inside: ^3.0.2
    p-map: ^4.0.0
    rimraf: ^3.0.2
    slash: ^3.0.0
  checksum: 563288b73b8b19a7261c47fd21a330eeab6e2acd7c6208c49790dfd369127120dd7836cdf0c1eca216b77c94782a81507eac6b4734252d3bef2795cb366996b6
  languageName: node
  linkType: hard

"del@npm:^7.1.0":
  version: 7.1.0
  resolution: "del@npm:7.1.0"
  dependencies:
    globby: ^13.1.2
    graceful-fs: ^4.2.10
    is-glob: ^4.0.3
    is-path-cwd: ^3.0.0
    is-path-inside: ^4.0.0
    p-map: ^5.5.0
    rimraf: ^3.0.2
    slash: ^4.0.0
  checksum: 93527e78e95125809ff20a112814b00648ed64af204be1a565862698060c9ec8f5c5fe1a4866725acfde9b0da6423f4b7a7642c1d38cd4b05cbeb643a7b089e3
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"deprecation@npm:^2.0.0":
  version: 2.3.1
  resolution: "deprecation@npm:2.3.1"
  checksum: f56a05e182c2c195071385455956b0c4106fe14e36245b00c689ceef8e8ab639235176a96977ba7c74afb173317fac2e0ec6ec7a1c6d1e6eaa401c586c714132
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: daaaed925ffa7889bd91d56e9624e6c8033911bb60f3a50a74a87500680652969dbaab9526d1e200a4c94acf80fc862a22131841145a0a8482d60a99c24f4a3e
  languageName: node
  linkType: hard

"detect-newline@npm:^3.0.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: ae6cd429c41ad01b164c59ea36f264a2c479598e61cba7c99da24175a7ab80ddf066420f2bec9a1c57a6bead411b4655ff15ad7d281c000a89791f48cbe939e7
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.6.3":
  version: 29.6.3
  resolution: "diff-sequences@npm:29.6.3"
  checksum: f4914158e1f2276343d98ff5b31fc004e7304f5470bf0f1adb2ac6955d85a531a6458d33e87667f98f6ae52ebd3891bb47d420bb48a5bd8b7a27ee25b20e33aa
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: ^2.0.2
  checksum: a45e277f7feaed309fe658ace1ff286c6e2002ac515af0aaf37145b8baa96e49899638c7cd47dccf84c3d32abfc113246625b3ac8f552d1046072adee13b0dc8
  languageName: node
  linkType: hard

"dot-prop@npm:^5.1.0":
  version: 5.3.0
  resolution: "dot-prop@npm:5.3.0"
  dependencies:
    is-obj: ^2.0.0
  checksum: d5775790093c234ef4bfd5fbe40884ff7e6c87573e5339432870616331189f7f5d86575c5b5af2dcf0f61172990f4f734d07844b1f23482fff09e3c4bead05ea
  languageName: node
  linkType: hard

"dot-prop@npm:^9.0.0":
  version: 9.0.0
  resolution: "dot-prop@npm:9.0.0"
  dependencies:
    type-fest: ^4.18.2
  checksum: a53425ed992f136db3c591b06bcf94f46fed7136b81703121e446c961043684e8996b9ce8f87b24d2859d82c8b14c18c3b1905352bb3a1ccc5e373153f43bf48
  languageName: node
  linkType: hard

"dotenv-expand@npm:~11.0.6":
  version: 11.0.7
  resolution: "dotenv-expand@npm:11.0.7"
  dependencies:
    dotenv: ^16.4.5
  checksum: 58455ad9ffedbf6180b49f8f35596da54f10b02efcaabcba5400363f432e1da057113eee39b42365535da41df1e794d54a4aa67b22b37c41686c3dce4e6a28c5
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.5":
  version: 16.6.1
  resolution: "dotenv@npm:16.6.1"
  checksum: e8bd63c9a37f57934f7938a9cf35de698097fadf980cb6edb61d33b3e424ceccfe4d10f37130b904a973b9038627c2646a3365a904b4406514ea94d7f1816b69
  languageName: node
  linkType: hard

"dotenv@npm:~16.4.5":
  version: 16.4.7
  resolution: "dotenv@npm:16.4.7"
  checksum: c27419b5875a44addcc56cc69b7dc5b0e6587826ca85d5b355da9303c6fc317fc9989f1f18366a16378c9fdd9532d14117a1abe6029cc719cdbbef6eaef2cea4
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 149207e36f07bd4941921b0ca929e3a28f1da7bd6b6ff8ff7f4e2f2e460675af4576eeba359c635723dc189b64cdd4787e0255897d5b135ccc5d15cb8685fc90
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.173":
  version: 1.5.178
  resolution: "electron-to-chromium@npm:1.5.178"
  checksum: 7799c7c564fbee12fa2b830e3dcac480dd61b1c3eb2e05e802e5b477a1a92c4726ca9046af597b2e8008f2661aa1ab87ca853d36da17a037d172f91dfe874746
  languageName: node
  linkType: hard

"emittery@npm:^0.13.1":
  version: 0.13.1
  resolution: "emittery@npm:0.13.1"
  checksum: 2b089ab6306f38feaabf4f6f02792f9ec85fc054fda79f44f6790e61bbf6bc4e1616afb9b232e0c5ec5289a8a452f79bfa6d905a6fd64e94b49981f0934001c6
  languageName: node
  linkType: hard

"emoji-regex@npm:^10.3.0":
  version: 10.4.0
  resolution: "emoji-regex@npm:10.4.0"
  checksum: a6d9a0e454829a52e664e049847776ee1fff5646617b06cd87de7c03ce1dfcce4102a3b154d5e9c8e90f8125bc120fc1fe114d523dddf60a8a161f26c72658d2
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: abf5cd51b78082cf8af7be6785813c33b6df2068ce5191a40ca8b1afe6a86f9230af9a9ce694a5ce4665955e5c1120871826df9c128a642e09c58d592e2807fe
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.5
  resolution: "end-of-stream@npm:1.4.5"
  dependencies:
    once: ^1.4.0
  checksum: 1e0cfa6e7f49887544e03314f9dfc56a8cb6dde910cbb445983ecc2ff426fc05946df9d75d8a21a3a64f2cecfe1bf88f773952029f46756b2ed64a24e95b1fb8
  languageName: node
  linkType: hard

"env-editor@npm:^0.4.1":
  version: 0.4.2
  resolution: "env-editor@npm:0.4.2"
  checksum: d162e161d9a1bddaf63f68428c587b1d823afe7d56cde039ce403cc68706c68350c92b9db44692f4ecea1d67ec80de9ba01ca70568299ed929d3fa056c40aebf
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0, env-paths@npm:^2.2.1":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: ^1.3.4
  checksum: 3b916d2d14c6682f287c8bfa28e14672f47eafe832701080e420e7cdbaebb2c50293868256a95706ac2330fe078cf5664713158b49bc30d7a5f2ac229ded0e18
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9, es-abstract@npm:^1.24.0":
  version: 1.24.0
  resolution: "es-abstract@npm:1.24.0"
  dependencies:
    array-buffer-byte-length: ^1.0.2
    arraybuffer.prototype.slice: ^1.0.4
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    data-view-buffer: ^1.0.2
    data-view-byte-length: ^1.0.2
    data-view-byte-offset: ^1.0.1
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    es-set-tostringtag: ^2.1.0
    es-to-primitive: ^1.3.0
    function.prototype.name: ^1.1.8
    get-intrinsic: ^1.3.0
    get-proto: ^1.0.1
    get-symbol-description: ^1.1.0
    globalthis: ^1.0.4
    gopd: ^1.2.0
    has-property-descriptors: ^1.0.2
    has-proto: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    internal-slot: ^1.1.0
    is-array-buffer: ^3.0.5
    is-callable: ^1.2.7
    is-data-view: ^1.0.2
    is-negative-zero: ^2.0.3
    is-regex: ^1.2.1
    is-set: ^2.0.3
    is-shared-array-buffer: ^1.0.4
    is-string: ^1.1.1
    is-typed-array: ^1.1.15
    is-weakref: ^1.1.1
    math-intrinsics: ^1.1.0
    object-inspect: ^1.13.4
    object-keys: ^1.1.1
    object.assign: ^4.1.7
    own-keys: ^1.0.1
    regexp.prototype.flags: ^1.5.4
    safe-array-concat: ^1.1.3
    safe-push-apply: ^1.0.0
    safe-regex-test: ^1.1.0
    set-proto: ^1.0.0
    stop-iteration-iterator: ^1.1.0
    string.prototype.trim: ^1.2.10
    string.prototype.trimend: ^1.0.9
    string.prototype.trimstart: ^1.0.8
    typed-array-buffer: ^1.0.3
    typed-array-byte-length: ^1.0.3
    typed-array-byte-offset: ^1.0.4
    typed-array-length: ^1.0.7
    unbox-primitive: ^1.1.0
    which-typed-array: ^1.1.19
  checksum: 06b3d605e56e3da9d16d4db2629a42dac1ca31f2961a41d15c860422a266115e865b43e82d6b9da81a0fabbbb65ebc12fb68b0b755bc9dbddacb6bf7450e96df
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-iterator-helpers@npm:1.2.1"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-abstract: ^1.23.6
    es-errors: ^1.3.0
    es-set-tostringtag: ^2.0.3
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.6
    globalthis: ^1.0.4
    gopd: ^1.2.0
    has-property-descriptors: ^1.0.2
    has-proto: ^1.2.0
    has-symbols: ^1.1.0
    internal-slot: ^1.1.0
    iterator.prototype: ^1.1.4
    safe-array-concat: ^1.1.3
  checksum: 952808dd1df3643d67ec7adf20c30b36e5eecadfbf36354e6f39ed3266c8e0acf3446ce9bc465e38723d613cb1d915c1c07c140df65bdce85da012a6e7bda62b
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: ^1.3.0
  checksum: 214d3767287b12f36d3d7267ef342bbbe1e89f899cfd67040309fc65032372a8e60201410a99a1645f2f90c1912c8c49c8668066f6bdd954bcd614dda2e3da97
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3, es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 789f35de4be3dc8d11fdcb91bc26af4ae3e6d602caa93299a8c45cf05d36cc5081454ae2a6d3afa09cceca214b76c046e4f8151e092e6fc7feeb5efb9e794fc6
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2":
  version: 1.1.0
  resolution: "es-shim-unscopables@npm:1.1.0"
  dependencies:
    hasown: ^2.0.2
  checksum: 33cfb1ebcb2f869f0bf528be1a8660b4fe8b6cec8fc641f330e508db2284b58ee2980fad6d0828882d22858c759c0806076427a3673b6daa60f753e3b558ee15
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: ^1.2.7
    is-date-object: ^1.0.5
    is-symbol: ^1.0.4
  checksum: 966965880356486cd4d1fe9a523deda2084c81b3702d951212c098f5f2ee93605d1b7c1840062efb48a07d892641c7ed1bc194db563645c0dd2b919cb6d65b93
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 47b029c83de01b0d17ad99ed766347b974b0d628e848de404018f3abee728e987da0d2d370ad4574aa3d5b5bfc368754fd085d69a30f8e75903486ec4b5b709e
  languageName: node
  linkType: hard

"escape-goat@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-goat@npm:4.0.0"
  checksum: 7034e0025eec7b751074b837f10312c5b768493265bdad046347c0aadbc1e652776f7e5df94766473fecb5d3681169cc188fe9ccc1e22be53318c18be1671cc0
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:5.0.0, escape-string-regexp@npm:^5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 20daabe197f3cb198ec28546deebcf24b3dbb1a5a269184381b3116d12f0532e06007f4bc8da25669d6a7f8efb68db0758df4cd981f57bc5b57f521a3e12c59e
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 9f8a2d5743677c16e85c810e3024d54f0c8dea6424fad3c79ef6666e81dd0846f7437f5e729dfcdac8981bc9e5294c39b4580814d114076b8d36318f46ae4395
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"escodegen@npm:^2.1.0":
  version: 2.1.0
  resolution: "escodegen@npm:2.1.0"
  dependencies:
    esprima: ^4.0.1
    estraverse: ^5.2.0
    esutils: ^2.0.2
    source-map: ~0.6.1
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: bin/escodegen.js
    esgenerate: bin/esgenerate.js
  checksum: 096696407e161305cd05aebb95134ad176708bc5cb13d0dcc89a5fcbb959b8ed757e7f2591a5f8036f8f4952d4a724de0df14cd419e29212729fa6df5ce16bf6
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^10.1.1":
  version: 10.1.5
  resolution: "eslint-config-prettier@npm:10.1.5"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 6d8eb41c716fc2b073bfd9daede0c87c4c2b29055f15c94618eec00a19c5499024ddb89902d6793b5ac9102075e4739d7cc2d4d58c7e7ed4cc2dcf79e3fbcacc
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^8.5.0":
  version: 8.10.0
  resolution: "eslint-config-prettier@npm:8.10.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 153266badd477e49b0759816246b2132f1dbdb6c7f313ca60a9af5822fd1071c2bc5684a3720d78b725452bbac04bb130878b2513aea5e72b1b792de5a69fec8
  languageName: node
  linkType: hard

"eslint-plugin-eslint-comments@npm:^3.2.0":
  version: 3.2.0
  resolution: "eslint-plugin-eslint-comments@npm:3.2.0"
  dependencies:
    escape-string-regexp: ^1.0.5
    ignore: ^5.0.5
  peerDependencies:
    eslint: ">=4.19.1"
  checksum: c9fe273dd56699abdf7e416cfad0344eb50aa01564a5a9133e72d982defb89310bc2e9b0b148ce19c5190d7ff641223b0ba9e667a194bc48467c3dd0d471e657
  languageName: node
  linkType: hard

"eslint-plugin-ft-flow@npm:^2.0.1":
  version: 2.0.3
  resolution: "eslint-plugin-ft-flow@npm:2.0.3"
  dependencies:
    lodash: ^4.17.21
    string-natural-compare: ^3.0.1
  peerDependencies:
    "@babel/eslint-parser": ^7.12.0
    eslint: ^8.1.0
  checksum: 6272f7c352154875dc85c7dcd7cf66f6ed926a9a6aba81c675583bcc6695147597d6b9a6db0f643a387d14eccd61dc36daf20eec1c49e91ce1c63c01ffe295f7
  languageName: node
  linkType: hard

"eslint-plugin-jest@npm:^27.9.0":
  version: 27.9.0
  resolution: "eslint-plugin-jest@npm:27.9.0"
  dependencies:
    "@typescript-eslint/utils": ^5.10.0
  peerDependencies:
    "@typescript-eslint/eslint-plugin": ^5.0.0 || ^6.0.0 || ^7.0.0
    eslint: ^7.0.0 || ^8.0.0
    jest: "*"
  peerDependenciesMeta:
    "@typescript-eslint/eslint-plugin":
      optional: true
    jest:
      optional: true
  checksum: e2a4b415105408de28ad146818fcc6f4e122f6a39c6b2216ec5c24a80393f1390298b20231b0467bc5fd730f6e24b05b89e1a6a3ce651fc159aa4174ecc233d0
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.2.3":
  version: 5.5.1
  resolution: "eslint-plugin-prettier@npm:5.5.1"
  dependencies:
    prettier-linter-helpers: ^1.0.0
    synckit: ^0.11.7
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    eslint-config-prettier: ">= 7.0.0 <10.0.0 || >=10.1.0"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 913ecebfeab932ed510d8608875594d2529dc28957eed0d56a896e9862257031038a9f0921dcf198d61475ef0f5904332a7f1839be387d0a314c91f3cfbf3817
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^4.6.0":
  version: 4.6.2
  resolution: "eslint-plugin-react-hooks@npm:4.6.2"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
  checksum: 395c433610f59577cfcf3f2e42bcb130436c8a0b3777ac64f441d88c5275f4fcfc89094cedab270f2822daf29af1079151a7a6579a8e9ea8cee66540ba0384c4
  languageName: node
  linkType: hard

"eslint-plugin-react-native-globals@npm:^0.1.1":
  version: 0.1.2
  resolution: "eslint-plugin-react-native-globals@npm:0.1.2"
  checksum: ab91e8ecbb51718fb0763f29226b1c2d402251ab2c4730a8bf85f38b805e32d4243da46d07ccdb12cb9dcce9e7514364a1706142cf970f58dcc9a820bcf4b732
  languageName: node
  linkType: hard

"eslint-plugin-react-native@npm:^4.0.0":
  version: 4.1.0
  resolution: "eslint-plugin-react-native@npm:4.1.0"
  dependencies:
    eslint-plugin-react-native-globals: ^0.1.1
  peerDependencies:
    eslint: ^3.17.0 || ^4 || ^5 || ^6 || ^7 || ^8
  checksum: b6acc5aa91f95cb4600d6ab4c00cf22577083e72c61aabcf010f4388d97e4fc53ba075db54eeee53cba25b297e1a6ec611434f2c2d0bfb3e8dc6419400663fe9
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.30.1":
  version: 7.37.5
  resolution: "eslint-plugin-react@npm:7.37.5"
  dependencies:
    array-includes: ^3.1.8
    array.prototype.findlast: ^1.2.5
    array.prototype.flatmap: ^1.3.3
    array.prototype.tosorted: ^1.1.4
    doctrine: ^2.1.0
    es-iterator-helpers: ^1.2.1
    estraverse: ^5.3.0
    hasown: ^2.0.2
    jsx-ast-utils: ^2.4.1 || ^3.0.0
    minimatch: ^3.1.2
    object.entries: ^1.1.9
    object.fromentries: ^2.0.8
    object.values: ^1.2.1
    prop-types: ^15.8.1
    resolve: ^2.0.0-next.5
    semver: ^6.3.1
    string.prototype.matchall: ^4.0.12
    string.prototype.repeat: ^1.0.0
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: 8675e7558e646e3c2fcb04bb60cfe416000b831ef0b363f0117838f5bfc799156113cb06058ad4d4b39fc730903b7360b05038da11093064ca37caf76b7cf2ca
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1, eslint-scope@npm:^5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.4.0":
  version: 8.4.0
  resolution: "eslint-scope@npm:8.4.0"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: cf88f42cd5e81490d549dc6d350fe01e6fe420f9d9ea34f134bb359b030e3c4ef888d36667632e448937fe52449f7181501df48c08200e3d3b0fee250d05364e
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^2.1.0":
  version: 2.1.0
  resolution: "eslint-visitor-keys@npm:2.1.0"
  checksum: e3081d7dd2611a35f0388bbdc2f5da60b3a3c5b8b6e928daffff7391146b434d691577aa95064c8b7faad0b8a680266bcda0a42439c18c717b80e6718d7e267d
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-visitor-keys@npm:4.2.1"
  checksum: 3a77e3f99a49109f6fb2c5b7784bc78f9743b834d238cdba4d66c602c6b52f19ed7bcd0a5c5dbbeae3a8689fd785e76c001799f53d2228b278282cf9f699fff5
  languageName: node
  linkType: hard

"eslint@npm:^9.22.0":
  version: 9.30.1
  resolution: "eslint@npm:9.30.1"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.12.1
    "@eslint/config-array": ^0.21.0
    "@eslint/config-helpers": ^0.3.0
    "@eslint/core": ^0.14.0
    "@eslint/eslintrc": ^3.3.1
    "@eslint/js": 9.30.1
    "@eslint/plugin-kit": ^0.3.1
    "@humanfs/node": ^0.16.6
    "@humanwhocodes/module-importer": ^1.0.1
    "@humanwhocodes/retry": ^0.4.2
    "@types/estree": ^1.0.6
    "@types/json-schema": ^7.0.15
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.6
    debug: ^4.3.2
    escape-string-regexp: ^4.0.0
    eslint-scope: ^8.4.0
    eslint-visitor-keys: ^4.2.1
    espree: ^10.4.0
    esquery: ^1.5.0
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^8.0.0
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    json-stable-stringify-without-jsonify: ^1.0.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: e6723b98ba19ff17cf0cacb29c3c0ea5c7c6b6fb648136b2d009e7e2da4980a2562c9523623b0faf449750e890f3b274b20bee11fa9c8f43362d235485ba2f91
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.4.0":
  version: 10.4.0
  resolution: "espree@npm:10.4.0"
  dependencies:
    acorn: ^8.15.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^4.2.1
  checksum: 5f9d0d7c81c1bca4bfd29a55270067ff9d575adb8c729a5d7f779c2c7b910bfc68ccf8ec19b29844b707440fc159a83868f22c8e87bbf7cbcb225ed067df6c85
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:^4.0.1, esprima@npm:~4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0, event-target-shim@npm:^5.0.1":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 1ffe3bb22a6d51bdeb6bf6f7cf97d2ff4a74b017ad12284cc9e6a279e727dc30a5de6bb613e5596ff4dc3e517841339ad09a7eec44266eccb1aa201a30448166
  languageName: node
  linkType: hard

"exec-async@npm:^2.2.0":
  version: 2.2.0
  resolution: "exec-async@npm:2.2.0"
  checksum: 5877d83c2d553994accb39c26f40f0a633bca10d9572696e524fd91b385060ba05d1edcc28d6e3899c451e65ed453fdc7e6b69bd5d5a27d914220a100f81bb3a
  languageName: node
  linkType: hard

"execa@npm:8.0.0":
  version: 8.0.0
  resolution: "execa@npm:8.0.0"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^8.0.1
    human-signals: ^5.0.0
    is-stream: ^3.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^5.1.0
    onetime: ^6.0.0
    signal-exit: ^4.1.0
    strip-final-newline: ^3.0.0
  checksum: 295bbe73be190a0b2f031a13eb57574594e59663f253608c1ea7ebdfbffe9081080df13914ac9c93feabd5461473ccf35694d2ea8f2ba25933f189dc1adc2f7c
  languageName: node
  linkType: hard

"execa@npm:^4.0.3":
  version: 4.1.0
  resolution: "execa@npm:4.1.0"
  dependencies:
    cross-spawn: ^7.0.0
    get-stream: ^5.0.0
    human-signals: ^1.1.1
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.0
    onetime: ^5.1.0
    signal-exit: ^3.0.2
    strip-final-newline: ^2.0.0
  checksum: e30d298934d9c52f90f3847704fd8224e849a081ab2b517bbc02f5f7732c24e56a21f14cb96a08256deffeb2d12b2b7cb7e2b014a12fb36f8d3357e06417ed55
  languageName: node
  linkType: hard

"execa@npm:^5.0.0, execa@npm:^5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.0
    human-signals: ^2.1.0
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.1
    onetime: ^5.1.2
    signal-exit: ^3.0.3
    strip-final-newline: ^2.0.0
  checksum: fba9022c8c8c15ed862847e94c252b3d946036d7547af310e344a527e59021fd8b6bb0723883ea87044dc4f0201f949046993124a42ccb0855cae5bf8c786343
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: abc407f07a875c3961e4781dfcb743b58d6c93de9ab263f4f8c9d23bb6da5f9b7764fc773f86b43dd88030444d5ab8abcb611cb680fba8ca075362b77114bba3
  languageName: node
  linkType: hard

"expect@npm:^29.0.0, expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "expect@npm:29.7.0"
  dependencies:
    "@jest/expect-utils": ^29.7.0
    jest-get-type: ^29.6.3
    jest-matcher-utils: ^29.7.0
    jest-message-util: ^29.7.0
    jest-util: ^29.7.0
  checksum: 9257f10288e149b81254a0fda8ffe8d54a7061cd61d7515779998b012579d2b8c22354b0eb901daf0145f347403da582f75f359f4810c007182ad3fb318b5c0c
  languageName: node
  linkType: hard

"expo-asset@npm:~11.1.6":
  version: 11.1.6
  resolution: "expo-asset@npm:11.1.6"
  dependencies:
    "@expo/image-utils": ^0.7.5
    expo-constants: ~17.1.6
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
  checksum: 16e60f14482a22018676e1ce92d4b1d0641cfd1d7dc0fcb0100ee5b4bcf3629dceeb8c813f78ad5a47b5d22a8ac383987354a2ae7b649db1f633a9f367aa8be3
  languageName: node
  linkType: hard

"expo-constants@npm:~17.1.6":
  version: 17.1.6
  resolution: "expo-constants@npm:17.1.6"
  dependencies:
    "@expo/config": ~11.0.9
    "@expo/env": ~1.0.5
  peerDependencies:
    expo: "*"
    react-native: "*"
  checksum: 8586157be83de7010fa70e5a6c4eddaf0cd2a7cd88c531b59aef72d92d76627167413e5c5a81b15f179a74cdc6e558756e49d3c549349ff7f9459c9dd2e32240
  languageName: node
  linkType: hard

"expo-file-system@npm:~18.1.11":
  version: 18.1.11
  resolution: "expo-file-system@npm:18.1.11"
  peerDependencies:
    expo: "*"
    react-native: "*"
  checksum: 8cc9d7cf0835d328174e414be8c2c69ca145716ce7ac83033f7197ee4309b53b435a86b76a3d3072d374b340ae61eb1f8ababdc9aebc5d9e8958944d7904a6c2
  languageName: node
  linkType: hard

"expo-font@npm:~13.3.2":
  version: 13.3.2
  resolution: "expo-font@npm:13.3.2"
  dependencies:
    fontfaceobserver: ^2.1.0
  peerDependencies:
    expo: "*"
    react: "*"
  checksum: ea257395f2267c6d5c491561a415a57e3a55473428da1c7c7b1639cc386525fca8b56793d40f079e21ad1ccbb56e3180ecd943dbecc8c5543146e8760f930376
  languageName: node
  linkType: hard

"expo-keep-awake@npm:~14.1.4":
  version: 14.1.4
  resolution: "expo-keep-awake@npm:14.1.4"
  peerDependencies:
    expo: "*"
    react: "*"
  checksum: fd3adfd3f1bacb06c244c4c21452de892c55fe2ee0eed9e9912292ceafb1cdb0ea58941c4f77395032f37321113321d23ac142d63b9d0d214b7821d78b74d60e
  languageName: node
  linkType: hard

"expo-modules-autolinking@npm:2.1.13":
  version: 2.1.13
  resolution: "expo-modules-autolinking@npm:2.1.13"
  dependencies:
    "@expo/spawn-async": ^1.7.2
    chalk: ^4.1.0
    commander: ^7.2.0
    find-up: ^5.0.0
    glob: ^10.4.2
    require-from-string: ^2.0.2
    resolve-from: ^5.0.0
  bin:
    expo-modules-autolinking: bin/expo-modules-autolinking.js
  checksum: 5cd02a9a0cb0c9c6144b082cfd17d413398ac9140ec269822f0e19cde94e49f5f800aa974c02651a7de1d410ef746acd73216edc367b8a107bcb2b8ba1d4cbc1
  languageName: node
  linkType: hard

"expo-modules-core@npm:2.4.2":
  version: 2.4.2
  resolution: "expo-modules-core@npm:2.4.2"
  dependencies:
    invariant: ^2.2.4
  checksum: d78392bc3f3ccf5d1e1f081e01149dc7b3d81e8c4736e08e691c81a24c9143b2239474772723c1e3ef65a8a89e4aa19d7ece2143f076750d014d9d1d75051684
  languageName: node
  linkType: hard

"expo-status-bar@npm:~2.2.3":
  version: 2.2.3
  resolution: "expo-status-bar@npm:2.2.3"
  dependencies:
    react-native-edge-to-edge: 1.6.0
    react-native-is-edge-to-edge: ^1.1.6
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 37e8a111037a3dc94ada9c0549e1b7bae0d6dcef48392edf7a559f64d5db78560e11165f5a8d5fe3a15e6d0bd3895b782443e7bb3018eb2798a11b509f3acf97
  languageName: node
  linkType: hard

"expo@npm:~53.0.16":
  version: 53.0.16
  resolution: "expo@npm:53.0.16"
  dependencies:
    "@babel/runtime": ^7.20.0
    "@expo/cli": 0.24.17
    "@expo/config": ~11.0.11
    "@expo/config-plugins": ~10.1.0
    "@expo/fingerprint": 0.13.3
    "@expo/metro-config": 0.20.16
    "@expo/vector-icons": ^14.0.0
    babel-preset-expo: ~13.2.2
    expo-asset: ~11.1.6
    expo-constants: ~17.1.6
    expo-file-system: ~18.1.11
    expo-font: ~13.3.2
    expo-keep-awake: ~14.1.4
    expo-modules-autolinking: 2.1.13
    expo-modules-core: 2.4.2
    react-native-edge-to-edge: 1.6.0
    whatwg-url-without-unicode: 8.0.0-3
  peerDependencies:
    "@expo/dom-webview": "*"
    "@expo/metro-runtime": "*"
    react: "*"
    react-native: "*"
    react-native-webview: "*"
  peerDependenciesMeta:
    "@expo/dom-webview":
      optional: true
    "@expo/metro-runtime":
      optional: true
    react-native-webview:
      optional: true
  bin:
    expo: bin/cli
    expo-modules-autolinking: bin/autolinking
    fingerprint: bin/fingerprint
  checksum: 38c8c12647b2bc3a39a7d310c133402940d4c24137e6e2835a6befd38acf25adb728ea2ba5e1cba0d82aa9a18c3fc62995b3540afea77a71eb1831e4e961ab43
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 7e191e3dd6edd8c56c88f2c8037c98fbb8034fe48778be53ed8cb30ccef371a061a4e999a469aab939b92f8f12698f3b426d52f4f76b7a20da5f9f98c3cbc862
  languageName: node
  linkType: hard

"external-editor@npm:^3.1.0":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: ^0.7.0
    iconv-lite: ^0.4.24
    tmp: ^0.0.33
  checksum: 1c2a616a73f1b3435ce04030261bed0e22d4737e14b090bb48e58865da92529c9f2b05b893de650738d55e692d071819b45e1669259b2b354bc3154d27a698c7
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: d22d371b994fdc8cce9ff510d7b8dc4da70ac327bcba20df607dd5b9cae9f908f4d1028f5fe467650f058d1e7270235ae0b8230809a262b4df587a3b3aa216c3
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.0, fast-glob@npm:^3.3.2, fast-glob@npm:^3.3.3":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.8
  checksum: 0704d7b85c0305fd2cef37777337dfa26230fdd072dce9fb5c82a4b03156f3ffb8ed3e636033e65d45d2a5805a4e475825369a27404c0307f2db0c8eb3366fbd
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0, fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 7161ba2a7944778d679ba8e5f00d6a2bb479a2142df0982f541d67be6c979b17808f7edbb0ce78161c85035974bde3fa52b5137df31da46c0828cb629ba67c4e
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: ^1.0.4
  checksum: 7691d1794fb84ad0ec2a185f10e00f0e1713b894e2c9c4d42f0bc0ba5f8c00e6e655a202074ca0b91b9c3d977aab7c30c41a8dc069fb5368576ac0054870a0e6
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: 2.1.1
  checksum: b15a124cef28916fe07b400eb87cbc73ca082c142abf7ca8e8de6af43eca79ca7bd13eb4d4d48240b3bd3136eaac40d16e42d6edf87a8e5d1dd8070626860c78
  languageName: node
  linkType: hard

"fbjs-css-vars@npm:^1.0.0":
  version: 1.0.2
  resolution: "fbjs-css-vars@npm:1.0.2"
  checksum: 72baf6d22c45b75109118b4daecb6c8016d4c83c8c0f23f683f22e9d7c21f32fff6201d288df46eb561e3c7d4bb4489b8ad140b7f56444c453ba407e8bd28511
  languageName: node
  linkType: hard

"fbjs@npm:^3.0.4":
  version: 3.0.5
  resolution: "fbjs@npm:3.0.5"
  dependencies:
    cross-fetch: ^3.1.5
    fbjs-css-vars: ^1.0.0
    loose-envify: ^1.0.0
    object-assign: ^4.1.0
    promise: ^7.1.1
    setimmediate: ^1.0.5
    ua-parser-js: ^1.0.35
  checksum: e609b5b64686bc96495a5c67728ed9b2710b9b3d695c5759c5f5e47c9483d1c323543ac777a86459e3694efc5712c6ce7212e944feb19752867d699568bb0e54
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: fe9f3014901d023cf631831dcb9eae5447f4d7f69218001dd01ecf007eccc40f6c129a04411b5cc273a5f93c14e02e971e17270afc9022041c80be924091eb6f
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: ^4.0.0
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"finalhandler@npm:1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: 2.6.9
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    on-finished: ~2.3.0
    parseurl: ~1.3.3
    statuses: ~1.5.0
    unpipe: ~1.0.0
  checksum: 617880460c5138dd7ccfd555cb5dde4d8f170f4b31b8bd51e4b646bb2946c30f7db716428a1f2882d730d2b72afb47d1f67cc487b874cb15426f95753a88965e
  languageName: node
  linkType: hard

"find-cache-dir@npm:^2.0.0":
  version: 2.1.0
  resolution: "find-cache-dir@npm:2.1.0"
  dependencies:
    commondir: ^1.0.1
    make-dir: ^2.0.0
    pkg-dir: ^3.0.0
  checksum: 60ad475a6da9f257df4e81900f78986ab367d4f65d33cf802c5b91e969c28a8762f098693d7a571b6e4dd4c15166c2da32ae2d18b6766a18e2071079448fdce4
  languageName: node
  linkType: hard

"find-up-simple@npm:^1.0.0":
  version: 1.0.1
  resolution: "find-up-simple@npm:1.0.1"
  checksum: 6e374bffda9f8425314eab47ef79752b6e77dcc95c0ad17d257aef48c32fe07bbc41bcafbd22941c25bb94fffaaaa8e178d928867d844c58100c7fe19ec82f72
  languageName: node
  linkType: hard

"find-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-up@npm:3.0.0"
  dependencies:
    locate-path: ^3.0.0
  checksum: 38eba3fe7a66e4bc7f0f5a1366dc25508b7cfc349f852640e3678d26ad9a6d7e2c43eff0a472287de4a9753ef58f066a0ea892a256fa3636ad51b3fe1e17fae9
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"find-up@npm:^7.0.0":
  version: 7.0.0
  resolution: "find-up@npm:7.0.0"
  dependencies:
    locate-path: ^7.2.0
    path-exists: ^5.0.0
    unicorn-magic: ^0.1.0
  checksum: e1c63860f9c04355ab2aa19f4be51c1a6e14a7d8cfbd8090e2be6da2a36a76995907cb45337a4b582b19b164388f71d6ab118869dc7bffb2093f2c089ecb95ee
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.4
  checksum: 899fc86bf6df093547d76e7bfaeb900824b869d7d457d02e9b8aae24836f0a99fbad79328cfd6415ee8908f180699bf259dc7614f793447cb14f707caf5996f6
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"flow-enums-runtime@npm:^0.0.6":
  version: 0.0.6
  resolution: "flow-enums-runtime@npm:0.0.6"
  checksum: c60412ed6d43b26bf5dfa66be8e588c3ccdb20191fd269e02ca7e8e1d350c73a327cc9a7edb626c80c31eb906981945d12a87ca37118985f33406303806dab79
  languageName: node
  linkType: hard

"flow-parser@npm:0.*":
  version: 0.274.2
  resolution: "flow-parser@npm:0.274.2"
  checksum: 1c3ffb0d7fba1ec5ceb4d9dfdf1e37a2bcf33a4a870f06f9aff8368821074d3a0d972cda859670596a4c55878a991fb03a4530de5ac07a322fbfc4baabed6973
  languageName: node
  linkType: hard

"fontfaceobserver@npm:^2.1.0":
  version: 2.3.0
  resolution: "fontfaceobserver@npm:2.3.0"
  checksum: 5f14715974203b9d68f299f93a7623afd9d5701572d683e861cdbb7514573ac556f56e9b5d07d2d534e01aed19a3b0bbe568e735e0e5494cbea913fc3f12b856
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: ^1.2.7
  checksum: 3c986d7e11f4381237cc98baa0a2f87eabe74719eee65ed7bed275163082b940ede19268c61d04c6260e0215983b12f8d885e3c8f9aa8c2113bf07c37051745c
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: ^7.0.6
    signal-exit: ^4.0.1
  checksum: b2c1a6fc0bf0233d645d9fefdfa999abf37db1b33e5dab172b3cbfb0662b88bfbd2c9e7ab853533d199050ec6b65c03fcf078fc212d26e4990220e98c6930eef
  languageName: node
  linkType: hard

"freeport-async@npm:^2.0.0":
  version: 2.0.0
  resolution: "freeport-async@npm:2.0.0"
  checksum: 03156ab2179fbbf5b7ff3aafc56f3e01c9d7df5cc366fbf3c29f26007773632e33ed90847fa4a979c5412ad55de8b21a7292601c531acaf8957933d96225c76d
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 13ea8b08f91e669a64e3ba3a20eb79d7ca5379a81f1ff7f4310d54e2320645503cc0c78daedc93dfb6191287295f6479544a649c64d8e41a1c0fb0c221552346
  languageName: node
  linkType: hard

"fs-extra@npm:^10.1.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: dc94ab37096f813cc3ca12f0f1b5ad6744dfed9ed21e953d72530d103cea193c2f81584a39e9dee1bea36de5ee66805678c0dddc048e8af1427ac19c00fffc50
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@^2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    functions-have-names: ^1.2.3
    hasown: ^2.0.2
    is-callable: ^1.2.7
  checksum: 3a366535dc08b25f40a322efefa83b2da3cd0f6da41db7775f2339679120ef63b6c7e967266182609e655b8f0a8f65596ed21c7fd72ad8bd5621c2340edd4010
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-east-asian-width@npm:^1.0.0":
  version: 1.3.0
  resolution: "get-east-asian-width@npm:1.3.0"
  checksum: 757a34c7a46ff385e2775f96f9d3e553f6b6666a8898fb89040d36a1010fba692332772945606a7d4b0f0c6afb84cd394e75d5477c56e1f00f1eb79603b0aecc
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: ^1.0.2
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    function-bind: ^1.1.2
    get-proto: ^1.0.1
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.1.0
  checksum: 301008e4482bb9a9cb49e132b88fee093bff373b4e6def8ba219b1e96b60158a6084f273ef5cafe832e42cd93462f4accb46a618d35fe59a2b507f2388c5b79d
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: bba0811116d11e56d702682ddef7c73ba3481f114590e705fc549f4d868972263896af313c57a25c076e3c0d567e11d919a64ba1b30c879be985fc9d44f96148
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: ^1.0.1
    es-object-atoms: ^1.0.0
  checksum: 4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-stream@npm:^5.0.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: ^3.0.0
  checksum: 8bc1a23174a06b2b4ce600df38d6c98d2ef6d84e020c1ddad632ad75bac4e092eeb40e4c09e0761c35fc2dbc5e7fff5dab5e763a383582c4a167dd69a905bd12
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: e04ecece32c92eebf5b8c940f51468cd53554dcbb0ea725b2748be583c9523d00128137966afce410b9b051eb2ef16d657cd2b120ca8edafcf5a65e81af63cad
  languageName: node
  linkType: hard

"get-stream@npm:^8.0.1":
  version: 8.0.1
  resolution: "get-stream@npm:8.0.1"
  checksum: 01e3d3cf29e1393f05f44d2f00445c5f9ec3d1c49e8179b31795484b9c117f4c695e5e07b88b50785d5c8248a788c85d9913a79266fc77e3ef11f78f10f1b974
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
  checksum: 655ed04db48ee65ef2ddbe096540d4405e79ba0a7f54225775fef43a7e2afcb93a77d141c5f05fdef0afce2eb93bcbfb3597142189d562ac167ff183582683cd
  languageName: node
  linkType: hard

"get-uri@npm:^6.0.1":
  version: 6.0.4
  resolution: "get-uri@npm:6.0.4"
  dependencies:
    basic-ftp: ^5.0.2
    data-uri-to-buffer: ^6.0.2
    debug: ^4.3.4
  checksum: 7eae81655e0c8cee250d29c189e09030f37a2d37987298325709affb9408de448bf2dc43ee9a59acd21c1f100c3ca711d0446b4e689e9590c25774ecc59f0442
  languageName: node
  linkType: hard

"getenv@npm:^2.0.0":
  version: 2.0.0
  resolution: "getenv@npm:2.0.0"
  checksum: d5e4cd001952db17d546c8ae5961b68dd83d0a6c6027cc4c613cbe0f88ca835f661364a7eff32428953e7677af95fb0a35f035c98b661bef2fcabb5d7c711c86
  languageName: node
  linkType: hard

"git-raw-commits@npm:^4.0.0":
  version: 4.0.0
  resolution: "git-raw-commits@npm:4.0.0"
  dependencies:
    dargs: ^8.0.0
    meow: ^12.0.1
    split2: ^4.0.0
  bin:
    git-raw-commits: cli.mjs
  checksum: 95546f4afcb33cf00ff638f7fec55ad61d4d927447737900e1f6fcbbdbb341b3f150908424cc62acb6d9faaea6f1e8f55d0697b899f0589af9d2733afb20abfb
  languageName: node
  linkType: hard

"git-raw-commits@npm:^5.0.0":
  version: 5.0.0
  resolution: "git-raw-commits@npm:5.0.0"
  dependencies:
    "@conventional-changelog/git-client": ^1.0.0
    meow: ^13.0.0
  bin:
    git-raw-commits: src/cli.js
  checksum: 8e2767f3a1d751b9aef0f8e84259c87114f1691a0e90ee915ebff5b2f5f8e72d7ea573ff2930be4286c9e067e85713ae67c0645c02e647c5a9c0f5b00bfd6284
  languageName: node
  linkType: hard

"git-semver-tags@npm:^8.0.0":
  version: 8.0.0
  resolution: "git-semver-tags@npm:8.0.0"
  dependencies:
    "@conventional-changelog/git-client": ^1.0.0
    meow: ^13.0.0
  bin:
    git-semver-tags: src/cli.js
  checksum: 49ac7dc10d0a025eaac8bbdcfe9b0e9e596701a1b4ee78b16769995bc9f4bb8230741c37471b6534b804896c01a354effe2d252d727544c4dc5c5f314b559305
  languageName: node
  linkType: hard

"git-up@npm:^7.0.0":
  version: 7.0.0
  resolution: "git-up@npm:7.0.0"
  dependencies:
    is-ssh: ^1.4.0
    parse-url: ^8.1.0
  checksum: 2faadbab51e94d2ffb220e426e950087cc02c15d664e673bd5d1f734cfa8196fed8b19493f7bf28fe216d087d10e22a7fd9b63687e0ba7d24f0ddcfb0a266d6e
  languageName: node
  linkType: hard

"git-url-parse@npm:14.0.0":
  version: 14.0.0
  resolution: "git-url-parse@npm:14.0.0"
  dependencies:
    git-up: ^7.0.0
  checksum: b011c5de652e60e5f19de9815d1b78b2f725deb07e73d1b9ff8ca6657406d0a6c691fbe4460017822676a80635f93099345cadbd06361b76f53c4556265d3e48
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.4.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"glob@npm:^7.0.0, glob@npm:^7.1.1, glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"glob@npm:^8.0.3":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^5.0.1
    once: ^1.3.0
  checksum: 92fbea3221a7d12075f26f0227abac435de868dd0736a17170663783296d0dd8d3d532a5672b4488a439bf5d7fb85cdd07c11185d6cd39184f0385cbdfb86a47
  languageName: node
  linkType: hard

"global-directory@npm:^4.0.1":
  version: 4.0.1
  resolution: "global-directory@npm:4.0.1"
  dependencies:
    ini: 4.1.1
  checksum: 5b4df24438a4e5f21e43fbdd9e54f5e12bb48dce01a0a83b415d8052ce91be2d3a97e0c8f98a535e69649b2190036155e9f0f7d3c62f9318f31bdc3fd4f235f5
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 534b8216736a5425737f59f6e6a5c7f386254560c9f41d24a9227d60ee3ad4a9e82c5b85def0e212e9d92162f83a92544be4c7fd4c902cb913736c10e08237ac
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: ^1.2.1
    gopd: ^1.0.1
  checksum: 39ad667ad9f01476474633a1834a70842041f70a55571e8dcef5fb957980a92da5022db5430fca8aecc5d47704ae30618c0bc877a579c70710c904e9ef06108a
  languageName: node
  linkType: hard

"globby@npm:14.0.2":
  version: 14.0.2
  resolution: "globby@npm:14.0.2"
  dependencies:
    "@sindresorhus/merge-streams": ^2.1.0
    fast-glob: ^3.3.2
    ignore: ^5.2.4
    path-type: ^5.0.0
    slash: ^5.1.0
    unicorn-magic: ^0.1.0
  checksum: 2cee79efefca4383a825fc2fcbdb37e5706728f2d39d4b63851927c128fff62e6334ef7d4d467949d411409ad62767dc2d214e0f837a0f6d4b7290b6711d485c
  languageName: node
  linkType: hard

"globby@npm:^11.0.1, globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b4be8885e0cfa018fc783792942d53926c35c50b3aefd3fdcfb9d22c627639dc26bd2327a40a0b74b074100ce95bb7187bfeae2f236856aa3de183af7a02aea6
  languageName: node
  linkType: hard

"globby@npm:^13.1.2":
  version: 13.2.2
  resolution: "globby@npm:13.2.2"
  dependencies:
    dir-glob: ^3.0.1
    fast-glob: ^3.3.0
    ignore: ^5.2.4
    merge2: ^1.4.1
    slash: ^4.0.0
  checksum: f3d84ced58a901b4fcc29c846983108c426631fe47e94872868b65565495f7bee7b3defd68923bd480582771fd4bbe819217803a164a618ad76f1d22f666f41e
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"graceful-fs@npm:4.2.10":
  version: 4.2.10
  resolution: "graceful-fs@npm:4.2.10"
  checksum: 3f109d70ae123951905d85032ebeae3c2a5a7a997430df00ea30df0e3a6c60cf6689b109654d6fdacd28810a053348c4d14642da1d075049e6be1ba5216218da
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.10, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"handlebars@npm:^4.7.7":
  version: 4.7.8
  resolution: "handlebars@npm:4.7.8"
  dependencies:
    minimist: ^1.2.5
    neo-async: ^2.6.2
    source-map: ^0.6.1
    uglify-js: ^3.1.4
    wordwrap: ^1.0.0
  dependenciesMeta:
    uglify-js:
      optional: true
  bin:
    handlebars: bin/handlebars
  checksum: 00e68bb5c183fd7b8b63322e6234b5ac8fbb960d712cb3f25587d559c2951d9642df83c04a1172c918c41bcfc81bfbd7a7718bbce93b893e0135fc99edea93ff
  languageName: node
  linkType: hard

"hard-rejection@npm:^2.1.0":
  version: 2.1.0
  resolution: "hard-rejection@npm:2.1.0"
  checksum: 7baaf80a0c7fff4ca79687b4060113f1529589852152fa935e6787a2bc96211e784ad4588fb3048136ff8ffc9dfcf3ae385314a5b24db32de20bea0d1597f9dc
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 79730518ae02c77e4af6a1d1a0b6a2c3e1509785532771f9baf0241e83e36329542c3d7a0e723df8cbc85f74eff4f177828a2265a01ba576adbdc2d40d86538b
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: ^1.0.0
  checksum: f55010cb94caa56308041d77967c72a02ffd71386b23f9afa8447e58bc92d49d15c19bf75173713468e92fe3fb1680b03b115da39c21c32c74886d1d50d3e7ff
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"hermes-estree@npm:0.25.1":
  version: 0.25.1
  resolution: "hermes-estree@npm:0.25.1"
  checksum: 97f42e9178dff61db017810b4f79f5a2cdbb3cde94b7d99ba84ed632ee2adfcae2244555587951b3151fc036676c68f48f57fbe2b49e253eb1f3f904d284a8b0
  languageName: node
  linkType: hard

"hermes-estree@npm:0.28.1":
  version: 0.28.1
  resolution: "hermes-estree@npm:0.28.1"
  checksum: 4f7b4e0491352012a6cb799315a0aae16abdcc894335e901552ee6c64732d0cf06f0913c579036f9f452b7c4ad9bb0b6ab14e510c13bd7e5997385f77633ab00
  languageName: node
  linkType: hard

"hermes-parser@npm:0.25.1":
  version: 0.25.1
  resolution: "hermes-parser@npm:0.25.1"
  dependencies:
    hermes-estree: 0.25.1
  checksum: 4edcfaa3030931343b540182b83c432aba4cdcb1925952521ab4cfb7ab90c2c1543dfcb042ccd51d5e81e4bfe2809420e85902c2ff95ef7c6c64644ce17138ea
  languageName: node
  linkType: hard

"hermes-parser@npm:0.28.1":
  version: 0.28.1
  resolution: "hermes-parser@npm:0.28.1"
  dependencies:
    hermes-estree: 0.28.1
  checksum: 0d95280d527e1ad46e8caacd56b24d07e4aec39704de86cf164600f2c4fb00f406dd74a37b2103433ef7ec388a549072da20438e224bd47def21f973c36aab7d
  languageName: node
  linkType: hard

"hosted-git-info@npm:^4.0.1":
  version: 4.1.0
  resolution: "hosted-git-info@npm:4.1.0"
  dependencies:
    lru-cache: ^6.0.0
  checksum: c3f87b3c2f7eb8c2748c8f49c0c2517c9a95f35d26f4bf54b2a8cba05d2e668f3753548b6ea366b18ec8dadb4e12066e19fa382a01496b0ffa0497eb23cbe461
  languageName: node
  linkType: hard

"hosted-git-info@npm:^7.0.0":
  version: 7.0.2
  resolution: "hosted-git-info@npm:7.0.2"
  dependencies:
    lru-cache: ^10.0.1
  checksum: 467cf908a56556417b18e86ae3b8dee03c2360ef1d51e61c4028fe87f6f309b6ff038589c94b5666af207da9d972d5107698906aabeb78aca134641962a5c6f8
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: d2df2da3ad40ca9ee3a39c5cc6475ef67c8f83c234475f24d8e9ce0dc80a2c82df8e1d6fa78ddd1e9022a586ea1bd247a615e80a5cd9273d90111ddda7d9e974
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 7a7246ddfce629f96832791176fd643589d954e6f3b49548dadb4290451961237fab8fcea41cd2008fe819d95b41c1e8b97f47d088afc0a1c81705287b4ddbcc
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0, http-proxy-agent@npm:^7.0.1":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.5, https-proxy-agent@npm:^7.0.6":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"human-signals@npm:^1.1.1":
  version: 1.1.1
  resolution: "human-signals@npm:1.1.1"
  checksum: d587647c9e8ec24e02821b6be7de5a0fc37f591f6c4e319b3054b43fd4c35a70a94c46fc74d8c1a43c47fde157d23acd7421f375e1c1365b09a16835b8300205
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: b87fd89fce72391625271454e70f67fe405277415b48bcc0117ca73d31fa23a4241787afdc8d67f5a116cf37258c052f59ea82daffa72364d61351423848e3b8
  languageName: node
  linkType: hard

"human-signals@npm:^5.0.0":
  version: 5.0.0
  resolution: "human-signals@npm:5.0.0"
  checksum: 6504560d5ed91444f16bea3bd9dfc66110a339442084e56c3e7fa7bbdf3f406426d6563d662bdce67064b165eac31eeabfc0857ed170aaa612cf14ec9f9a464c
  languageName: node
  linkType: hard

"hyphenate-style-name@npm:^1.0.3":
  version: 1.1.0
  resolution: "hyphenate-style-name@npm:1.1.0"
  checksum: b9ed74e29181d96bd58a2d0e62fc4a19879db591dba268275829ff0ae595fcdf11faafaeaa63330a45c3004664d7db1f0fc7cdb372af8ee4615ed8260302c207
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^5.0.5, ignore@npm:^5.2.0, ignore@npm:^5.2.4, ignore@npm:^5.3.1":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 2acfd32a573260ea522ea0bfeff880af426d68f6831f973129e2ba7363f422923cf53aab62f8369cbf4667c7b25b6f8a3761b34ecdb284ea18e87a5262a865be
  languageName: node
  linkType: hard

"image-size@npm:^1.0.2":
  version: 1.2.1
  resolution: "image-size@npm:1.2.1"
  dependencies:
    queue: 6.0.2
  bin:
    image-size: bin/image-size.js
  checksum: 8601ddd4edc1db16f097f5cf585c23214e29c3b8f4d8a8f8d59b8e3bae2338c8a5073236bfff421d8541091a98a38b802ed049203c745286a69d1aac4e5bc4c7
  languageName: node
  linkType: hard

"import-fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-fresh@npm:2.0.0"
  dependencies:
    caller-path: ^2.0.0
    resolve-from: ^3.0.0
  checksum: 610255f9753cc6775df00be08e9f43691aa39f7703e3636c45afe22346b8b545e600ccfe100c554607546fc8e861fa149a0d1da078c8adedeea30fff326eef79
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.2.0
  resolution: "import-local@npm:3.2.0"
  dependencies:
    pkg-dir: ^4.2.0
    resolve-cwd: ^3.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 0b0b0b412b2521739fbb85eeed834a3c34de9bc67e670b3d0b86248fc460d990a7b116ad056c084b87a693ef73d1f17268d6a5be626bb43c998a8b1c8a230004
  languageName: node
  linkType: hard

"import-meta-resolve@npm:^4.0.0":
  version: 4.1.0
  resolution: "import-meta-resolve@npm:4.1.0"
  checksum: 6497af27bf3ee384ad4efd4e0ec3facf9a114863f35a7b35f248659f32faa5e1ae07baa74d603069f35734ae3718a78b3f66926f98dc9a62e261e7df37854a62
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"indent-string@npm:^5.0.0":
  version: 5.0.0
  resolution: "indent-string@npm:5.0.0"
  checksum: e466c27b6373440e6d84fbc19e750219ce25865cb82d578e41a6053d727e5520dc5725217d6eb1cc76005a1bb1696a0f106d84ce7ebda3033b963a38583fb3b3
  languageName: node
  linkType: hard

"index-to-position@npm:^1.1.0":
  version: 1.1.0
  resolution: "index-to-position@npm:1.1.0"
  checksum: 078b05777ba4ccc2af13328cbdef8ac945c885aed7c28bf55b17b7e7722507dfb3afbdeb30b59ff224374857147d16043da1bcb2a4dc533c7924d81873ef4363
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"ini@npm:4.1.1":
  version: 4.1.1
  resolution: "ini@npm:4.1.1"
  checksum: 0e5909554074fbc31824fa5415b0f604de4a665514c96a897a77bf77353a7ad4743927321270e9d0610a9d510ccd1f3cd77422f7cc80d8f4542dbce75476fb6d
  languageName: node
  linkType: hard

"ini@npm:^1.3.4, ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"inline-style-prefixer@npm:^7.0.1":
  version: 7.0.1
  resolution: "inline-style-prefixer@npm:7.0.1"
  dependencies:
    css-in-js-utils: ^3.1.0
  checksum: 07a72573dfdac5e08fa18f5ce71d922861716955e230175ac415db227d9ed49443c764356cb407a92f4c85b30ebf39604165260b4dfbf3196b7736d7332c5c06
  languageName: node
  linkType: hard

"inquirer@npm:9.3.2":
  version: 9.3.2
  resolution: "inquirer@npm:9.3.2"
  dependencies:
    "@inquirer/figures": ^1.0.3
    ansi-escapes: ^4.3.2
    cli-width: ^4.1.0
    external-editor: ^3.1.0
    mute-stream: 1.0.0
    ora: ^5.4.1
    run-async: ^3.0.0
    rxjs: ^7.8.1
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wrap-ansi: ^6.2.0
    yoctocolors-cjs: ^2.1.1
  checksum: 8a606d400bfc8ce5a3fd70ce38a158327d7f65274cadce25acdfdf93e90aedfaa7b705b7929a10510b928c76c70bb39ca4e566e23620d45ce5c91b2334190f95
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    hasown: ^2.0.2
    side-channel: ^1.1.0
  checksum: 8e0991c2d048cc08dab0a91f573c99f6a4215075887517ea4fa32203ce8aea60fa03f95b177977fa27eb502e5168366d0f3e02c762b799691411d49900611861
  languageName: node
  linkType: hard

"interpret@npm:^1.0.0":
  version: 1.4.0
  resolution: "interpret@npm:1.4.0"
  checksum: 2e5f51268b5941e4a17e4ef0575bc91ed0ab5f8515e3cf77486f7c14d13f3010df9c0959f37063dcc96e78d12dc6b0bb1b9e111cdfe69771f4656d2993d36155
  languageName: node
  linkType: hard

"invariant@npm:^2.2.4":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: ^1.0.0
  checksum: cc3182d793aad82a8d1f0af697b462939cb46066ec48bbf1707c150ad5fad6406137e91a262022c269702e01621f35ef60269f6c0d7fd178487959809acdfb14
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"is-absolute@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-absolute@npm:1.0.0"
  dependencies:
    is-relative: ^1.0.0
    is-windows: ^1.0.1
  checksum: 9d16b2605eda3f3ce755410f1d423e327ad3a898bcb86c9354cf63970ed3f91ba85e9828aa56f5d6a952b9fae43d0477770f78d37409ae8ecc31e59ebc279b27
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: f137a2a6e77af682cdbffef1e633c140cf596f72321baf8bba0f4ef22685eb4339dde23dfe9e9ca430b5f961dee4d46577dcf12b792b68518c8449b134fb9156
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: ^1.0.0
    call-bound: ^1.0.3
    get-proto: ^1.0.1
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: 9bece45133da26636488ca127d7686b85ad3ca18927e2850cff1937a650059e90be1c71a48623f8791646bb7a241b0cabf602a0b9252dcfa5ab273f2399000e6
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: ^1.0.2
  checksum: ee1544f0e664f253306786ed1dce494b8cf242ef415d6375d8545b4d8816b0f054bd9f948a8988ae2c6325d1c28260dd02978236b2f7b8fb70dfc4838a6c9fa7
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 0415b181e8f1bfd5d3f8a20f8108e64d372a72131674eea9c2923f39d065b6ad08d654765553bdbffbd92c3746f1007986c34087db1bd89a31f71be8359ccdaa
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.16.0, is-core-module@npm:^2.5.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: ^2.0.2
  checksum: 6ec5b3c42d9cbf1ac23f164b16b8a140c3cec338bf8f884c076ca89950c7cc04c33e78f02b8cae7ff4751f3247e3174b2330f1fe4de194c7210deb8b1ea316a7
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    is-typed-array: ^1.1.13
  checksum: 31600dd19932eae7fd304567e465709ffbfa17fa236427c9c864148e1b54eb2146357fcf3aed9b686dee13c217e1bb5a649cb3b9c479e1004c0648e9febde1b2
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    has-tostringtag: ^1.0.2
  checksum: d6c36ab9d20971d65f3fc64cef940d57a4900a2ac85fb488a46d164c2072a33da1cb51eefcc039e3e5c208acbce343d3480b84ab5ff0983f617512da2742562a
  languageName: node
  linkType: hard

"is-directory@npm:^0.3.1":
  version: 0.3.1
  resolution: "is-directory@npm:0.3.1"
  checksum: dce9a9d3981e38f2ded2a80848734824c50ee8680cd09aa477bef617949715cfc987197a2ca0176c58a9fb192a1a0d69b535c397140d241996a609d5906ae524
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-docker@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-docker@npm:3.0.0"
  bin:
    is-docker: cli.js
  checksum: b698118f04feb7eaf3338922bd79cba064ea54a1c3db6ec8c0c8d8ee7613e7e5854d802d3ef646812a8a3ace81182a085dfa0a71cc68b06f3fa794b9783b3c90
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 38c646c506e64ead41a36c182d91639833311970b6b6c6268634f109eef0a1a9d2f1f2e499ef4cb43c744a13443c4cdd2f0812d5afdcee5e9b65b72b28c48557
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: a6ad5492cf9d1746f73b6744e0c43c0020510b59d56ddcb78a91cbc173f09b5e6beff53d75c9c5a29feb618bfef2bf458e025ecf3a57ad2268e2fb2569f56215
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    get-proto: ^1.0.0
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: f7f7276131bdf7e28169b86ac55a5b080012a597f9d85a0cbef6fe202a7133fa450a3b453e394870e3cb3685c5a764c64a9f12f614684b46969b1e6f297bed6b
  languageName: node
  linkType: hard

"is-git-dirty@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-git-dirty@npm:2.0.2"
  dependencies:
    execa: ^4.0.3
    is-git-repository: ^2.0.0
  checksum: 13c8f58600e1ea0874703c1fa0ca87825119cf05347bb3b0bbbd331eec42b6a0e89519be4dcb173ac8eda84d1ade97fe187df8af10df599f1df8d0267680abdd
  languageName: node
  linkType: hard

"is-git-repository@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-git-repository@npm:2.0.0"
  dependencies:
    execa: ^4.0.3
    is-absolute: ^1.0.0
  checksum: 9eba76437998b3239adc6e87ceb9b81f8ef00d6209f8700f2ba523e61359d5b068d11f8f94474bc90f92b39fd3c8261c4d60feb3cd62d18e1838480b0b135b88
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-in-ci@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-in-ci@npm:1.0.0"
  bin:
    is-in-ci: cli.js
  checksum: a2e82d04aa729008e31e4b3dda56266f02ffa44109525a9cb2f521f44a2538d2f86227a32ca4f855b0ebd24f976561c368105cacb477ca34b16acb0b766e9103
  languageName: node
  linkType: hard

"is-inside-container@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-inside-container@npm:1.0.0"
  dependencies:
    is-docker: ^3.0.0
  bin:
    is-inside-container: cli.js
  checksum: c50b75a2ab66ab3e8b92b3bc534e1ea72ca25766832c0623ac22d134116a98bcf012197d1caabe1d1c4bd5f84363d4aa5c36bb4b585fbcaf57be172cd10a1a03
  languageName: node
  linkType: hard

"is-installed-globally@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-installed-globally@npm:1.0.0"
  dependencies:
    global-directory: ^4.0.1
    is-path-inside: ^4.0.0
  checksum: 713bd28acc24b4b0744d8d73001a36a4c8225fad6cb51e5236e615f06393ad665b5e5eacab962d1b1101d7a8f89216ccb13d4be39b46758ad7b646c4f54a248d
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 824808776e2d468b2916cdd6c16acacebce060d844c35ca6d82267da692e92c3a16fdba624c50b54a63f38bdc4016055b6f443ce57d7147240de4f8cdabaf6f9
  languageName: node
  linkType: hard

"is-interactive@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-interactive@npm:2.0.0"
  checksum: e8d52ad490bed7ae665032c7675ec07732bbfe25808b0efbc4d5a76b1a1f01c165f332775c63e25e9a03d319ebb6b24f571a9e902669fc1e40b0a60b5be6e26c
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: e6ce5f6380f32b141b3153e6ba9074892bbbbd655e92e7ba5ff195239777e767a976dcd4e22f864accaf30e53ebf961ab1995424aef91af68788f0591b7396cc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: c1e6b23d2070c0539d7b36022d5a94407132411d01aba39ec549af824231f3804b1aea90b5e4e58e807a65d23ceb538ed6e355ce76b267bdd86edb757ffcbdcd
  languageName: node
  linkType: hard

"is-npm@npm:^6.0.0":
  version: 6.0.0
  resolution: "is-npm@npm:6.0.0"
  checksum: fafe1ddc772345f5460514891bb8014376904ccdbddd59eee7525c9adcc08d426933f28b087bef3e17524da7ebf35c03ef484ff3b6ba9d5fecd8c6e6a7d4bf11
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 6517f0a0e8c4b197a21afb45cd3053dc711e79d45d8878aa3565de38d0102b130ca8732485122c7b336e98c27dacd5236854e3e6526e0eb30cae64956535662f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-obj@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-obj@npm:2.0.0"
  checksum: c9916ac8f4621962a42f5e80e7ffdb1d79a3fab7456ceaeea394cd9e0858d04f985a9ace45be44433bf605673c8be8810540fe4cc7f4266fc7526ced95af5a08
  languageName: node
  linkType: hard

"is-path-cwd@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-path-cwd@npm:2.2.0"
  checksum: 46a840921bb8cc0dc7b5b423a14220e7db338072a4495743a8230533ce78812dc152548c86f4b828411fe98c5451959f07cf841c6a19f611e46600bd699e8048
  languageName: node
  linkType: hard

"is-path-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-path-cwd@npm:3.0.0"
  checksum: bc34d13b6a03dfca4a3ab6a8a5ba78ae4b24f4f1db4b2b031d2760c60d0913bd16a4b980dcb4e590adfc906649d5f5132684079a3972bd219da49deebb9adea8
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.2":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-path-inside@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-path-inside@npm:4.0.0"
  checksum: 8810fa11c58e6360b82c3e0d6cd7d9c7d0392d3ac9eb10f980b81f9839f40ac6d1d6d6f05d069db0d227759801228f0b072e1b6c343e4469b065ab5fe0b68fe5
  languageName: node
  linkType: hard

"is-plain-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-plain-obj@npm:1.1.0"
  checksum: 0ee04807797aad50859652a7467481816cbb57e5cc97d813a7dcd8915da8195dc68c436010bf39d195226cde6a2d352f4b815f16f26b7bf486a5754290629931
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: 2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 99ee0b6d30ef1bb61fa4b22fae7056c6c9b3c693803c0c284ff7a8570f83075a7d38cda53b06b7996d441215c27895ea5d1af62124562e13d91b3dbec41a5e13
  languageName: node
  linkType: hard

"is-relative@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-relative@npm:1.0.0"
  dependencies:
    is-unc-path: ^1.0.0
  checksum: 3271a0df109302ef5e14a29dcd5d23d9788e15ade91a40b942b035827ffbb59f7ce9ff82d036ea798541a52913cbf9d2d0b66456340887b51f3542d57b5a4c05
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 36e3f8c44bdbe9496c9689762cc4110f6a6a12b767c5d74c0398176aa2678d4467e3bf07595556f2dba897751bde1422480212b97d973c7b08a343100b0c0dfe
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1611fedc175796eebb88f4dfc393dd969a4a8e6c69cadaff424ee9d4464f9f026399a5f84a90f7c62d6d7ee04e3626a912149726de102b0bd6c1ee6a9868fa5a
  languageName: node
  linkType: hard

"is-ssh@npm:^1.4.0":
  version: 1.4.1
  resolution: "is-ssh@npm:1.4.1"
  dependencies:
    protocols: ^2.0.1
  checksum: 005b461ac444398eb8b7cd2f489288e49dd18c8b6cbf1eb20767f9b79f330ab6e3308b2dac8ec6ca2a950d2a368912e0e992e2474bc1b5204693abb6226c1431
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-stream@npm:3.0.0"
  checksum: 172093fe99119ffd07611ab6d1bcccfe8bc4aa80d864b15f43e63e54b7abc71e779acd69afdb854c4e2a67fdc16ae710e370eda40088d1cfc956a50ed82d8f16
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 2eeaaff605250f5e836ea3500d33d1a5d3aa98d008641d9d42fb941e929ffd25972326c2ef912987e54c95b6f10416281aaf1b35cdf81992cfb7524c5de8e193
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.2
    has-symbols: ^1.1.0
    safe-regex-test: ^1.1.0
  checksum: bfafacf037af6f3c9d68820b74be4ae8a736a658a3344072df9642a090016e281797ba8edbeb1c83425879aae55d1cb1f30b38bf132d703692b2570367358032
  languageName: node
  linkType: hard

"is-text-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-text-path@npm:2.0.0"
  dependencies:
    text-extensions: ^2.0.0
  checksum: 3a8725fc7c0d4c7741a97993bc2fecc09a0963660394d3ee76145274366c98ad57c6791d20d4ef829835f573b1137265051c05ecd65fbe72f69bb9ab9e3babbd
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: ^1.1.16
  checksum: ea7cfc46c282f805d19a9ab2084fd4542fed99219ee9dbfbc26284728bd713a51eac66daa74eca00ae0a43b61322920ba334793607dc39907465913e921e0892
  languageName: node
  linkType: hard

"is-unc-path@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-unc-path@npm:1.0.0"
  dependencies:
    unc-path-regex: ^0.1.2
  checksum: e8abfde203f7409f5b03a5f1f8636e3a41e78b983702ef49d9343eb608cdfe691429398e8815157519b987b739bcfbc73ae7cf4c8582b0ab66add5171088eab6
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^1.3.0":
  version: 1.3.0
  resolution: "is-unicode-supported@npm:1.3.0"
  checksum: 20a1fc161afafaf49243551a5ac33b6c4cf0bbcce369fcd8f2951fbdd000c30698ce320de3ee6830497310a8f41880f8066d440aa3eb0a853e2aa4836dd89abc
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-unicode-supported@npm:2.1.0"
  checksum: f254e3da6b0ab1a57a94f7273a7798dd35d1d45b227759f600d0fa9d5649f9c07fa8d3c8a6360b0e376adf916d151ec24fc9a50c5295c58bae7ca54a76a063f9
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: f36aef758b46990e0d3c37269619c0a08c5b29428c0bb11ecba7f75203442d6c7801239c2f31314bc79199217ef08263787f3837d9e22610ad1da62970d6616d
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1769b9aed5d435a3a989ffc18fc4ad1947d2acdaf530eb2bd6af844861b545047ea51102f75901f89043bed0267ed61d914ee21e6e8b9aa734ec201cdfc0726f
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: 5c6c8415a06065d78bdd5e3a771483aa1cd928df19138aa73c4c51333226f203f22117b4325df55cc8b3085a6716870a320c2d757efee92d7a7091a039082041
  languageName: node
  linkType: hard

"is-windows@npm:^1.0.1":
  version: 1.0.2
  resolution: "is-windows@npm:1.0.2"
  checksum: 438b7e52656fe3b9b293b180defb4e448088e7023a523ec21a91a80b9ff8cdb3377ddb5b6e60f7c7de4fa8b63ab56e121b6705fe081b3cf1b828b0a380009ad7
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1, is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: ^2.0.0
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"is-wsl@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-wsl@npm:3.1.0"
  dependencies:
    is-inside-container: ^1.0.0
  checksum: f9734c81f2f9cf9877c5db8356bfe1ff61680f1f4c1011e91278a9c0564b395ae796addb4bf33956871041476ec82c3e5260ed57b22ac91794d4ae70a1d2f0a9
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"issue-parser@npm:7.0.1":
  version: 7.0.1
  resolution: "issue-parser@npm:7.0.1"
  dependencies:
    lodash.capitalize: ^4.2.1
    lodash.escaperegexp: ^4.1.2
    lodash.isplainobject: ^4.0.6
    lodash.isstring: ^4.0.1
    lodash.uniqby: ^4.7.0
  checksum: baf2831baa84c214a8c9f095889476f2ad7a6511fef7d096941ecf4666a822fbce298baac38510c4be782fc562488d4909535e81fb7a28c55779fcc88e3ec595
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 2367407a8d13982d8f7a859a35e7f8dd5d8f75aae4bb5484ede3a9ea1b426dc245aff28b976a2af48ee759fdd9be374ce2bd2669b644f31e76c5f46a2e29a831
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": ^7.12.3
    "@babel/parser": ^7.14.7
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-coverage: ^3.2.0
    semver: ^6.3.0
  checksum: bf16f1803ba5e51b28bbd49ed955a736488381e09375d830e42ddeb403855b2006f850711d95ad726f2ba3f1ae8e7366de7e51d2b9ac67dc4d80191ef7ddf272
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^6.0.0":
  version: 6.0.3
  resolution: "istanbul-lib-instrument@npm:6.0.3"
  dependencies:
    "@babel/core": ^7.23.9
    "@babel/parser": ^7.23.9
    "@istanbuljs/schema": ^0.1.3
    istanbul-lib-coverage: ^3.2.0
    semver: ^7.5.4
  checksum: 74104c60c65c4fa0e97cc76f039226c356123893929f067bfad5f86fe839e08f5d680354a68fead3bc9c1e2f3fa6f3f53cded70778e821d911e851d349f3545a
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: ^3.0.0
    make-dir: ^4.0.0
    supports-color: ^7.1.0
  checksum: fd17a1b879e7faf9bb1dc8f80b2a16e9f5b7b8498fe6ed580a618c34df0bfe53d2abd35bf8a0a00e628fb7405462576427c7df20bbe4148d19c14b431c974b21
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.0":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: ^4.1.1
    istanbul-lib-coverage: ^3.0.0
    source-map: ^0.6.1
  checksum: 21ad3df45db4b81852b662b8d4161f6446cd250c1ddc70ef96a585e2e85c26ed7cd9c2a396a71533cfb981d1a645508bc9618cae431e55d01a0628e7dec62ef2
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: ^2.0.0
    istanbul-lib-report: ^3.0.0
  checksum: 2072db6e07bfbb4d0eb30e2700250636182398c1af811aea5032acb219d2080f7586923c09fa194029efd6b92361afb3dcbe1ebcc3ee6651d13340f7c6c4ed95
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.4":
  version: 1.1.5
  resolution: "iterator.prototype@npm:1.1.5"
  dependencies:
    define-data-property: ^1.1.4
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.6
    get-proto: ^1.0.0
    has-symbols: ^1.1.0
    set-function-name: ^2.0.2
  checksum: 7db23c42629ba4790e6e15f78b555f41dbd08818c85af306988364bd19d86716a1187cb333444f3a0036bfc078a0e9cb7ec67fef3a61662736d16410d7f77869
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"jest-changed-files@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-changed-files@npm:29.7.0"
  dependencies:
    execa: ^5.0.0
    jest-util: ^29.7.0
    p-limit: ^3.1.0
  checksum: 963e203893c396c5dfc75e00a49426688efea7361b0f0e040035809cecd2d46b3c01c02be2d9e8d38b1138357d2de7719ea5b5be21f66c10f2e9685a5a73bb99
  languageName: node
  linkType: hard

"jest-circus@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-circus@npm:29.7.0"
  dependencies:
    "@jest/environment": ^29.7.0
    "@jest/expect": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    co: ^4.6.0
    dedent: ^1.0.0
    is-generator-fn: ^2.0.0
    jest-each: ^29.7.0
    jest-matcher-utils: ^29.7.0
    jest-message-util: ^29.7.0
    jest-runtime: ^29.7.0
    jest-snapshot: ^29.7.0
    jest-util: ^29.7.0
    p-limit: ^3.1.0
    pretty-format: ^29.7.0
    pure-rand: ^6.0.0
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: 349437148924a5a109c9b8aad6d393a9591b4dac1918fc97d81b7fc515bc905af9918495055071404af1fab4e48e4b04ac3593477b1d5dcf48c4e71b527c70a7
  languageName: node
  linkType: hard

"jest-cli@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-cli@npm:29.7.0"
  dependencies:
    "@jest/core": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/types": ^29.6.3
    chalk: ^4.0.0
    create-jest: ^29.7.0
    exit: ^0.1.2
    import-local: ^3.0.2
    jest-config: ^29.7.0
    jest-util: ^29.7.0
    jest-validate: ^29.7.0
    yargs: ^17.3.1
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 664901277a3f5007ea4870632ed6e7889db9da35b2434e7cb488443e6bf5513889b344b7fddf15112135495b9875892b156faeb2d7391ddb9e2a849dcb7b6c36
  languageName: node
  linkType: hard

"jest-config@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-config@npm:29.7.0"
  dependencies:
    "@babel/core": ^7.11.6
    "@jest/test-sequencer": ^29.7.0
    "@jest/types": ^29.6.3
    babel-jest: ^29.7.0
    chalk: ^4.0.0
    ci-info: ^3.2.0
    deepmerge: ^4.2.2
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    jest-circus: ^29.7.0
    jest-environment-node: ^29.7.0
    jest-get-type: ^29.6.3
    jest-regex-util: ^29.6.3
    jest-resolve: ^29.7.0
    jest-runner: ^29.7.0
    jest-util: ^29.7.0
    jest-validate: ^29.7.0
    micromatch: ^4.0.4
    parse-json: ^5.2.0
    pretty-format: ^29.7.0
    slash: ^3.0.0
    strip-json-comments: ^3.1.1
  peerDependencies:
    "@types/node": "*"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    "@types/node":
      optional: true
    ts-node:
      optional: true
  checksum: 4cabf8f894c180cac80b7df1038912a3fc88f96f2622de33832f4b3314f83e22b08fb751da570c0ab2b7988f21604bdabade95e3c0c041068ac578c085cf7dff
  languageName: node
  linkType: hard

"jest-diff@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-diff@npm:29.7.0"
  dependencies:
    chalk: ^4.0.0
    diff-sequences: ^29.6.3
    jest-get-type: ^29.6.3
    pretty-format: ^29.7.0
  checksum: 08e24a9dd43bfba1ef07a6374e5af138f53137b79ec3d5cc71a2303515335898888fa5409959172e1e05de966c9e714368d15e8994b0af7441f0721ee8e1bb77
  languageName: node
  linkType: hard

"jest-docblock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-docblock@npm:29.7.0"
  dependencies:
    detect-newline: ^3.0.0
  checksum: 66390c3e9451f8d96c5da62f577a1dad701180cfa9b071c5025acab2f94d7a3efc2515cfa1654ebe707213241541ce9c5530232cdc8017c91ed64eea1bd3b192
  languageName: node
  linkType: hard

"jest-each@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-each@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    chalk: ^4.0.0
    jest-get-type: ^29.6.3
    jest-util: ^29.7.0
    pretty-format: ^29.7.0
  checksum: e88f99f0184000fc8813f2a0aa79e29deeb63700a3b9b7928b8a418d7d93cd24933608591dbbdea732b473eb2021c72991b5cc51a17966842841c6e28e6f691c
  languageName: node
  linkType: hard

"jest-environment-node@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-environment-node@npm:29.7.0"
  dependencies:
    "@jest/environment": ^29.7.0
    "@jest/fake-timers": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    jest-mock: ^29.7.0
    jest-util: ^29.7.0
  checksum: 501a9966292cbe0ca3f40057a37587cb6def25e1e0c5e39ac6c650fe78d3c70a2428304341d084ac0cced5041483acef41c477abac47e9a290d5545fd2f15646
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-get-type@npm:29.6.3"
  checksum: 88ac9102d4679d768accae29f1e75f592b760b44277df288ad76ce5bf038c3f5ce3719dea8aa0f035dac30e9eb034b848ce716b9183ad7cc222d029f03e92205
  languageName: node
  linkType: hard

"jest-haste-map@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-haste-map@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@types/graceful-fs": ^4.1.3
    "@types/node": "*"
    anymatch: ^3.0.3
    fb-watchman: ^2.0.0
    fsevents: ^2.3.2
    graceful-fs: ^4.2.9
    jest-regex-util: ^29.6.3
    jest-util: ^29.7.0
    jest-worker: ^29.7.0
    micromatch: ^4.0.4
    walker: ^1.0.8
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: c2c8f2d3e792a963940fbdfa563ce14ef9e14d4d86da645b96d3cd346b8d35c5ce0b992ee08593939b5f718cf0a1f5a90011a056548a1dbf58397d4356786f01
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-leak-detector@npm:29.7.0"
  dependencies:
    jest-get-type: ^29.6.3
    pretty-format: ^29.7.0
  checksum: e3950e3ddd71e1d0c22924c51a300a1c2db6cf69ec1e51f95ccf424bcc070f78664813bef7aed4b16b96dfbdeea53fe358f8aeaaea84346ae15c3735758f1605
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-matcher-utils@npm:29.7.0"
  dependencies:
    chalk: ^4.0.0
    jest-diff: ^29.7.0
    jest-get-type: ^29.6.3
    pretty-format: ^29.7.0
  checksum: d7259e5f995d915e8a37a8fd494cb7d6af24cd2a287b200f831717ba0d015190375f9f5dc35393b8ba2aae9b2ebd60984635269c7f8cff7d85b077543b7744cd
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-message-util@npm:29.7.0"
  dependencies:
    "@babel/code-frame": ^7.12.13
    "@jest/types": ^29.6.3
    "@types/stack-utils": ^2.0.0
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    micromatch: ^4.0.4
    pretty-format: ^29.7.0
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: a9d025b1c6726a2ff17d54cc694de088b0489456c69106be6b615db7a51b7beb66788bea7a59991a019d924fbf20f67d085a445aedb9a4d6760363f4d7d09930
  languageName: node
  linkType: hard

"jest-mock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-mock@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@types/node": "*"
    jest-util: ^29.7.0
  checksum: 81ba9b68689a60be1482212878973700347cb72833c5e5af09895882b9eb5c4e02843a1bbdf23f94c52d42708bab53a30c45a3482952c9eec173d1eaac5b86c5
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.2":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: db1a8ab2cb97ca19c01b1cfa9a9c8c69a143fde833c14df1fab0766f411b1148ff0df878adea09007ac6a2085ec116ba9a996a6ad104b1e58c20adbf88eed9b2
  languageName: node
  linkType: hard

"jest-regex-util@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-regex-util@npm:29.6.3"
  checksum: 0518beeb9bf1228261695e54f0feaad3606df26a19764bc19541e0fc6e2a3737191904607fb72f3f2ce85d9c16b28df79b7b1ec9443aa08c3ef0e9efda6f8f2a
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve-dependencies@npm:29.7.0"
  dependencies:
    jest-regex-util: ^29.6.3
    jest-snapshot: ^29.7.0
  checksum: aeb75d8150aaae60ca2bb345a0d198f23496494677cd6aefa26fc005faf354061f073982175daaf32b4b9d86b26ca928586344516e3e6969aa614cb13b883984
  languageName: node
  linkType: hard

"jest-resolve@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve@npm:29.7.0"
  dependencies:
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.7.0
    jest-pnp-resolver: ^1.2.2
    jest-util: ^29.7.0
    jest-validate: ^29.7.0
    resolve: ^1.20.0
    resolve.exports: ^2.0.0
    slash: ^3.0.0
  checksum: 0ca218e10731aa17920526ec39deaec59ab9b966237905ffc4545444481112cd422f01581230eceb7e82d86f44a543d520a71391ec66e1b4ef1a578bd5c73487
  languageName: node
  linkType: hard

"jest-runner@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runner@npm:29.7.0"
  dependencies:
    "@jest/console": ^29.7.0
    "@jest/environment": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    emittery: ^0.13.1
    graceful-fs: ^4.2.9
    jest-docblock: ^29.7.0
    jest-environment-node: ^29.7.0
    jest-haste-map: ^29.7.0
    jest-leak-detector: ^29.7.0
    jest-message-util: ^29.7.0
    jest-resolve: ^29.7.0
    jest-runtime: ^29.7.0
    jest-util: ^29.7.0
    jest-watcher: ^29.7.0
    jest-worker: ^29.7.0
    p-limit: ^3.1.0
    source-map-support: 0.5.13
  checksum: f0405778ea64812bf9b5c50b598850d94ccf95d7ba21f090c64827b41decd680ee19fcbb494007cdd7f5d0d8906bfc9eceddd8fa583e753e736ecd462d4682fb
  languageName: node
  linkType: hard

"jest-runtime@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runtime@npm:29.7.0"
  dependencies:
    "@jest/environment": ^29.7.0
    "@jest/fake-timers": ^29.7.0
    "@jest/globals": ^29.7.0
    "@jest/source-map": ^29.6.3
    "@jest/test-result": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    cjs-module-lexer: ^1.0.0
    collect-v8-coverage: ^1.0.0
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.7.0
    jest-message-util: ^29.7.0
    jest-mock: ^29.7.0
    jest-regex-util: ^29.6.3
    jest-resolve: ^29.7.0
    jest-snapshot: ^29.7.0
    jest-util: ^29.7.0
    slash: ^3.0.0
    strip-bom: ^4.0.0
  checksum: d19f113d013e80691e07047f68e1e3448ef024ff2c6b586ce4f90cd7d4c62a2cd1d460110491019719f3c59bfebe16f0e201ed005ef9f80e2cf798c374eed54e
  languageName: node
  linkType: hard

"jest-snapshot@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-snapshot@npm:29.7.0"
  dependencies:
    "@babel/core": ^7.11.6
    "@babel/generator": ^7.7.2
    "@babel/plugin-syntax-jsx": ^7.7.2
    "@babel/plugin-syntax-typescript": ^7.7.2
    "@babel/types": ^7.3.3
    "@jest/expect-utils": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    babel-preset-current-node-syntax: ^1.0.0
    chalk: ^4.0.0
    expect: ^29.7.0
    graceful-fs: ^4.2.9
    jest-diff: ^29.7.0
    jest-get-type: ^29.6.3
    jest-matcher-utils: ^29.7.0
    jest-message-util: ^29.7.0
    jest-util: ^29.7.0
    natural-compare: ^1.4.0
    pretty-format: ^29.7.0
    semver: ^7.5.3
  checksum: 86821c3ad0b6899521ce75ee1ae7b01b17e6dfeff9166f2cf17f012e0c5d8c798f30f9e4f8f7f5bed01ea7b55a6bc159f5eda778311162cbfa48785447c237ad
  languageName: node
  linkType: hard

"jest-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-util@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    ci-info: ^3.2.0
    graceful-fs: ^4.2.9
    picomatch: ^2.2.3
  checksum: 042ab4980f4ccd4d50226e01e5c7376a8556b472442ca6091a8f102488c0f22e6e8b89ea874111d2328a2080083bf3225c86f3788c52af0bd0345a00eb57a3ca
  languageName: node
  linkType: hard

"jest-validate@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-validate@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    camelcase: ^6.2.0
    chalk: ^4.0.0
    jest-get-type: ^29.6.3
    leven: ^3.1.0
    pretty-format: ^29.7.0
  checksum: 191fcdc980f8a0de4dbdd879fa276435d00eb157a48683af7b3b1b98b0f7d9de7ffe12689b617779097ff1ed77601b9f7126b0871bba4f776e222c40f62e9dae
  languageName: node
  linkType: hard

"jest-watcher@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-watcher@npm:29.7.0"
  dependencies:
    "@jest/test-result": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    emittery: ^0.13.1
    jest-util: ^29.7.0
    string-length: ^4.0.1
  checksum: 67e6e7fe695416deff96b93a14a561a6db69389a0667e9489f24485bb85e5b54e12f3b2ba511ec0b777eca1e727235b073e3ebcdd473d68888650489f88df92f
  languageName: node
  linkType: hard

"jest-worker@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-worker@npm:29.7.0"
  dependencies:
    "@types/node": "*"
    jest-util: ^29.7.0
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 30fff60af49675273644d408b650fc2eb4b5dcafc5a0a455f238322a8f9d8a98d847baca9d51ff197b6747f54c7901daa2287799230b856a0f48287d131f8c13
  languageName: node
  linkType: hard

"jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest@npm:29.7.0"
  dependencies:
    "@jest/core": ^29.7.0
    "@jest/types": ^29.6.3
    import-local: ^3.0.2
    jest-cli: ^29.7.0
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 17ca8d67504a7dbb1998cf3c3077ec9031ba3eb512da8d71cb91bcabb2b8995c4e4b292b740cb9bf1cbff5ce3e110b3f7c777b0cefb6f41ab05445f248d0ee0b
  languageName: node
  linkType: hard

"jimp-compact@npm:0.16.1":
  version: 0.16.1
  resolution: "jimp-compact@npm:0.16.1"
  checksum: 5a1c62d70881b31f79ea65fecfe03617be0eb56139bc451f37e8972365c99ac3b52c5176c446ff27144c98ab664a99107ae08d347044e94e1de637f165b41a57
  languageName: node
  linkType: hard

"jiti@npm:^2.4.1":
  version: 2.4.2
  resolution: "jiti@npm:2.4.2"
  bin:
    jiti: lib/jiti-cli.mjs
  checksum: c6c30c7b6b293e9f26addfb332b63d964a9f143cdd2cf5e946dbe5143db89f7c1b50ad9223b77fb1f6ddb0b9c5ecef995fea024ecf7d2861d285d779cde66e1e
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsc-safe-url@npm:^0.2.2, jsc-safe-url@npm:^0.2.4":
  version: 0.2.4
  resolution: "jsc-safe-url@npm:0.2.4"
  checksum: 53b5741ba2c0a54da1722929dc80becb2c6fcc9525124fb6c2aec1a00f48e79afffd26816c278111e7b938e37ace029e33cbb8cdaa4ac1f528a87e58022284af
  languageName: node
  linkType: hard

"jscodeshift@npm:^17.0.0":
  version: 17.3.0
  resolution: "jscodeshift@npm:17.3.0"
  dependencies:
    "@babel/core": ^7.24.7
    "@babel/parser": ^7.24.7
    "@babel/plugin-transform-class-properties": ^7.24.7
    "@babel/plugin-transform-modules-commonjs": ^7.24.7
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.24.7
    "@babel/plugin-transform-optional-chaining": ^7.24.7
    "@babel/plugin-transform-private-methods": ^7.24.7
    "@babel/preset-flow": ^7.24.7
    "@babel/preset-typescript": ^7.24.7
    "@babel/register": ^7.24.6
    flow-parser: 0.*
    graceful-fs: ^4.2.4
    micromatch: ^4.0.7
    neo-async: ^2.5.0
    picocolors: ^1.0.1
    recast: ^0.23.11
    tmp: ^0.2.3
    write-file-atomic: ^5.0.1
  peerDependencies:
    "@babel/preset-env": ^7.1.6
  peerDependenciesMeta:
    "@babel/preset-env":
      optional: true
  bin:
    jscodeshift: bin/jscodeshift.js
  checksum: 6a529c8dcab8eef48381425c706d58a0a9205397cad367925872845ff1c35924f8f838bbd1397b28a065061032047c9fd843877000a3743240db4ba6ded2546b
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 19c94095ea026725540c0d29da33ab03144f6bcf2d4159e4833d534976e99e0c09c38cefa9a575279a51fc36b31166f8d6d05c9fe2645d5f15851d690b41f17f
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: a36d3ca40574a974d9c2063bf68c2b6141c20da8f2a36bd3279fc802563f35f0527a6c828801295bdfb2803952cf2cf387786c2c90ed564f88d5782475abfe3c
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: ff2b5ba2a70e88fd97a3cb28c1840144c5ce8fae9cbeeddba15afa333a5c407cf0e42300cd0a2885dbb055227fe68d405070faad941beeffbfde9cf3b2c78c5d
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^2.2.1, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"jsonparse@npm:^1.2.0":
  version: 1.3.1
  resolution: "jsonparse@npm:1.3.1"
  checksum: 6514a7be4674ebf407afca0eda3ba284b69b07f9958a8d3113ef1005f7ec610860c312be067e450c569aab8b89635e332cee3696789c750692bb60daba627f4d
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flat: ^1.3.1
    object.assign: ^4.1.4
    object.values: ^1.1.6
  checksum: f4b05fa4d7b5234230c905cfa88d36dc8a58a6666975a3891429b1a8cdc8a140bca76c297225cb7a499fad25a2c052ac93934449a2c31a44fc9edd06c773780a
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2, kind-of@npm:^6.0.3":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: df82cd1e172f957bae9c536286265a5cdbd5eeca487cb0a3b2a7b41ef959fc61f8e7c0e9aeea9c114ccf2c166b6a8dd45a46fd619c1c569d210ecd2765ad5169
  languageName: node
  linkType: hard

"kleur@npm:^4.1.4":
  version: 4.1.5
  resolution: "kleur@npm:4.1.5"
  checksum: 1dc476e32741acf0b1b5b0627ffd0d722e342c1b0da14de3e8ae97821327ca08f9fb944542fb3c126d90ac5f27f9d804edbe7c585bf7d12ef495d115e0f22c12
  languageName: node
  linkType: hard

"ky@npm:^1.2.0":
  version: 1.8.1
  resolution: "ky@npm:1.8.1"
  checksum: 802f3023ae1060b1d8c11376b9866fb5be82fa5174473d82c16a25d2905b3b41bc0121a134be87d8e3b40b24d56d34920a376e653785310803cbb8ea7cd43f85
  languageName: node
  linkType: hard

"lan-network@npm:^0.1.6":
  version: 0.1.7
  resolution: "lan-network@npm:0.1.7"
  bin:
    lan-network: dist/lan-network-cli.js
  checksum: 7b7793a60de60fa152371eba8b00e73c160b4aef28c51790e75c958e6031dcf314fe7a0e10de0610d902dd26cc562c7d88d0cb3cb3f2e23be4e4defb41c258c3
  languageName: node
  linkType: hard

"latest-version@npm:^9.0.0":
  version: 9.0.0
  resolution: "latest-version@npm:9.0.0"
  dependencies:
    package-json: ^10.0.0
  checksum: 98f0a33bcba8f77e3627d7febe896287cde2e631d39844b447e900b3bc954f5ffa2a353fbb343c6cf2827009f21f4b11ca36a54c03f6d989ed0e36a68606d422
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 638401d534585261b6003db9d99afd244dfe82d75ddb6db5c0df412842d5ab30b2ef18de471aaec70fe69a46f17b4ae3c7f01d8a4e6580ef7adb9f4273ad1e55
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lighthouse-logger@npm:^1.0.0":
  version: 1.4.2
  resolution: "lighthouse-logger@npm:1.4.2"
  dependencies:
    debug: ^2.6.9
    marky: ^1.2.2
  checksum: ba6b73d93424318fab58b4e07c9ed246e3e969a3313f26b69515ed4c06457dd9a0b11bc706948398fdaef26aa4ba5e65cb848c37ce59f470d3c6c450b9b79a33
  languageName: node
  linkType: hard

"lightningcss-darwin-arm64@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-darwin-arm64@npm:1.27.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-darwin-x64@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-darwin-x64@npm:1.27.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-freebsd-x64@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-freebsd-x64@npm:1.27.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-linux-arm-gnueabihf@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-arm-gnueabihf@npm:1.27.0"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-gnu@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-arm64-gnu@npm:1.27.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-musl@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-arm64-musl@npm:1.27.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-x64-gnu@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-x64-gnu@npm:1.27.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-x64-musl@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-x64-musl@npm:1.27.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-win32-arm64-msvc@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-win32-arm64-msvc@npm:1.27.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-win32-x64-msvc@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-win32-x64-msvc@npm:1.27.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"lightningcss@npm:~1.27.0":
  version: 1.27.0
  resolution: "lightningcss@npm:1.27.0"
  dependencies:
    detect-libc: ^1.0.3
    lightningcss-darwin-arm64: 1.27.0
    lightningcss-darwin-x64: 1.27.0
    lightningcss-freebsd-x64: 1.27.0
    lightningcss-linux-arm-gnueabihf: 1.27.0
    lightningcss-linux-arm64-gnu: 1.27.0
    lightningcss-linux-arm64-musl: 1.27.0
    lightningcss-linux-x64-gnu: 1.27.0
    lightningcss-linux-x64-musl: 1.27.0
    lightningcss-win32-arm64-msvc: 1.27.0
    lightningcss-win32-x64-msvc: 1.27.0
  dependenciesMeta:
    lightningcss-darwin-arm64:
      optional: true
    lightningcss-darwin-x64:
      optional: true
    lightningcss-freebsd-x64:
      optional: true
    lightningcss-linux-arm-gnueabihf:
      optional: true
    lightningcss-linux-arm64-gnu:
      optional: true
    lightningcss-linux-arm64-musl:
      optional: true
    lightningcss-linux-x64-gnu:
      optional: true
    lightningcss-linux-x64-musl:
      optional: true
    lightningcss-win32-arm64-msvc:
      optional: true
    lightningcss-win32-x64-msvc:
      optional: true
  checksum: 3761a4feb67ca250bf1b1cb1982a3d212dee56ea345dd487592908648e70d8c17da2f5918affaf08b6cdc4e4702eee29d800ff29e16d194e7af6300af1b28409
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"locate-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "locate-path@npm:3.0.0"
  dependencies:
    p-locate: ^3.0.0
    path-exists: ^3.0.0
  checksum: 53db3996672f21f8b0bf2a2c645ae2c13ffdae1eeecfcd399a583bce8516c0b88dcb4222ca6efbbbeb6949df7e46860895be2c02e8d3219abd373ace3bfb4e11
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"locate-path@npm:^7.2.0":
  version: 7.2.0
  resolution: "locate-path@npm:7.2.0"
  dependencies:
    p-locate: ^6.0.0
  checksum: c1b653bdf29beaecb3d307dfb7c44d98a2a98a02ebe353c9ad055d1ac45d6ed4e1142563d222df9b9efebc2bcb7d4c792b507fad9e7150a04c29530b7db570f8
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: cb9227612f71b83e42de93eccf1232feeb25e705bdb19ba26c04f91e885bfd3dd5c517c4a97137658190581d3493ea3973072ca010aab7e301046d90740393d1
  languageName: node
  linkType: hard

"lodash.capitalize@npm:^4.2.1":
  version: 4.2.1
  resolution: "lodash.capitalize@npm:4.2.1"
  checksum: d9195f31d48c105206f1099946d8bbc8ab71435bc1c8708296992a31a992bb901baf120fdcadd773098ac96e62a79e6b023ee7d26a2deb0d6c6aada930e6ad0a
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.escaperegexp@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.escaperegexp@npm:4.1.2"
  checksum: 6d99452b1cfd6073175a9b741a9b09ece159eac463f86f02ea3bee2e2092923fce812c8d2bf446309cc52d1d61bf9af51c8118b0d7421388e6cead7bd3798f0f
  languageName: node
  linkType: hard

"lodash.isplainobject@npm:^4.0.6":
  version: 4.0.6
  resolution: "lodash.isplainobject@npm:4.0.6"
  checksum: 29c6351f281e0d9a1d58f1a4c8f4400924b4c79f18dfc4613624d7d54784df07efaff97c1ff2659f3e085ecf4fff493300adc4837553104cef2634110b0d5337
  languageName: node
  linkType: hard

"lodash.isstring@npm:^4.0.1":
  version: 4.0.1
  resolution: "lodash.isstring@npm:4.0.1"
  checksum: eaac87ae9636848af08021083d796e2eea3d02e80082ab8a9955309569cb3a463ce97fd281d7dc119e402b2e7d8c54a23914b15d2fc7fff56461511dc8937ba0
  languageName: node
  linkType: hard

"lodash.kebabcase@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.kebabcase@npm:4.1.1"
  checksum: 5a6c59161914e1bae23438a298c7433e83d935e0f59853fa862e691164696bc07f6dfa4c313d499fbf41ba8d53314e9850416502376705a357d24ee6ca33af78
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash.mergewith@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.mergewith@npm:4.6.2"
  checksum: a6db2a9339752411f21b956908c404ec1e088e783a65c8b29e30ae5b3b6384f82517662d6f425cc97c2070b546cc2c7daaa8d33f78db7b6e9be06cd834abdeb8
  languageName: node
  linkType: hard

"lodash.snakecase@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.snakecase@npm:4.1.1"
  checksum: 1685ed3e83dda6eae5a4dcaee161a51cd210aabb3e1c09c57150e7dd8feda19e4ca0d27d0631eabe8d0f4eaa51e376da64e8c018ae5415417c5890d42feb72a8
  languageName: node
  linkType: hard

"lodash.startcase@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.startcase@npm:4.4.0"
  checksum: c03a4a784aca653845fe09d0ef67c902b6e49288dc45f542a4ab345a9c406a6dc194c774423fa313ee7b06283950301c1221dd2a1d8ecb2dac8dfbb9ed5606b5
  languageName: node
  linkType: hard

"lodash.throttle@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.throttle@npm:4.1.1"
  checksum: 129c0a28cee48b348aef146f638ef8a8b197944d4e9ec26c1890c19d9bf5a5690fe11b655c77a4551268819b32d27f4206343e30c78961f60b561b8608c8c805
  languageName: node
  linkType: hard

"lodash.uniq@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.uniq@npm:4.5.0"
  checksum: a4779b57a8d0f3c441af13d9afe7ecff22dd1b8ce1129849f71d9bbc8e8ee4e46dfb4b7c28f7ad3d67481edd6e51126e4e2a6ee276e25906d10f7140187c392d
  languageName: node
  linkType: hard

"lodash.uniqby@npm:^4.7.0":
  version: 4.7.0
  resolution: "lodash.uniqby@npm:4.7.0"
  checksum: 659264545a95726d1493123345aad8cbf56e17810fa9a0b029852c6d42bc80517696af09d99b23bef1845d10d95e01b8b4a1da578f22aeba7a30d3e0022a4938
  languageName: node
  linkType: hard

"lodash.upperfirst@npm:^4.3.1":
  version: 4.3.1
  resolution: "lodash.upperfirst@npm:4.3.1"
  checksum: cadec6955900afe1928cc60cdc4923a79c2ef991e42665419cc81630ed9b4f952a1093b222e0141ab31cbc4dba549f97ec28ff67929d71e01861c97188a5fa83
  languageName: node
  linkType: hard

"lodash@npm:4.17.21, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-symbols@npm:^2.2.0":
  version: 2.2.0
  resolution: "log-symbols@npm:2.2.0"
  dependencies:
    chalk: ^2.0.1
  checksum: 4c95e3b65f0352dbe91dc4989c10baf7a44e2ef5b0db7e6721e1476268e2b6f7090c3aa880d4f833a05c5c3ff18f4ec5215a09bd0099986d64a8186cfeb48ac8
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: ^4.1.0
    is-unicode-supported: ^0.1.0
  checksum: fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"log-symbols@npm:^6.0.0":
  version: 6.0.0
  resolution: "log-symbols@npm:6.0.0"
  dependencies:
    chalk: ^5.3.0
    is-unicode-supported: ^1.3.0
  checksum: 510cdda36700cbcd87a2a691ea08d310a6c6b449084018f7f2ec4f732ca5e51b301ff1327aadd96f53c08318e616276c65f7fe22f2a16704fb0715d788bc3c33
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"lru-cache@npm:^7.14.1":
  version: 7.18.3
  resolution: "lru-cache@npm:7.18.3"
  checksum: e550d772384709deea3f141af34b6d4fa392e2e418c1498c078de0ee63670f1f46f5eee746e8ef7e69e1c895af0d4224e62ee33e66a543a14763b0f2e74c1356
  languageName: node
  linkType: hard

"macos-release@npm:^3.1.0":
  version: 3.4.0
  resolution: "macos-release@npm:3.4.0"
  checksum: f4c0cb8b3f93b05d73c502b4bbe2b811c44facfc9bd072c13a30ff2a8ba1cad5d9de517d10be8b31e2b917643245a81587a2eec8300e66a7364419d11402ab02
  languageName: node
  linkType: hard

"make-dir@npm:^2.0.0, make-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "make-dir@npm:2.1.0"
  dependencies:
    pify: ^4.0.1
    semver: ^5.6.0
  checksum: 043548886bfaf1820323c6a2997e6d2fa51ccc2586ac14e6f14634f7458b4db2daf15f8c310e2a0abd3e0cddc64df1890d8fc7263033602c47bb12cbfcf86aab
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: ^7.5.3
  checksum: bf0731a2dd3aab4db6f3de1585cea0b746bb73eb5a02e3d8d72757e376e64e6ada190b1eddcde5b2f24a81b688a9897efd5018737d05e02e2a671dda9cff8a8a
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: 1.0.5
  checksum: b38a025a12c8146d6eeea5a7f2bf27d51d8ad6064da8ca9405fcf7bf9b54acd43e3b30ddd7abb9b1bfa4ddb266019133313482570ddb207de568f71ecfcf6060
  languageName: node
  linkType: hard

"map-obj@npm:^1.0.0":
  version: 1.0.1
  resolution: "map-obj@npm:1.0.1"
  checksum: 9949e7baec2a336e63b8d4dc71018c117c3ce6e39d2451ccbfd3b8350c547c4f6af331a4cbe1c83193d7c6b786082b6256bde843db90cb7da2a21e8fcc28afed
  languageName: node
  linkType: hard

"map-obj@npm:^4.1.0":
  version: 4.3.0
  resolution: "map-obj@npm:4.3.0"
  checksum: fbc554934d1a27a1910e842bc87b177b1a556609dd803747c85ece420692380827c6ae94a95cce4407c054fa0964be3bf8226f7f2cb2e9eeee432c7c1985684e
  languageName: node
  linkType: hard

"marky@npm:^1.2.2":
  version: 1.3.0
  resolution: "marky@npm:1.3.0"
  checksum: c25fe1d45525e317f89d116e87a50d385cc7e7d0d418548e75334273cb97990db37228c365718b5572077c80f22a599c732ccbd3da9728cd806465d63c786eda
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 0e513b29d120f478c85a70f49da0b8b19bc638975eca466f2eeae0071f3ad00454c621bf66e16dd435896c208e719fc91ad79bbfba4e400fe0b372e7c1c9c9a2
  languageName: node
  linkType: hard

"memoize-one@npm:^5.0.0":
  version: 5.2.1
  resolution: "memoize-one@npm:5.2.1"
  checksum: a3cba7b824ebcf24cdfcd234aa7f86f3ad6394b8d9be4c96ff756dafb8b51c7f71320785fbc2304f1af48a0467cbbd2a409efc9333025700ed523f254cb52e3d
  languageName: node
  linkType: hard

"memoize-one@npm:^6.0.0":
  version: 6.0.0
  resolution: "memoize-one@npm:6.0.0"
  checksum: f185ea69f7cceae5d1cb596266dcffccf545e8e7b4106ec6aa93b71ab9d16460dd118ac8b12982c55f6d6322fcc1485de139df07eacffaae94888b9b3ad7675f
  languageName: node
  linkType: hard

"meow@npm:^10.1.3":
  version: 10.1.5
  resolution: "meow@npm:10.1.5"
  dependencies:
    "@types/minimist": ^1.2.2
    camelcase-keys: ^7.0.0
    decamelize: ^5.0.0
    decamelize-keys: ^1.1.0
    hard-rejection: ^2.1.0
    minimist-options: 4.1.0
    normalize-package-data: ^3.0.2
    read-pkg-up: ^8.0.0
    redent: ^4.0.0
    trim-newlines: ^4.0.2
    type-fest: ^1.2.2
    yargs-parser: ^20.2.9
  checksum: dd5f0caa4af18517813547dc66741dcbf52c4c23def5062578d39b11189fd9457aee5c1f2263a5cd6592a465023df8357e8ac876b685b64dbcf545e3f66c23a7
  languageName: node
  linkType: hard

"meow@npm:^12.0.1":
  version: 12.1.1
  resolution: "meow@npm:12.1.1"
  checksum: a6f3be85fbe53430ef53ab933dd790c39216eb4dbaabdbef593aa59efb40ecaa417897000175476bc33eed09e4cbce01df7ba53ba91e9a4bd84ec07024cb8914
  languageName: node
  linkType: hard

"meow@npm:^13.0.0":
  version: 13.2.0
  resolution: "meow@npm:13.2.0"
  checksum: 79c61dc02ad448ff5c29bbaf1ef42181f1eae9947112c0e23db93e84cbc2708ecda53e54bfc6689f1e55255b2cea26840ec76e57a5773a16ca45f4fe2163ec1c
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"metro-babel-transformer@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-babel-transformer@npm:0.82.4"
  dependencies:
    "@babel/core": ^7.25.2
    flow-enums-runtime: ^0.0.6
    hermes-parser: 0.28.1
    nullthrows: ^1.1.1
  checksum: 03587a3f3e84180eb560b5652ffa62b08e89a0ff9a3bd8292e39c4ccae7836ce5e2d156f9cb33b56b3a0e9ed51453b458db626df7eee1515c02cf9dfd1cb6457
  languageName: node
  linkType: hard

"metro-cache-key@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-cache-key@npm:0.82.4"
  dependencies:
    flow-enums-runtime: ^0.0.6
  checksum: a6ab3908295b5ba346d4d991595cc8baf1d22be39fbd4bdf794b617868a003a4f9925d2d01fdbc4c616ff74196783cb4ea5f98dcb6a7c1b510e72e075d9f6b24
  languageName: node
  linkType: hard

"metro-cache@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-cache@npm:0.82.4"
  dependencies:
    exponential-backoff: ^3.1.1
    flow-enums-runtime: ^0.0.6
    https-proxy-agent: ^7.0.5
    metro-core: 0.82.4
  checksum: 8c6d9126872fc42de66bc8ebd8e827f7ed9da6c4f421db57e3efd7f43f1b44d664bcaea97c7d5b364e1391d5e0e4fc16581681f0b1c7f94db07c19569e2f80a5
  languageName: node
  linkType: hard

"metro-config@npm:0.82.4, metro-config@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro-config@npm:0.82.4"
  dependencies:
    connect: ^3.6.5
    cosmiconfig: ^5.0.5
    flow-enums-runtime: ^0.0.6
    jest-validate: ^29.7.0
    metro: 0.82.4
    metro-cache: 0.82.4
    metro-core: 0.82.4
    metro-runtime: 0.82.4
  checksum: 05daf4477e5db1dfda26ce5631de23510f5d893f3486bb259e00a576ab4f16b613b3e1b97eee160cf64ef75aaf4b2560cb3cc840e6149b04aa00409b27f6cbfe
  languageName: node
  linkType: hard

"metro-core@npm:0.82.4, metro-core@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro-core@npm:0.82.4"
  dependencies:
    flow-enums-runtime: ^0.0.6
    lodash.throttle: ^4.1.1
    metro-resolver: 0.82.4
  checksum: bb17d2f1adcd32e6402000a0a27b3a1682b2cc1835cc29f1bae0136fd31b97b37c79f1def55bd60f3a2a85028d073c671c82e72a9b1eef1465f1dae5ce02d3c8
  languageName: node
  linkType: hard

"metro-file-map@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-file-map@npm:0.82.4"
  dependencies:
    debug: ^4.4.0
    fb-watchman: ^2.0.0
    flow-enums-runtime: ^0.0.6
    graceful-fs: ^4.2.4
    invariant: ^2.2.4
    jest-worker: ^29.7.0
    micromatch: ^4.0.4
    nullthrows: ^1.1.1
    walker: ^1.0.7
  checksum: f5f24c5bcae7acbfbd2606707df35e1178e196c3e00d2a69bb8a4443942851989918e9f07e8301a7f8fb83d3fb17e9fd2320b9de322a2acfeb6f03f565c6bbf6
  languageName: node
  linkType: hard

"metro-minify-terser@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-minify-terser@npm:0.82.4"
  dependencies:
    flow-enums-runtime: ^0.0.6
    terser: ^5.15.0
  checksum: 23170c34f519ebaa57189283f51847108395f53ebfcb798e2907bf28e3fce8649f80ff4d1b3f0ed2e321287b61ea84ff825923d8879d23d36f7a9bcbbb804294
  languageName: node
  linkType: hard

"metro-resolver@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-resolver@npm:0.82.4"
  dependencies:
    flow-enums-runtime: ^0.0.6
  checksum: d4833712d70516930e60cfd59fa7695eacb23eb064b89819903e27f53f1350ed4acfaa02011655f8aacc63f41d15b0781489db17a167994701596192054d791e
  languageName: node
  linkType: hard

"metro-runtime@npm:0.82.4, metro-runtime@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro-runtime@npm:0.82.4"
  dependencies:
    "@babel/runtime": ^7.25.0
    flow-enums-runtime: ^0.0.6
  checksum: a0fa5004db83c6e132f2228c6d91aa56a31d97406c252b27b8e1bdff8f2ff6e453290cc44c4f07b4f0e458fc01eb28c3b85b7d915f6caffb3cc4d2c10f38abd9
  languageName: node
  linkType: hard

"metro-source-map@npm:0.82.4, metro-source-map@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro-source-map@npm:0.82.4"
  dependencies:
    "@babel/traverse": ^7.25.3
    "@babel/traverse--for-generate-function-map": "npm:@babel/traverse@^7.25.3"
    "@babel/types": ^7.25.2
    flow-enums-runtime: ^0.0.6
    invariant: ^2.2.4
    metro-symbolicate: 0.82.4
    nullthrows: ^1.1.1
    ob1: 0.82.4
    source-map: ^0.5.6
    vlq: ^1.0.0
  checksum: 41a5efbf6eff61db338922b5651ed69ca0cb42786100dc794c273147c9af2698ee3f3d7d967232b7591e9b8875d416c12fe7e1f10bb9cf8cb46c9d6a13c10773
  languageName: node
  linkType: hard

"metro-symbolicate@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-symbolicate@npm:0.82.4"
  dependencies:
    flow-enums-runtime: ^0.0.6
    invariant: ^2.2.4
    metro-source-map: 0.82.4
    nullthrows: ^1.1.1
    source-map: ^0.5.6
    vlq: ^1.0.0
  bin:
    metro-symbolicate: src/index.js
  checksum: dbe92d7eea7d71ebbfd35cc901d3e428774d7a4747d10781a8f4350a6c341edd352f3b25939a7c3f174a07deffac019a92bab04f32fe8cb7e8c3a708eab11115
  languageName: node
  linkType: hard

"metro-transform-plugins@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-transform-plugins@npm:0.82.4"
  dependencies:
    "@babel/core": ^7.25.2
    "@babel/generator": ^7.25.0
    "@babel/template": ^7.25.0
    "@babel/traverse": ^7.25.3
    flow-enums-runtime: ^0.0.6
    nullthrows: ^1.1.1
  checksum: b1a04093b41a8becd700ddae93278a87424f3c35b86bc0912eb5734948ea7f9d54c2440240277315cfabffc1dd9c4d4155c5286464546a97c5656981a97ce42d
  languageName: node
  linkType: hard

"metro-transform-worker@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-transform-worker@npm:0.82.4"
  dependencies:
    "@babel/core": ^7.25.2
    "@babel/generator": ^7.25.0
    "@babel/parser": ^7.25.3
    "@babel/types": ^7.25.2
    flow-enums-runtime: ^0.0.6
    metro: 0.82.4
    metro-babel-transformer: 0.82.4
    metro-cache: 0.82.4
    metro-cache-key: 0.82.4
    metro-minify-terser: 0.82.4
    metro-source-map: 0.82.4
    metro-transform-plugins: 0.82.4
    nullthrows: ^1.1.1
  checksum: 5d17296ba1ca6ce939c4ffbd99d7372a6033ba6f6d2da42634509a9c121055440ae5c5eea8677d9201e06d71d811729b313c3f6b54f69cb87d05c5b9f92c6334
  languageName: node
  linkType: hard

"metro@npm:0.82.4, metro@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro@npm:0.82.4"
  dependencies:
    "@babel/code-frame": ^7.24.7
    "@babel/core": ^7.25.2
    "@babel/generator": ^7.25.0
    "@babel/parser": ^7.25.3
    "@babel/template": ^7.25.0
    "@babel/traverse": ^7.25.3
    "@babel/types": ^7.25.2
    accepts: ^1.3.7
    chalk: ^4.0.0
    ci-info: ^2.0.0
    connect: ^3.6.5
    debug: ^4.4.0
    error-stack-parser: ^2.0.6
    flow-enums-runtime: ^0.0.6
    graceful-fs: ^4.2.4
    hermes-parser: 0.28.1
    image-size: ^1.0.2
    invariant: ^2.2.4
    jest-worker: ^29.7.0
    jsc-safe-url: ^0.2.2
    lodash.throttle: ^4.1.1
    metro-babel-transformer: 0.82.4
    metro-cache: 0.82.4
    metro-cache-key: 0.82.4
    metro-config: 0.82.4
    metro-core: 0.82.4
    metro-file-map: 0.82.4
    metro-resolver: 0.82.4
    metro-runtime: 0.82.4
    metro-source-map: 0.82.4
    metro-symbolicate: 0.82.4
    metro-transform-plugins: 0.82.4
    metro-transform-worker: 0.82.4
    mime-types: ^2.1.27
    nullthrows: ^1.1.1
    serialize-error: ^2.1.0
    source-map: ^0.5.6
    throat: ^5.0.0
    ws: ^7.5.10
    yargs: ^17.6.2
  bin:
    metro: src/cli.js
  checksum: cc155f963e393f0d1c6c2f5b094e9e2f3b1a0354d5bdf5bead5a5655ddbb2457fbd16fb101ba41bb0784b44970b7f72ba980229a24d2754598eabcefcccfb13f
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.7, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-db@npm:>= 1.43.0 < 2":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: e99aaf2f23f5bd607deb08c83faba5dd25cf2fec90a7cc5b92d8260867ee08dab65312e1a589e60093dc7796d41e5fae013268418482f1db4c7d52d0a0960ac9
  languageName: node
  linkType: hard

"mime-types@npm:2.1.35, mime-types@npm:^2.1.27, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: fef25e39263e6d207580bdc629f8872a3f9772c923c7f8c7e793175cee22777bbe8bba95e5d509a40aaa292d8974514ce634ae35769faa45f22d17edda5e8557
  languageName: node
  linkType: hard

"mimic-fn@npm:^1.0.0":
  version: 1.2.0
  resolution: "mimic-fn@npm:1.2.0"
  checksum: 69c08205156a1f4906d9c46f9b4dc08d18a50176352e77fdeb645cedfe9f20c0b19865d465bd2dec27a5c432347f24dc07fc3695e11159d193f892834233e939
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-fn@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-fn@npm:4.0.0"
  checksum: 995dcece15ee29aa16e188de6633d43a3db4611bcf93620e7e62109ec41c79c0f34277165b8ce5e361205049766e371851264c21ac64ca35499acb5421c2ba56
  languageName: node
  linkType: hard

"mimic-function@npm:^5.0.0":
  version: 5.0.1
  resolution: "mimic-function@npm:5.0.1"
  checksum: eb5893c99e902ccebbc267c6c6b83092966af84682957f79313311edb95e8bb5f39fb048d77132b700474d1c86d90ccc211e99bae0935447a4834eb4c882982c
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.1":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: bfc6dd03c5eaf623a4963ebd94d087f6f4bbbfd8c41329a7f09706b0cb66969c4ddd336abeb587bc44bc6f08e13bf90f0b374f9d71f9f01e04adc2cd6f083ef1
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 7564208ef81d7065a370f788d337cd80a689e981042cb9a1d0e6580b6c6a8c9279eba80010516e258835a988363f99f54a6f711a315089b8b42694f5da9d0d77
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.0, minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist-options@npm:4.1.0":
  version: 4.1.0
  resolution: "minimist-options@npm:4.1.0"
  dependencies:
    arrify: ^1.0.1
    is-plain-obj: ^1.1.0
    kind-of: ^6.0.3
  checksum: 8c040b3068811e79de1140ca2b708d3e203c8003eb9a414c1ab3cd467fc5f17c9ca02a5aef23bedc51a7f8bfbe77f87e9a7e31ec81fba304cda675b019496f4e
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.5, minimist@npm:^1.2.8":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3dfca705ce887ca9ff14d73e8d8593996dea1a1ecd8101fdbb9c10549d1f9670bc8fb66ad0192769ead4c2dc01b4f9ca1cf567ded365adff17827a303b948140
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: ^7.1.2
  checksum: 493bed14dcb6118da7f8af356a8947cf1473289c09658e5aabd69a737800a8c3b1736fb7d7931b722268a9c9bc038a6d53c049b6a6af24b34a121823bb709996
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"mute-stream@npm:1.0.0":
  version: 1.0.0
  resolution: "mute-stream@npm:1.0.0"
  checksum: 36fc968b0e9c9c63029d4f9dc63911950a3bdf55c9a87f58d3a266289b67180201cade911e7699f8b2fa596b34c9db43dad37649e3f7fdd13c3bb9edb0017ee7
  languageName: node
  linkType: hard

"mz@npm:^2.7.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: ^1.0.0
    object-assign: ^4.0.1
    thenify-all: ^1.0.0
  checksum: 8427de0ece99a07e9faed3c0c6778820d7543e3776f9a84d22cf0ec0a8eb65f6e9aee9c9d353ff9a105ff62d33a9463c6ca638974cc652ee8140cd1e35951c87
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.7":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 3be20d8866a57a6b6d218e82549711c8352ed969f9ab3c45379da28f405363ad4c9aeb0b39e9abc101a529ca65a72ff9502b00bf74a912c4b64a9d62dfd26c29
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 7ded10aa02a0707d1d12a9973fdb5954f98547ca7beb60e31cb3a403cc6e8f11138db7a3b0128425cf836fc85d145ec4ce983b2bdf83dca436af879c2d683510
  languageName: node
  linkType: hard

"neo-async@npm:^2.5.0, neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"nested-error-stacks@npm:~2.0.1":
  version: 2.0.1
  resolution: "nested-error-stacks@npm:2.0.1"
  checksum: 8430d7d80ad69b1add2992ee2992a363db6c1a26a54740963bc99a004c5acb1d2a67049397062eab2caa3a312b4da89a0b85de3bdf82d7d472a6baa166311fe6
  languageName: node
  linkType: hard

"netmask@npm:^2.0.2":
  version: 2.0.2
  resolution: "netmask@npm:2.0.2"
  checksum: c65cb8d3f7ea5669edddb3217e4c96910a60d0d9a4b52d9847ff6b28b2d0277cd8464eee0ef85133cdee32605c57940cacdd04a9a019079b091b6bba4cb0ec22
  languageName: node
  linkType: hard

"new-github-release-url@npm:2.0.0":
  version: 2.0.0
  resolution: "new-github-release-url@npm:2.0.0"
  dependencies:
    type-fest: ^2.5.1
  checksum: 3d4ae0f3b775623ceed8e558b6f9850e897aea981a9c937d3ad4e018669c829beccb2c4b5a6af996726ebf86c5b7638368dfc01f3ac2e395d1df29309bc0c5ca
  languageName: node
  linkType: hard

"node-fetch@npm:^2.7.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: d76d2f5edb451a3f05b15115ec89fc6be39de37c6089f1b6368df03b91e1633fd379a7e01b7ab05089a25034b2023d959b47e59759cb38d88341b2459e89d6e5
  languageName: node
  linkType: hard

"node-forge@npm:^1.2.1, node-forge@npm:^1.3.1":
  version: 1.3.1
  resolution: "node-forge@npm:1.3.1"
  checksum: 08fb072d3d670599c89a1704b3e9c649ff1b998256737f0e06fbd1a5bf41cae4457ccaee32d95052d80bbafd9ffe01284e078c8071f0267dc9744e51c5ed42a9
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    tinyglobby: ^0.2.12
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 2536282ba81f8a94b29482d3622b6ab298611440619e46de4512a6f32396a68b5530357c474b859787069d84a4c537d99e0c71078cce5b9f808bf84eeb78e8fb
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: d0b30b1ee6d961851c60d5eaa745d30b5c95d94bc0e74b81e5292f7c42a49e3af87f1eb9e89f59456f80645d679202537de751b7d72e9e40ceea40c5e449057e
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 917dbced519f48c6289a44830a0ca6dc944c3ee9243c468ebd8515a41c97c8b2c256edb7f3f750416bc37952cc9608684e6483c7b6c6f39f6bd8d86c52cfe658
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: ^3.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 49cfd3eb6f565e292bf61f2ff1373a457238804d5a5a63a8d786c923007498cba89f3648e3b952bc10203e3e7285752abf5b14eaf012edb821e84f24e881a92a
  languageName: node
  linkType: hard

"normalize-package-data@npm:^3.0.2":
  version: 3.0.3
  resolution: "normalize-package-data@npm:3.0.3"
  dependencies:
    hosted-git-info: ^4.0.1
    is-core-module: ^2.5.0
    semver: ^7.3.4
    validate-npm-package-license: ^3.0.1
  checksum: bbcee00339e7c26fdbc760f9b66d429258e2ceca41a5df41f5df06cc7652de8d82e8679ff188ca095cad8eff2b6118d7d866af2b68400f74602fbcbce39c160a
  languageName: node
  linkType: hard

"normalize-package-data@npm:^6.0.0":
  version: 6.0.2
  resolution: "normalize-package-data@npm:6.0.2"
  dependencies:
    hosted-git-info: ^7.0.0
    semver: ^7.3.5
    validate-npm-package-license: ^3.0.4
  checksum: ea35f8de68e03fc845f545c8197857c0cd256207fdb809ca63c2b39fe76ae77765ee939eb21811fb6c3b533296abf49ebe3cd617064f98a775adaccb24ff2e03
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"npm-package-arg@npm:^11.0.0":
  version: 11.0.3
  resolution: "npm-package-arg@npm:11.0.3"
  dependencies:
    hosted-git-info: ^7.0.0
    proc-log: ^4.0.0
    semver: ^7.3.5
    validate-npm-package-name: ^5.0.0
  checksum: cc6f22c39201aa14dcceeddb81bfbf7fa0484f94bcd2b3ad038e18afec5167c843cdde90c897f6034dc368faa0100c1eeee6e3f436a89e0af32ba932af4a8c28
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.0, npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: ^3.0.0
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"npm-run-path@npm:^5.1.0":
  version: 5.3.0
  resolution: "npm-run-path@npm:5.3.0"
  dependencies:
    path-key: ^4.0.0
  checksum: ae8e7a89da9594fb9c308f6555c73f618152340dcaae423e5fb3620026fefbec463618a8b761920382d666fa7a2d8d240b6fe320e8a6cdd54dc3687e2b659d25
  languageName: node
  linkType: hard

"nullthrows@npm:^1.1.1":
  version: 1.1.1
  resolution: "nullthrows@npm:1.1.1"
  checksum: 10806b92121253eb1b08ecf707d92480f5331ba8ae5b23fa3eb0548ad24196eb797ed47606153006568a5733ea9e528a3579f21421f7828e09e7756f4bdd386f
  languageName: node
  linkType: hard

"ob1@npm:0.82.4":
  version: 0.82.4
  resolution: "ob1@npm:0.82.4"
  dependencies:
    flow-enums-runtime: ^0.0.6
  checksum: 8385f5a20195c5c6e61bd18528a10baebe2287dd67fcf5721efeffe89dc61c7ab2b6c56ae9c6649687dda80a20663c33c18e4fc5cc651fd53e6befed3b9d9cf1
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 582810c6a8d2ef988ea0a39e69e115a138dad8f42dd445383b394877e5816eb4268489f316a6f74ee9c4e0a984b3eab1028e3e79d62b1ed67c726661d55c7a8b
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
    has-symbols: ^1.1.0
    object-keys: ^1.1.1
  checksum: 60e07d2651cf4f5528c485f1aa4dbded9b384c47d80e8187cefd11320abb1aebebf78df5483451dfa549059f8281c21f7b4bf7d19e9e5e97d8d617df0df298de
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.9":
  version: 1.1.9
  resolution: "object.entries@npm:1.1.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    define-properties: ^1.2.1
    es-object-atoms: ^1.1.1
  checksum: 0ab2ef331c4d6a53ff600a5d69182948d453107c3a1f7fd91bc29d387538c2aba21d04949a74f57c21907208b1f6fb175567fd1f39f1a7a4046ba1bca762fb41
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
  checksum: 29b2207a2db2782d7ced83f93b3ff5d425f901945f3665ffda1821e30a7253cd1fd6b891a64279976098137ddfa883d748787a6fea53ecdb51f8df8b8cec0ae1
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.1":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: f9b9a2a125ccf8ded29414d7c056ae0d187b833ee74919821fc60d7e216626db220d9cb3cf33f965c84aaaa96133626ca13b80f3c158b673976dc8cfcfcd26bb
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: d20929a25e7f0bb62f937a425b5edeb4e4cde0540d77ba146ec9357f00b0d497cdb3b9b05b9c8e46222407d1548d08166bff69cc56dfa55ba0e4469228920ff0
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: 1.1.1
  checksum: 1db595bd963b0124d6fa261d18320422407b8f01dc65863840f3ddaaf7bcad5b28ff6847286703ca53f4ec19595bd67a2f1253db79fc4094911ec6aa8df1671b
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 2bf13467215d1e540a62a75021e8b318a6cfc5d4fc53af8e8f84ad98dbcea02d506c6d24180cd62e1d769c44721ba542f3154effc1f7579a8288c9f7873ed8e5
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^2.0.0":
  version: 2.0.1
  resolution: "onetime@npm:2.0.1"
  dependencies:
    mimic-fn: ^1.0.0
  checksum: bb44015ac7a525d0fb43b029a583d4ad359834632b4424ca209b438aacf6d669dda81b5edfbdb42c22636e607b276ba5589f46694a729e3bc27948ce26f4cc1a
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"onetime@npm:^6.0.0":
  version: 6.0.0
  resolution: "onetime@npm:6.0.0"
  dependencies:
    mimic-fn: ^4.0.0
  checksum: 0846ce78e440841335d4e9182ef69d5762e9f38aa7499b19f42ea1c4cd40f0b4446094c455c713f9adac3f4ae86f613bb5e30c99e52652764d06a89f709b3788
  languageName: node
  linkType: hard

"onetime@npm:^7.0.0":
  version: 7.0.0
  resolution: "onetime@npm:7.0.0"
  dependencies:
    mimic-function: ^5.0.0
  checksum: eb08d2da9339819e2f9d52cab9caf2557d80e9af8c7d1ae86e1a0fef027d00a88e9f5bd67494d350df360f7c559fbb44e800b32f310fb989c860214eacbb561c
  languageName: node
  linkType: hard

"open@npm:10.1.0":
  version: 10.1.0
  resolution: "open@npm:10.1.0"
  dependencies:
    default-browser: ^5.2.1
    define-lazy-prop: ^3.0.0
    is-inside-container: ^1.0.0
    is-wsl: ^3.1.0
  checksum: 079b0771616bac13b08129b0300032dc9328d72f345e460dd0416b8a8196a5bdf5e0251fefec8aa2a6a97c736734ac65dd8f1d29ab3fc9a13e85624aa5bc4470
  languageName: node
  linkType: hard

"open@npm:^7.0.3":
  version: 7.4.2
  resolution: "open@npm:7.4.2"
  dependencies:
    is-docker: ^2.0.0
    is-wsl: ^2.1.1
  checksum: 3333900ec0e420d64c23b831bc3467e57031461d843c801f569b2204a1acc3cd7b3ec3c7897afc9dde86491dfa289708eb92bba164093d8bd88fb2c231843c91
  languageName: node
  linkType: hard

"open@npm:^8.0.4":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: ^2.0.0
    is-docker: ^2.1.1
    is-wsl: ^2.2.0
  checksum: 6388bfff21b40cb9bd8f913f9130d107f2ed4724ea81a8fd29798ee322b361ca31fa2cdfb491a5c31e43a3996cfe9566741238c7a741ada8d7af1cb78d85cf26
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"ora@npm:8.1.1":
  version: 8.1.1
  resolution: "ora@npm:8.1.1"
  dependencies:
    chalk: ^5.3.0
    cli-cursor: ^5.0.0
    cli-spinners: ^2.9.2
    is-interactive: ^2.0.0
    is-unicode-supported: ^2.0.0
    log-symbols: ^6.0.0
    stdin-discarder: ^0.2.2
    string-width: ^7.2.0
    strip-ansi: ^7.1.0
  checksum: 0cb79b9d8458ef0878e43d692fddb078c0885c82bbfa45e46de366f71fd506a75d8f9d5df71859624f7f0fe488c17d2e6882d7a35126214cf1a0e0c0f51248c4
  languageName: node
  linkType: hard

"ora@npm:^3.4.0":
  version: 3.4.0
  resolution: "ora@npm:3.4.0"
  dependencies:
    chalk: ^2.4.2
    cli-cursor: ^2.1.0
    cli-spinners: ^2.0.0
    log-symbols: ^2.2.0
    strip-ansi: ^5.2.0
    wcwidth: ^1.0.1
  checksum: f1f8e7f290b766276dcd19ddf2159a1971b1ec37eec4a5556b8f5e4afbe513a965ed65c183d38956724263b6a20989b3d8fb71b95ac4a2d6a01db2f1ed8899e4
  languageName: node
  linkType: hard

"ora@npm:^5.4.1":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: ^4.1.0
    chalk: ^4.1.0
    cli-cursor: ^3.1.0
    cli-spinners: ^2.5.0
    is-interactive: ^1.0.0
    is-unicode-supported: ^0.1.0
    log-symbols: ^4.1.0
    strip-ansi: ^6.0.0
    wcwidth: ^1.0.1
  checksum: 28d476ee6c1049d68368c0dc922e7225e3b5600c3ede88fade8052837f9ed342625fdaa84a6209302587c8ddd9b664f71f0759833cbdb3a4cf81344057e63c63
  languageName: node
  linkType: hard

"os-name@npm:5.1.0":
  version: 5.1.0
  resolution: "os-name@npm:5.1.0"
  dependencies:
    macos-release: ^3.1.0
    windows-release: ^5.0.1
  checksum: fae0fc02601d2966ee3255e80a6b3ac5d04265228d7b08563b4a8f2057732250cdff80b7ec33de2fef565cd92104078e71f4959fc081c6d197e2ec03a760ca42
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 5666560f7b9f10182548bf7013883265be33620b1c1b4a4d405c25be2636f970c5488ff3e6c48de75b55d02bde037249fe5dbfbb4c0fb7714953d56aed062e6d
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.2.6
    object-keys: ^1.1.1
    safe-push-apply: ^1.0.0
  checksum: cc9dd7d85c4ccfbe8109fce307d581ac7ede7b26de892b537873fbce2dc6a206d89aea0630dbb98e47ce0873517cefeaa7be15fcf94aaf4764a3b34b474a5b61
  languageName: node
  linkType: hard

"p-limit@npm:^2.0.0, p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2, p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-limit@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-limit@npm:4.0.0"
  dependencies:
    yocto-queue: ^1.0.0
  checksum: 01d9d70695187788f984226e16c903475ec6a947ee7b21948d6f597bed788e3112cc7ec2e171c1d37125057a5f45f3da21d8653e04a3a793589e12e9e80e756b
  languageName: node
  linkType: hard

"p-locate@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-locate@npm:3.0.0"
  dependencies:
    p-limit: ^2.0.0
  checksum: 83991734a9854a05fe9dbb29f707ea8a0599391f52daac32b86f08e21415e857ffa60f0e120bfe7ce0cc4faf9274a50239c7895fc0d0579d08411e513b83a4ae
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-locate@npm:^6.0.0":
  version: 6.0.0
  resolution: "p-locate@npm:6.0.0"
  dependencies:
    p-limit: ^4.0.0
  checksum: 2bfe5234efa5e7a4e74b30a5479a193fdd9236f8f6b4d2f3f69e3d286d9a7d7ab0c118a2a50142efcf4e41625def635bd9332d6cbf9cc65d85eb0718c579ab38
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-map@npm:^5.5.0":
  version: 5.5.0
  resolution: "p-map@npm:5.5.0"
  dependencies:
    aggregate-error: ^4.0.0
  checksum: 065cb6fca6b78afbd070dd9224ff160dc23eea96e57863c09a0c8ea7ce921043f76854be7ee0abc295cff1ac9adcf700e79a1fbe3b80b625081087be58e7effb
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"pac-proxy-agent@npm:^7.1.0":
  version: 7.2.0
  resolution: "pac-proxy-agent@npm:7.2.0"
  dependencies:
    "@tootallnate/quickjs-emscripten": ^0.23.0
    agent-base: ^7.1.2
    debug: ^4.3.4
    get-uri: ^6.0.1
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.6
    pac-resolver: ^7.0.1
    socks-proxy-agent: ^8.0.5
  checksum: 099c1bc8944da6a98e8b7de1fbf23e4014bc3063f66a7c29478bd852c1162e1d086a4f80f874f40961ebd5c516e736aed25852db97b79360cbdcc9db38086981
  languageName: node
  linkType: hard

"pac-resolver@npm:^7.0.1":
  version: 7.0.1
  resolution: "pac-resolver@npm:7.0.1"
  dependencies:
    degenerator: ^5.0.0
    netmask: ^2.0.2
  checksum: 839134328781b80d49f9684eae1f5c74f50a1d4482076d44c84fc2f3ca93da66fa11245a4725a057231e06b311c20c989fd0681e662a0792d17f644d8fe62a5e
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"package-json@npm:^10.0.0":
  version: 10.0.1
  resolution: "package-json@npm:10.0.1"
  dependencies:
    ky: ^1.2.0
    registry-auth-token: ^5.0.2
    registry-url: ^6.0.1
    semver: ^7.6.0
  checksum: bb197441910f065b1d644f7f0cfc532ed8bf8ae7fa777c2c626e0ccb1a10992e8538fca4b338d365a70a7a44a648b1583ecd7c57d564c1a2a3715f60440a0489
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: ^1.3.1
    json-parse-better-errors: ^1.0.1
  checksum: 0fe227d410a61090c247e34fa210552b834613c006c2c64d9a05cfe9e89cf8b4246d1246b1a99524b53b313e9ac024438d0680f67e33eaed7e6f38db64cfe7b5
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse-json@npm:^8.0.0":
  version: 8.3.0
  resolution: "parse-json@npm:8.3.0"
  dependencies:
    "@babel/code-frame": ^7.26.2
    index-to-position: ^1.1.0
    type-fest: ^4.39.1
  checksum: 23812dd66a8ceedfeb0fd8a92c96b88b18bc1030cf1f07cd29146b711a208ef91ac995cf14517422f908fa930f84324086bf22fdcc1013029776cc01d589bae4
  languageName: node
  linkType: hard

"parse-path@npm:^7.0.0":
  version: 7.1.0
  resolution: "parse-path@npm:7.1.0"
  dependencies:
    protocols: ^2.0.0
  checksum: 1da6535a967b14911837bba98e5f8d16acb415b28753ff6225e3121dce71167a96c79278fbb631d695210dadae37462a9eff40d93b9c659cf1ce496fd5db9bb6
  languageName: node
  linkType: hard

"parse-png@npm:^2.1.0":
  version: 2.1.0
  resolution: "parse-png@npm:2.1.0"
  dependencies:
    pngjs: ^3.3.0
  checksum: 0c6b6c42c8830cd16f6f9e9aedafd53111c0ad2ff350ba79c629996887567558f5639ad0c95764f96f7acd1f9ff63d4ac73737e80efa3911a6de9839ee520c96
  languageName: node
  linkType: hard

"parse-url@npm:^8.1.0":
  version: 8.1.0
  resolution: "parse-url@npm:8.1.0"
  dependencies:
    parse-path: ^7.0.0
  checksum: b93e21ab4c93c7d7317df23507b41be7697694d4c94f49ed5c8d6288b01cba328fcef5ba388e147948eac20453dee0df9a67ab2012415189fff85973bdffe8d9
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 96e92643aa34b4b28d0de1cd2eba52a1c5313a90c6542d03f62750d82480e20bfa62bc865d5cfc6165f5fcd5aeb0851043c40a39be5989646f223300021bae0a
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-exists@npm:^5.0.0":
  version: 5.0.0
  resolution: "path-exists@npm:5.0.0"
  checksum: 8ca842868cab09423994596eb2c5ec2a971c17d1a3cb36dbf060592c730c725cd524b9067d7d2a1e031fef9ba7bd2ac6dc5ec9fb92aa693265f7be3987045254
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-key@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-key@npm:4.0.0"
  checksum: 8e6c314ae6d16b83e93032c61020129f6f4484590a777eed709c4a01b50e498822b00f76ceaf94bc64dbd90b327df56ceadce27da3d83393790f1219e07721d7
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.5, path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"path-type@npm:^5.0.0":
  version: 5.0.0
  resolution: "path-type@npm:5.0.0"
  checksum: 15ec24050e8932c2c98d085b72cfa0d6b4eeb4cbde151a0a05726d8afae85784fc5544f733d8dfc68536587d5143d29c0bd793623fad03d7e61cc00067291cd5
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.0.1, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"picomatch@npm:^3.0.1":
  version: 3.0.1
  resolution: "picomatch@npm:3.0.1"
  checksum: b7fe18174bcc05bbf0ea09cc85623ae395676b3e6bc25636d4c20db79a948586237e429905453bf1ba385bc7a7aa5b56f1b351680e650d2b5c305ceb98dfc914
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: a7a5188c954f82c6585720e9143297ccd0e35ad8072231608086ca950bee672d51b0ef676254af0788205e59bd4e4deb4e7708769226bed725bf13370a7d1464
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 9c4e34278cb09987685fa5ef81499c82546c033713518f6441778fbec623fc708777fe8ac633097c72d88470d5963094076c7305cafc7ad340aae27cfacd856b
  languageName: node
  linkType: hard

"pirates@npm:^4.0.1, pirates@npm:^4.0.4, pirates@npm:^4.0.6":
  version: 4.0.7
  resolution: "pirates@npm:4.0.7"
  checksum: 3dcbaff13c8b5bc158416feb6dc9e49e3c6be5fddc1ea078a05a73ef6b85d79324bbb1ef59b954cdeff000dbf000c1d39f32dc69310c7b78fbada5171b583e40
  languageName: node
  linkType: hard

"pkg-dir@npm:^3.0.0":
  version: 3.0.0
  resolution: "pkg-dir@npm:3.0.0"
  dependencies:
    find-up: ^3.0.0
  checksum: 70c9476ffefc77552cc6b1880176b71ad70bfac4f367604b2b04efd19337309a4eec985e94823271c7c0e83946fa5aeb18cd360d15d10a5d7533e19344bfa808
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"plist@npm:^3.0.5":
  version: 3.1.0
  resolution: "plist@npm:3.1.0"
  dependencies:
    "@xmldom/xmldom": ^0.8.8
    base64-js: ^1.5.1
    xmlbuilder: ^15.1.1
  checksum: c8ea013da8646d4c50dff82f9be39488054621cc229957621bb00add42b5d4ce3657cf58d4b10c50f7dea1a81118f825838f838baeb4e6f17fab453ecf91d424
  languageName: node
  linkType: hard

"pngjs@npm:^3.3.0":
  version: 3.4.0
  resolution: "pngjs@npm:3.4.0"
  checksum: 8bd40bd698abd16b72c97b85cb858c80894fbedc76277ce72a784aa441e14795d45d9856e97333ca469b34b67528860ffc8a7317ca6beea349b645366df00bcd
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: cfcd4f05264eee8fd184cd4897a17890561d1d473434b43ab66ad3673d9c9128981ec01e0cb1d65a52cd6b1eebfb2eae1e53e39b2e0eca86afc823ede7a4f41b
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:~8.4.32":
  version: 8.4.49
  resolution: "postcss@npm:8.4.49"
  dependencies:
    nanoid: ^3.3.7
    picocolors: ^1.1.1
    source-map-js: ^1.2.1
  checksum: eb5d6cbdca24f50399aafa5d2bea489e4caee4c563ea1edd5a2485bc5f84e9ceef3febf170272bc83a99c31d23a316ad179213e853f34c2a7a8ffa534559d63a
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: ^1.1.2
  checksum: 00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier@npm:^3.0.3":
  version: 3.6.2
  resolution: "prettier@npm:3.6.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 0206f5f437892e8858f298af8850bf9d0ef1c22e21107a213ba56bfb9c2387a2020bfda244a20161d8e3dad40c6b04101609a55d370dece53d0a31893b64f861
  languageName: node
  linkType: hard

"pretty-bytes@npm:^5.6.0":
  version: 5.6.0
  resolution: "pretty-bytes@npm:5.6.0"
  checksum: 9c082500d1e93434b5b291bd651662936b8bd6204ec9fa17d563116a192d6d86b98f6d328526b4e8d783c07d5499e2614a807520249692da9ec81564b2f439cd
  languageName: node
  linkType: hard

"pretty-format@npm:^29.0.0, pretty-format@npm:^29.7.0":
  version: 29.7.0
  resolution: "pretty-format@npm:29.7.0"
  dependencies:
    "@jest/schemas": ^29.6.3
    ansi-styles: ^5.0.0
    react-is: ^18.0.0
  checksum: 032c1602383e71e9c0c02a01bbd25d6759d60e9c7cf21937dde8357aa753da348fcec5def5d1002c9678a8524d5fe099ad98861286550ef44de8808cc61e43b6
  languageName: node
  linkType: hard

"proc-log@npm:^4.0.0":
  version: 4.2.0
  resolution: "proc-log@npm:4.2.0"
  checksum: 98f6cd012d54b5334144c5255ecb941ee171744f45fca8b43b58ae5a0c1af07352475f481cadd9848e7f0250376ee584f6aa0951a856ff8f021bdfbff4eb33fc
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"progress@npm:^2.0.3":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: f67403fe7b34912148d9252cb7481266a354bd99ce82c835f79070643bb3c6583d10dbcfda4d41e04bbc1d8437e9af0fb1e1f2135727878f5308682a579429b7
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"promise@npm:^7.1.1":
  version: 7.3.1
  resolution: "promise@npm:7.3.1"
  dependencies:
    asap: ~2.0.3
  checksum: 475bb069130179fbd27ed2ab45f26d8862376a137a57314cf53310bdd85cc986a826fd585829be97ebc0aaf10e9d8e68be1bfe5a4a0364144b1f9eedfa940cf1
  languageName: node
  linkType: hard

"promise@npm:^8.3.0":
  version: 8.3.0
  resolution: "promise@npm:8.3.0"
  dependencies:
    asap: ~2.0.6
  checksum: a69f0ddbddf78ffc529cffee7ad950d307347615970564b17988ce43fbe767af5c738a9439660b24a9a8cbea106c0dcbb6c2b20e23b7e96a8e89e5c2679e94d5
  languageName: node
  linkType: hard

"prompts@npm:^2.0.1, prompts@npm:^2.3.2, prompts@npm:^2.4.2":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: ^3.0.3
    sisteransi: ^1.0.5
  checksum: d8fd1fe63820be2412c13bfc5d0a01909acc1f0367e32396962e737cb2fc52d004f3302475d5ce7d18a1e8a79985f93ff04ee03007d091029c3f9104bffc007d
  languageName: node
  linkType: hard

"prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"proto-list@npm:~1.2.1":
  version: 1.2.4
  resolution: "proto-list@npm:1.2.4"
  checksum: 4d4826e1713cbfa0f15124ab0ae494c91b597a3c458670c9714c36e8baddf5a6aad22842776f2f5b137f259c8533e741771445eb8df82e861eea37a6eaba03f7
  languageName: node
  linkType: hard

"protocols@npm:^2.0.0, protocols@npm:^2.0.1":
  version: 2.0.2
  resolution: "protocols@npm:2.0.2"
  checksum: 031cc068eb800468a50eb7c1e1c528bf142fb8314f5df9b9ea3c3f9df1697a19f97b9915b1229cef694d156812393172d9c3051ef7878d26eaa8c6faa5cccec4
  languageName: node
  linkType: hard

"proxy-agent@npm:6.5.0":
  version: 6.5.0
  resolution: "proxy-agent@npm:6.5.0"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    http-proxy-agent: ^7.0.1
    https-proxy-agent: ^7.0.6
    lru-cache: ^7.14.1
    pac-proxy-agent: ^7.1.0
    proxy-from-env: ^1.1.0
    socks-proxy-agent: ^8.0.5
  checksum: d03ad2d171c2768280ade7ea6a7c5b1d0746215d70c0a16e02780c26e1d347edd27b3f48374661ae54ec0f7b41e6e45175b687baf333b36b1fd109a525154806
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: ed7fcc2ba0a33404958e34d95d18638249a68c430e30fcb6c478497d72739ba64ce9810a24f53a7d921d0c065e5b78e3822759800698167256b04659366ca4d4
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.3
  resolution: "pump@npm:3.0.3"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: 52843fc933b838c0330f588388115a1b28ef2a5ffa7774709b142e35431e8ab0c2edec90de3fa34ebb72d59fef854f151eea7dfc211b6dcf586b384556bd2f39
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"pupa@npm:^3.1.0":
  version: 3.1.0
  resolution: "pupa@npm:3.1.0"
  dependencies:
    escape-goat: ^4.0.0
  checksum: 0e4f4ab6bbdce600fa6d23b1833f1af57b2641246ff4cbe10f9d66e4e5479b0de2864a88d5bd629eef59524eda3c6680726acd7f3f873d9ed46b7f095d0bb5f6
  languageName: node
  linkType: hard

"pure-rand@npm:^6.0.0":
  version: 6.1.0
  resolution: "pure-rand@npm:6.1.0"
  checksum: 8d53bc02bed99eca0b65b505090152ee7e9bd67dd74f8ff32ba1c883b87234067c5bf68d2614759fb217d82594d7a92919e6df80f97885e7b12b42af4bd3316a
  languageName: node
  linkType: hard

"qrcode-terminal@npm:0.11.0":
  version: 0.11.0
  resolution: "qrcode-terminal@npm:0.11.0"
  bin:
    qrcode-terminal: ./bin/qrcode-terminal.js
  checksum: ad146ea1e339e1745402a3ea131631f64f40f0d1ff9cc6bd9c21677feaa1ca6dcd32eadf188fd3febdab8bf6191b3d24d533454903a72543645a72820e4d324c
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"queue@npm:6.0.2":
  version: 6.0.2
  resolution: "queue@npm:6.0.2"
  dependencies:
    inherits: ~2.0.3
  checksum: ebc23639248e4fe40a789f713c20548e513e053b3dc4924b6cb0ad741e3f264dcff948225c8737834dd4f9ec286dbc06a1a7c13858ea382d9379f4303bcc0916
  languageName: node
  linkType: hard

"quick-lru@npm:^5.1.1":
  version: 5.1.1
  resolution: "quick-lru@npm:5.1.1"
  checksum: a516faa25574be7947969883e6068dbe4aa19e8ef8e8e0fd96cddd6d36485e9106d85c0041a27153286b0770b381328f4072aa40d3b18a19f5f7d2b78b94b5ed
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 0a268d4fea508661cf5743dfe3d5f47ce214fd6b7dec1de0da4d669dd4ef3d2144468ebe4179049eff253d9d27e719c88dae55be64f954e80135a0cada804ec9
  languageName: node
  linkType: hard

"rc@npm:1.2.8, rc@npm:~1.2.7":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: ^0.6.0
    ini: ~1.3.0
    minimist: ^1.2.0
    strip-json-comments: ~2.0.1
  bin:
    rc: ./cli.js
  checksum: 2e26e052f8be2abd64e6d1dabfbd7be03f80ec18ccbc49562d31f617d0015fbdbcf0f9eed30346ea6ab789e0fdfe4337f033f8016efdbee0df5354751842080e
  languageName: node
  linkType: hard

"react-devtools-core@npm:^6.1.1":
  version: 6.1.3
  resolution: "react-devtools-core@npm:6.1.3"
  dependencies:
    shell-quote: ^1.6.1
    ws: ^7
  checksum: 41fe32cb1e873d4284879d3cc41f40e9c1ae271fa65e6ae9f006aa8c0a1a88809c2cf8f2c1bb13d2db3c52f98b0823a2cddca08f323153333a35abf88a7bf8d3
  languageName: node
  linkType: hard

"react-dom@npm:19.0.0":
  version: 19.0.0
  resolution: "react-dom@npm:19.0.0"
  dependencies:
    scheduler: ^0.25.0
  peerDependencies:
    react: ^19.0.0
  checksum: 009cc6e575263a0d1906f9dd4aa6532d2d3d0d71e4c2b7777c8fe4de585fa06b5b77cdc2e0fbaa2f3a4a5e5d3305c189ba152153f358ee7da4d9d9ba5d3a8975
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: e20fe84c86ff172fc8d898251b7cc2c43645d108bf96d0b8edf39b98f9a2cae97b40520ee7ed8ee0085ccc94736c4886294456033304151c3f94978cec03df21
  languageName: node
  linkType: hard

"react-native-builder-bob@npm:^0.40.8":
  version: 0.40.12
  resolution: "react-native-builder-bob@npm:0.40.12"
  dependencies:
    "@babel/core": ^7.25.2
    "@babel/plugin-transform-flow-strip-types": ^7.26.5
    "@babel/plugin-transform-strict-mode": ^7.24.7
    "@babel/preset-env": ^7.25.2
    "@babel/preset-react": ^7.24.7
    "@babel/preset-typescript": ^7.24.7
    arktype: ^2.1.15
    babel-plugin-syntax-hermes-parser: ^0.28.0
    browserslist: ^4.20.4
    cross-spawn: ^7.0.3
    dedent: ^0.7.0
    del: ^6.1.1
    escape-string-regexp: ^4.0.0
    fs-extra: ^10.1.0
    glob: ^8.0.3
    is-git-dirty: ^2.0.1
    json5: ^2.2.1
    kleur: ^4.1.4
    prompts: ^2.4.2
    react-native-monorepo-config: ^0.1.8
    which: ^2.0.2
    yargs: ^17.5.1
  bin:
    bob: bin/bob
  checksum: 90869f3835597ba2b6a0cf6f5922120693d33c1b89da16f798731b3c4b99697f652948581f559c159ba380ee1f7ccd09cb6ce45b8b2749f1987c569ed3116661
  languageName: node
  linkType: hard

"react-native-edge-to-edge@npm:1.6.0":
  version: 1.6.0
  resolution: "react-native-edge-to-edge@npm:1.6.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 624893661f8708b6924faafb0586edc1f42b3741930866efd2dd263c9592e5abc8a4615d7a3ee41e8aa4da9486bda56c84385bc35a168269f22ec0a8432aa5de
  languageName: node
  linkType: hard

"react-native-is-edge-to-edge@npm:^1.1.6":
  version: 1.2.1
  resolution: "react-native-is-edge-to-edge@npm:1.2.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 8fb6d8ab7b953c7d7cec8c987cef24f1c5348a293a85cb49c7c53b54ef110c0ca746736ae730e297603c8c76020df912e93915fb17518c4f2f91143757177aba
  languageName: node
  linkType: hard

"react-native-linphone-voip-example@workspace:example":
  version: 0.0.0-use.local
  resolution: "react-native-linphone-voip-example@workspace:example"
  dependencies:
    "@babel/core": ^7.20.0
    "@expo/metro-runtime": ~5.0.4
    expo: ~53.0.16
    expo-status-bar: ~2.2.3
    react: 19.0.0
    react-dom: 19.0.0
    react-native: 0.79.5
    react-native-builder-bob: ^0.40.8
    react-native-monorepo-config: ^0.1.9
    react-native-web: ~0.20.0
  languageName: unknown
  linkType: soft

"react-native-linphone-voip@workspace:.":
  version: 0.0.0-use.local
  resolution: "react-native-linphone-voip@workspace:."
  dependencies:
    "@commitlint/config-conventional": ^19.6.0
    "@eslint/compat": ^1.2.7
    "@eslint/eslintrc": ^3.3.0
    "@eslint/js": ^9.22.0
    "@evilmartians/lefthook": ^1.5.0
    "@react-native/babel-preset": 0.78.2
    "@react-native/eslint-config": ^0.78.0
    "@release-it/conventional-changelog": ^9.0.2
    "@types/jest": ^29.5.5
    "@types/react": ^19.0.12
    commitlint: ^19.6.1
    del-cli: ^5.1.0
    eslint: ^9.22.0
    eslint-config-prettier: ^10.1.1
    eslint-plugin-prettier: ^5.2.3
    jest: ^29.7.0
    prettier: ^3.0.3
    react: 19.0.0
    react-native: 0.79.5
    react-native-builder-bob: ^0.40.8
    release-it: ^17.10.0
    typescript: ^5.8.3
  peerDependencies:
    react: "*"
    react-native: "*"
  languageName: unknown
  linkType: soft

"react-native-monorepo-config@npm:^0.1.8, react-native-monorepo-config@npm:^0.1.9":
  version: 0.1.9
  resolution: "react-native-monorepo-config@npm:0.1.9"
  dependencies:
    escape-string-regexp: ^5.0.0
    fast-glob: ^3.3.3
  checksum: 6356c362c517c49e17d54ee764c3566ba71491fa0d755618ecf2ca548348668e84fe448c24066645983acbc2bd4c0ed47594f9b3ec9dcc0558c0fd9594d2391e
  languageName: node
  linkType: hard

"react-native-web@npm:~0.20.0":
  version: 0.20.0
  resolution: "react-native-web@npm:0.20.0"
  dependencies:
    "@babel/runtime": ^7.18.6
    "@react-native/normalize-colors": ^0.74.1
    fbjs: ^3.0.4
    inline-style-prefixer: ^7.0.1
    memoize-one: ^6.0.0
    nullthrows: ^1.1.1
    postcss-value-parser: ^4.2.0
    styleq: ^0.1.3
  peerDependencies:
    react: ^18.0.0 || ^19.0.0
    react-dom: ^18.0.0 || ^19.0.0
  checksum: 8a75802b8f281466ff07547de34df434c918c7102b7de4cbca19bd532e38c692e70fc318bc0f8f5507b6d00c64c46540c65e926f757dd74ae7f6d279d98a6ed8
  languageName: node
  linkType: hard

"react-native@npm:0.79.5":
  version: 0.79.5
  resolution: "react-native@npm:0.79.5"
  dependencies:
    "@jest/create-cache-key-function": ^29.7.0
    "@react-native/assets-registry": 0.79.5
    "@react-native/codegen": 0.79.5
    "@react-native/community-cli-plugin": 0.79.5
    "@react-native/gradle-plugin": 0.79.5
    "@react-native/js-polyfills": 0.79.5
    "@react-native/normalize-colors": 0.79.5
    "@react-native/virtualized-lists": 0.79.5
    abort-controller: ^3.0.0
    anser: ^1.4.9
    ansi-regex: ^5.0.0
    babel-jest: ^29.7.0
    babel-plugin-syntax-hermes-parser: 0.25.1
    base64-js: ^1.5.1
    chalk: ^4.0.0
    commander: ^12.0.0
    event-target-shim: ^5.0.1
    flow-enums-runtime: ^0.0.6
    glob: ^7.1.1
    invariant: ^2.2.4
    jest-environment-node: ^29.7.0
    memoize-one: ^5.0.0
    metro-runtime: ^0.82.0
    metro-source-map: ^0.82.0
    nullthrows: ^1.1.1
    pretty-format: ^29.7.0
    promise: ^8.3.0
    react-devtools-core: ^6.1.1
    react-refresh: ^0.14.0
    regenerator-runtime: ^0.13.2
    scheduler: 0.25.0
    semver: ^7.1.3
    stacktrace-parser: ^0.1.10
    whatwg-fetch: ^3.0.0
    ws: ^6.2.3
    yargs: ^17.6.2
  peerDependencies:
    "@types/react": ^19.0.0
    react: ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  bin:
    react-native: cli.js
  checksum: 6db9368ca97ad2b4b2326d63edcb4ac151d77ad564afb3cbf1ae5caf8f169fee02bc83cb2411fc8b3e42f9d1ef08b2ac937a4260f67187829bed310e5d431988
  languageName: node
  linkType: hard

"react-refresh@npm:^0.14.0, react-refresh@npm:^0.14.2":
  version: 0.14.2
  resolution: "react-refresh@npm:0.14.2"
  checksum: d80db4bd40a36dab79010dc8aa317a5b931f960c0d83c4f3b81f0552cbcf7f29e115b84bb7908ec6a1eb67720fff7023084eff73ece8a7ddc694882478464382
  languageName: node
  linkType: hard

"react@npm:19.0.0":
  version: 19.0.0
  resolution: "react@npm:19.0.0"
  checksum: 86de15d85b2465feb40297a90319c325cb07cf27191a361d47bcfe8c6126c973d660125aa67b8f4cbbe39f15a2f32efd0c814e98196d8e5b68c567ba40a399c6
  languageName: node
  linkType: hard

"read-package-up@npm:^11.0.0":
  version: 11.0.0
  resolution: "read-package-up@npm:11.0.0"
  dependencies:
    find-up-simple: ^1.0.0
    read-pkg: ^9.0.0
    type-fest: ^4.6.0
  checksum: 535b7554d47fae5fb5c2e7aceebd48b5de4142cdfe7b21f942fa9a0f56db03d3b53cce298e19438e1149292279c285e6ba6722eca741d590fd242519c4bdbc17
  languageName: node
  linkType: hard

"read-pkg-up@npm:^8.0.0":
  version: 8.0.0
  resolution: "read-pkg-up@npm:8.0.0"
  dependencies:
    find-up: ^5.0.0
    read-pkg: ^6.0.0
    type-fest: ^1.0.1
  checksum: fe4c80401656b40b408884457fffb5a8015c03b1018cfd8e48f8d82a5e9023e24963603aeb2755608d964593e046c15b34d29b07d35af9c7aa478be81805209c
  languageName: node
  linkType: hard

"read-pkg@npm:^6.0.0":
  version: 6.0.0
  resolution: "read-pkg@npm:6.0.0"
  dependencies:
    "@types/normalize-package-data": ^2.4.0
    normalize-package-data: ^3.0.2
    parse-json: ^5.2.0
    type-fest: ^1.0.1
  checksum: 0cebdff381128e923815c643074a87011070e5fc352bee575d327d6485da3317fab6d802a7b03deeb0be7be8d3ad1640397b3d5d2f044452caf4e8d1736bf94f
  languageName: node
  linkType: hard

"read-pkg@npm:^9.0.0":
  version: 9.0.1
  resolution: "read-pkg@npm:9.0.1"
  dependencies:
    "@types/normalize-package-data": ^2.4.3
    normalize-package-data: ^6.0.0
    parse-json: ^8.0.0
    type-fest: ^4.6.0
    unicorn-magic: ^0.1.0
  checksum: 5544bea2a58c6e5706db49a96137e8f0768c69395f25363f934064fbba00bdcdaa326fcd2f4281741df38cf81dbf27b76138240dc6de0ed718cf650475e0de3c
  languageName: node
  linkType: hard

"readable-stream@npm:^3.0.2, readable-stream@npm:^3.4.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"recast@npm:^0.23.11":
  version: 0.23.11
  resolution: "recast@npm:0.23.11"
  dependencies:
    ast-types: ^0.16.1
    esprima: ~4.0.0
    source-map: ~0.6.1
    tiny-invariant: ^1.3.3
    tslib: ^2.0.1
  checksum: 1807159b1c33bc4a2d146e4ffea13b658e54bdcfab04fc4f9c9d7f1b4626c931e2ce41323e214516ec1e02a119037d686d825fc62f28072db27962b85e5b481d
  languageName: node
  linkType: hard

"rechoir@npm:^0.6.2":
  version: 0.6.2
  resolution: "rechoir@npm:0.6.2"
  dependencies:
    resolve: ^1.1.6
  checksum: fe76bf9c21875ac16e235defedd7cbd34f333c02a92546142b7911a0f7c7059d2e16f441fe6fb9ae203f459c05a31b2bcf26202896d89e390eda7514d5d2702b
  languageName: node
  linkType: hard

"redent@npm:^4.0.0":
  version: 4.0.0
  resolution: "redent@npm:4.0.0"
  dependencies:
    indent-string: ^5.0.0
    strip-indent: ^4.0.0
  checksum: 6944e7b1d8f3fd28c2515f5c605b9f7f0ea0f4edddf41890bbbdd4d9ee35abb7540c3b278f03ff827bd278bb6ff4a5bd8692ca406b748c5c1c3ce7355e9fbf8f
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.9
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.7
    get-proto: ^1.0.1
    which-builtin-type: ^1.2.1
  checksum: ccc5debeb66125e276ae73909cecb27e47c35d9bb79d9cc8d8d055f008c58010ab8cb401299786e505e4aab733a64cba9daf5f312a58e96a43df66adad221870
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: ^1.4.2
  checksum: d5c5fc13f8b8d7e16e791637a4bfef741f8d70e267d51845ee7d5404a32fa14c75b181c4efba33e4bff8b0000a2f13e9773593713dfe5b66597df4259275ce63
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.2":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 27481628d22a1c4e3ff551096a683b424242a216fee44685467307f14d58020af1e19660bf2e26064de946bad7eff28950eae9f8209d55723e2d9351e632bbb4
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3, regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-errors: ^1.3.0
    get-proto: ^1.0.1
    gopd: ^1.2.0
    set-function-name: ^2.0.2
  checksum: 18cb667e56cb328d2dda569d7f04e3ea78f2683135b866d606538cf7b1d4271f7f749f09608c877527799e6cf350e531368f3c7a20ccd1bb41048a48926bdeeb
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^10.2.0
    regjsgen: ^0.8.0
    regjsparser: ^0.12.0
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.1.0
  checksum: 67d3c4a3f6c99bc80b5d690074a27e6f675be1c1739f8a9acf028fbc36f1a468472574ea65e331e217995198ba4404d7878f3cb3739a73552dd3c70d3fb7f8e6
  languageName: node
  linkType: hard

"registry-auth-token@npm:^5.0.2":
  version: 5.1.0
  resolution: "registry-auth-token@npm:5.1.0"
  dependencies:
    "@pnpm/npm-conf": ^2.1.0
  checksum: 620c897167e2e0e9308b9cdd0288f70d651d9ec554348c39a96d398bb91d444e8cb4b3c0dc1e19d4a8f1c10ade85163baf606e5c09959baa31179bdfb1f7434e
  languageName: node
  linkType: hard

"registry-url@npm:^6.0.1":
  version: 6.0.1
  resolution: "registry-url@npm:6.0.1"
  dependencies:
    rc: 1.2.8
  checksum: 33712aa1b489aab7aba2191c1cdadfdd71f5bf166d4792d81744a6be332c160bd7d9273af8269d8a01284b9562f14a5b31b7abcf7ad9306c44887ecff51c89ab
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: a1d925ff14a4b2be774e45775ee6b33b256f89c42d480e6d85152d2133f18bd3d6af662161b226fa57466f7efec367eaf7ccd2a58c0ec2a1306667ba2ad07b0d
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: ~3.0.2
  bin:
    regjsparser: bin/parser
  checksum: 094b55b0ab3e1fd58f8ce5132a1d44dab08d91f7b0eea4132b0157b303ebb8ded20a9cbd893d25402d2aeddb23fac1f428ab4947b295d6fa51dd1c334a9e76f0
  languageName: node
  linkType: hard

"release-it@npm:^17.10.0":
  version: 17.11.0
  resolution: "release-it@npm:17.11.0"
  dependencies:
    "@iarna/toml": 2.2.5
    "@octokit/rest": 20.1.1
    async-retry: 1.3.3
    chalk: 5.4.1
    ci-info: ^4.1.0
    cosmiconfig: 9.0.0
    execa: 8.0.0
    git-url-parse: 14.0.0
    globby: 14.0.2
    inquirer: 9.3.2
    issue-parser: 7.0.1
    lodash: 4.17.21
    mime-types: 2.1.35
    new-github-release-url: 2.0.0
    open: 10.1.0
    ora: 8.1.1
    os-name: 5.1.0
    proxy-agent: 6.5.0
    semver: 7.6.3
    shelljs: 0.8.5
    update-notifier: 7.3.1
    url-join: 5.0.0
    wildcard-match: 5.1.4
    yargs-parser: 21.1.1
  bin:
    release-it: bin/release-it.js
  checksum: 0f1e43e23e4144901af9ffbc9ad9b4ab689198ee268d4c8c6b33e798c35c208f7d133a8a57ce95c67e12db4c1e752913e4af92d3dc31bdb9467be808f7b04729
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"requireg@npm:^0.2.2":
  version: 0.2.2
  resolution: "requireg@npm:0.2.2"
  dependencies:
    nested-error-stacks: ~2.0.1
    rc: ~1.2.7
    resolve: ~1.7.1
  checksum: 99b420a02e7272717153cdf75891cbb133c02c04b287721eb1bdb0668b6a98aa1da38c08d8148fc8b1443a668d939eeb622d390538ac8da17b18a977ebe998ae
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: ^5.0.0
  checksum: 546e0816012d65778e580ad62b29e975a642989108d9a3c5beabfb2304192fa3c9f9146fbdfe213563c6ff51975ae41bac1d3c6e047dd9572c94863a057b4d81
  languageName: node
  linkType: hard

"resolve-from@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-from@npm:3.0.0"
  checksum: fff9819254d2d62b57f74e5c2ca9c0bdd425ca47287c4d801bc15f947533148d858229ded7793b0f59e61e49e782fffd6722048add12996e1bd4333c29669062
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve-workspace-root@npm:^2.0.0":
  version: 2.0.0
  resolution: "resolve-workspace-root@npm:2.0.0"
  checksum: c7222391a35ecb3514fa04d753334a86f984d8ffe06ce87506582c4c5671ac608273b8f5e6faa2055be6e196785bf751ede9a48d484de53889d721b814c097ab
  languageName: node
  linkType: hard

"resolve.exports@npm:^2.0.0, resolve.exports@npm:^2.0.3":
  version: 2.0.3
  resolution: "resolve.exports@npm:2.0.3"
  checksum: abfb9f98278dcd0c19b8a49bb486abfafa23df4636d49128ea270dc982053c3ef230a530aecda1fae1322873fdfa6c97674fc539651ddfdb375ac58e0b8ef6df
  languageName: node
  linkType: hard

"resolve@npm:^1.1.6, resolve@npm:^1.20.0, resolve@npm:^1.22.10, resolve@npm:^1.22.2":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: ab7a32ff4046fcd7c6fdd525b24a7527847d03c3650c733b909b01b757f92eb23510afa9cc3e9bf3f26a3e073b48c88c706dfd4c1d2fb4a16a96b73b6328ddcf
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: a73ac69a1c4bd34c56b213d91f5b17ce390688fdb4a1a96ed3025cc7e08e7bfb90b3a06fcce461780cb0b589c958afcb0080ab802c71c01a7ecc8c64feafc89f
  languageName: node
  linkType: hard

"resolve@npm:~1.7.1":
  version: 1.7.1
  resolution: "resolve@npm:1.7.1"
  dependencies:
    path-parse: ^1.0.5
  checksum: afb829d4b923f9b17aaf55320c2feaf8d44577674a3a71510d299f832fb80f6703e5a701e01cf774c3241fe8663d4b2b99053cfbca7995488d18ea9f8c7ac309
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.1.6#~builtin<compat/resolve>, resolve@patch:resolve@^1.20.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.10#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.2#~builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#~builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 8aac1e4e4628bd00bf4b94b23de137dd3fe44097a8d528fd66db74484be929936e20c696e1a3edf4488f37e14180b73df6f600992baea3e089e8674291f16c9d
  languageName: node
  linkType: hard

"resolve@patch:resolve@^2.0.0-next.5#~builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#~builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 064d09c1808d0c51b3d90b5d27e198e6d0c5dad0eb57065fd40803d6a20553e5398b07f76739d69cbabc12547058bec6b32106ea66622375fb0d7e8fca6a846c
  languageName: node
  linkType: hard

"resolve@patch:resolve@~1.7.1#~builtin<compat/resolve>":
  version: 1.7.1
  resolution: "resolve@patch:resolve@npm%3A1.7.1#~builtin<compat/resolve>::version=1.7.1&hash=3bafbf"
  dependencies:
    path-parse: ^1.0.5
  checksum: c2a6f0e3856ac1ddc8297091c20ca6c36d99bf289ddea366c46bd2a7ed8b31075c7f9d01ff5d390ebed1fe41b9fabe57a79ae087992ba92e3592f0c3be07c1ac
  languageName: node
  linkType: hard

"restore-cursor@npm:^2.0.0":
  version: 2.0.0
  resolution: "restore-cursor@npm:2.0.0"
  dependencies:
    onetime: ^2.0.0
    signal-exit: ^3.0.2
  checksum: 482e13d02d834b6e5e3aa90304a8b5e840775d6f06916cc92a50038adf9f098dcc72405b567da8a37e137ae40ad3e31896fa3136ae62f7a426c2fbf53d036536
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: f877dd8741796b909f2a82454ec111afb84eb45890eb49ac947d87991379406b3b83ff9673a46012fca0d7844bb989f45cc5b788254cf1a39b6b5a9659de0630
  languageName: node
  linkType: hard

"restore-cursor@npm:^5.0.0":
  version: 5.1.0
  resolution: "restore-cursor@npm:5.1.0"
  dependencies:
    onetime: ^7.0.0
    signal-exit: ^4.1.0
  checksum: 838dd54e458d89cfbc1a923b343c1b0f170a04100b4ce1733e97531842d7b440463967e521216e8ab6c6f8e89df877acc7b7f4c18ec76e99fb9bf5a60d358d2c
  languageName: node
  linkType: hard

"retry@npm:0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 47c4d5be674f7c13eee4cfe927345023972197dbbdfba5d3af7e461d13b44de1bfd663bfc80d2f601f8ef3fc8164c16dd99655a221921954a65d044a2fc1233b
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 64cb3142ac5e9ad689aca289585cb41d22521f4571f73e9488af39f6b1bd62f0cbb3d65e2ecc768ec6494052523f473f1eb4b55c3e9014b3590c17fc6a03e22a
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"run-applescript@npm:^7.0.0":
  version: 7.0.0
  resolution: "run-applescript@npm:7.0.0"
  checksum: b02462454d8b182ad4117e5d4626e9e6782eb2072925c9fac582170b0627ae3c1ea92ee9b2df7daf84b5e9ffe14eb1cf5fb70bc44b15c8a0bfcdb47987e2410c
  languageName: node
  linkType: hard

"run-async@npm:^3.0.0":
  version: 3.0.0
  resolution: "run-async@npm:3.0.0"
  checksum: 280c03d5a88603f48103fc6fd69f07fb0c392a1e0d319c34ec96a2516030e07ba06f79231a563c78698b882649c2fc1fda601bc84705f57d50efcd1fa506cfc0
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rxjs@npm:^7.8.1":
  version: 7.8.2
  resolution: "rxjs@npm:7.8.2"
  dependencies:
    tslib: ^2.1.0
  checksum: 2f233d7c832a6c255dabe0759014d7d9b1c9f1cb2f2f0d59690fd11c883c9826ea35a51740c06ab45b6ade0d9087bde9192f165cba20b6730d344b831ef80744
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    has-symbols: ^1.1.0
    isarray: ^2.0.5
  checksum: 00f6a68140e67e813f3ad5e73e6dedcf3e42a9fa01f04d44b0d3f7b1f4b257af876832a9bfc82ac76f307e8a6cc652e3cf95876048a26cbec451847cf6ae3707
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    isarray: ^2.0.5
  checksum: 8c11cbee6dc8ff5cc0f3d95eef7052e43494591384015902e4292aef4ae9e539908288520ed97179cee17d6ffb450fe5f05a46ce7a1749685f7524fd568ab5db
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-regex: ^1.2.1
  checksum: 3c809abeb81977c9ed6c869c83aca6873ea0f3ab0f806b8edbba5582d51713f8a6e9757d24d2b4b088f563801475ea946c8e77e7713e8c65cdd02305b6caedab
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sax@npm:>=0.6.0":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 3ad64df16b743f0f2eb7c38ced9692a6d924f1cd07bbe45c39576c2cf50de8290d9d04e7b2228f924c7d05fecc4ec5cf651423278e0c7b63d260c387ef3af84a
  languageName: node
  linkType: hard

"scheduler@npm:0.25.0, scheduler@npm:^0.25.0":
  version: 0.25.0
  resolution: "scheduler@npm:0.25.0"
  checksum: b7bb9fddbf743e521e9aaa5198a03ae823f5e104ebee0cb9ec625392bb7da0baa1c28ab29cee4b1e407a94e76acc6eee91eeb749614f91f853efda2613531566
  languageName: node
  linkType: hard

"semver@npm:7.6.3":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 4110ec5d015c9438f322257b1c51fe30276e5f766a3f64c09edd1d7ea7118ecbc3f379f3b69032bacf13116dc7abc4ad8ce0d7e2bd642e26b0d271b56b61a7d8
  languageName: node
  linkType: hard

"semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.1.3, semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.5.2, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.6.3":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: dd94ba8f1cbc903d8eeb4dd8bf19f46b3deb14262b6717d0de3c804b594058ae785ef2e4b46c5c3b58733c99c83339068203002f9e37cfe44f7e2cc5e3d2f621
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 2.0.0
    mime: 1.6.0
    ms: 2.1.3
    on-finished: 2.4.1
    range-parser: ~1.2.1
    statuses: 2.0.1
  checksum: 5ae11bd900c1c2575525e2aa622e856804e2f96a09281ec1e39610d089f53aa69e13fd8db84b52f001d0318cf4bb0b3b904ad532fc4c0014eb90d32db0cff55f
  languageName: node
  linkType: hard

"send@npm:^0.19.0":
  version: 0.19.1
  resolution: "send@npm:0.19.1"
  dependencies:
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    encodeurl: ~2.0.0
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 2.0.0
    mime: 1.6.0
    ms: 2.1.3
    on-finished: 2.4.1
    range-parser: ~1.2.1
    statuses: 2.0.1
  checksum: 2a1991c8ac23a9b47c4477fbed056f1e4503ef683c669e9113303f793965c42f462d763755378eef9ad8b8c0e0cfbcf7789e2e517fa8d7451bc2cf8b3feca01e
  languageName: node
  linkType: hard

"serialize-error@npm:^2.1.0":
  version: 2.1.0
  resolution: "serialize-error@npm:2.1.0"
  checksum: 28464a6f65e6becd6e49fb782aff06573fdbf3d19f161a20228179842fed05c75a34110e54c3ee020b00240f9e11d8bee9b9fee5d04e0bc0bef1fdbf2baa297e
  languageName: node
  linkType: hard

"serve-static@npm:^1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: ~2.0.0
    escape-html: ~1.0.3
    parseurl: ~1.3.3
    send: 0.19.0
  checksum: dffc52feb4cc5c68e66d0c7f3c1824d4e989f71050aefc9bd5f822a42c54c9b814f595fc5f2b717f4c7cc05396145f3e90422af31186a93f76cf15f707019759
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.2
  checksum: d6229a71527fd0404399fc6227e0ff0652800362510822a291925c9d7b48a1ca1a468b11b281471c34cd5a2da0db4f5d7ff315a61d26655e77f6e971e6d0c80f
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
  checksum: ec27cbbe334598547e99024403e96da32aca3e530583e4dba7f5db1c43cbc4affa9adfbd77c7b2c210b9b8b2e7b2e600bad2a6c44fd62e804d8233f96bbb62f4
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: c9a6f2c5b51a2dabdc0247db9c46460152ffc62ee139f3157440bd48e7c59425093f42719ac1d7931f054f153e2d26cf37dfeb8da17a794a58198a2705e527fd
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: ^6.0.2
  checksum: 39b3dd9630a774aba288a680e7d2901f5c0eae7b8387fc5c8ea559918b29b3da144b7bdb990d7ccd9e11be05508ac9e459ce51d01fd65e583282f6ffafcba2e7
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shell-quote@npm:^1.6.1":
  version: 1.8.3
  resolution: "shell-quote@npm:1.8.3"
  checksum: 550dd84e677f8915eb013d43689c80bb114860649ec5298eb978f40b8f3d4bc4ccb072b82c094eb3548dc587144bb3965a8676f0d685c1cf4c40b5dc27166242
  languageName: node
  linkType: hard

"shelljs@npm:0.8.5":
  version: 0.8.5
  resolution: "shelljs@npm:0.8.5"
  dependencies:
    glob: ^7.0.0
    interpret: ^1.0.0
    rechoir: ^0.6.2
  bin:
    shjs: bin/shjs
  checksum: 7babc46f732a98f4c054ec1f048b55b9149b98aa2da32f6cf9844c434b43c6251efebd6eec120937bd0999e13811ebd45efe17410edb3ca938f82f9381302748
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 42501371cdf71f4ccbbc9c9e2eb00aaaab80a4c1c429d5e8da713fd4d39ef3b8d4a4b37ed4f275798a65260a551a7131fd87fe67e922dba4ac18586d6aab8b06
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: bf73d6d6682034603eb8e99c63b50155017ed78a522d27c2acec0388a792c3ede3238b878b953a08157093b85d05797217d270b7666ba1f111345fbe933380ff
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1, signal-exit@npm:^4.1.0":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"simple-plist@npm:^1.1.0":
  version: 1.4.0
  resolution: "simple-plist@npm:1.4.0"
  dependencies:
    bplist-creator: 0.1.1
    bplist-parser: 0.3.2
    plist: ^3.0.5
  checksum: fa8086f6b781c289f1abad21306481dda4af6373b32a5d998a70e53c2b7218a1d21ebb5ae3e736baae704c21d311d3d39d01d0e6a2387eda01b4020b9ebd909e
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: aba6438f46d2bfcef94cf112c835ab395172c75f67453fe05c340c770d3c402363018ae1ab4172a1026a90c47eaccf3af7b6ff6fa749a680c2929bd7fa2b37a4
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slash@npm:^4.0.0":
  version: 4.0.0
  resolution: "slash@npm:4.0.0"
  checksum: da8e4af73712253acd21b7853b7e0dbba776b786e82b010a5bfc8b5051a1db38ed8aba8e1e8f400dd2c9f373be91eb1c42b66e91abb407ff42b10feece5e1d2d
  languageName: node
  linkType: hard

"slash@npm:^5.1.0":
  version: 5.1.0
  resolution: "slash@npm:5.1.0"
  checksum: 70434b34c50eb21b741d37d455110258c42d2cf18c01e6518aeb7299f3c6e626330c889c0c552b5ca2ef54a8f5a74213ab48895f0640717cacefeef6830a1ba4
  languageName: node
  linkType: hard

"slugify@npm:^1.3.4, slugify@npm:^1.6.6":
  version: 1.6.6
  resolution: "slugify@npm:1.6.6"
  checksum: 04773c2d3b7aea8d2a61fa47cc7e5d29ce04e1a96cbaec409da57139df906acb3a449fac30b167d203212c806e73690abd4ff94fbad0a9a7b7ea109a2a638ae9
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3, socks-proxy-agent@npm:^8.0.5":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.5
  resolution: "socks@npm:2.8.5"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: d39a77a8c91cfacafc75c67dba45925eccfd884a8a4a68dcda6fb9ab7f37de6e250bb6db3721e8a16a066a8e1ebe872d4affc26f3eb763f4befedcc7b733b7ed
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 4eb0cd997cdf228bc253bcaff9340afeb706176e64868ecd20efbe6efea931465f43955612346d6b7318789e5265bdc419bc7669c1cebe3db0eb255f57efa76b
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.13":
  version: 0.5.13
  resolution: "source-map-support@npm:0.5.13"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 933550047b6c1a2328599a21d8b7666507427c0f5ef5eaadd56b5da0fd9505e239053c66fe181bf1df469a3b7af9d775778eee283cbb7ae16b902ddc09e93a97
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.16, source-map-support@npm:~0.5.20, source-map-support@npm:~0.5.21":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.5.6":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 5dc2043b93d2f194142c7f38f74a24670cd7a0063acdaf4bf01d2964b402257ae843c2a8fa822ad5b71013b5fcafa55af7421383da919752f22ff488bc553f4d
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.2.0
  resolution: "spdx-correct@npm:3.2.0"
  dependencies:
    spdx-expression-parse: ^3.0.0
    spdx-license-ids: ^3.0.0
  checksum: e9ae98d22f69c88e7aff5b8778dc01c361ef635580e82d29e5c60a6533cc8f4d820803e67d7432581af0cc4fb49973125076ee3b90df191d153e223c004193b2
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.5.0
  resolution: "spdx-exceptions@npm:2.5.0"
  checksum: bb127d6e2532de65b912f7c99fc66097cdea7d64c10d3ec9b5e96524dbbd7d20e01cba818a6ddb2ae75e62bb0c63d5e277a7e555a85cbc8ab40044984fa4ae15
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: a1c6e104a2cbada7a593eaa9f430bd5e148ef5290d4c0409899855ce8b1c39652bcc88a725259491a82601159d6dc790bedefc9016c7472f7de8de7361f8ccde
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.21
  resolution: "spdx-license-ids@npm:3.0.21"
  checksum: 681dfe26d250f48cc725c9118adf1eb0a175e3c298cd8553c039bfae37ed21bea30a27bc02dbb99b4a0d3a25c644c5dda952090e11ef4b3093f6ec7db4b93b58
  languageName: node
  linkType: hard

"split2@npm:^4.0.0":
  version: 4.2.0
  resolution: "split2@npm:4.2.0"
  checksum: 05d54102546549fe4d2455900699056580cca006c0275c334611420f854da30ac999230857a85fdd9914dc2109ae50f80fda43d2a445f2aa86eccdc1dfce779d
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: ^2.0.0
  checksum: 052bf4d25bbf5f78e06c1d5e67de2e088b06871fa04107ca8d3f0e9d9263326e2942c8bedee3545795fc77d787d443a538345eef74db2f8e35db3558c6f91ff7
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: bae1596873595c4610993fa84f86a3387d67586401c1816ea048c0196800c0646c4d2da98c2ee80557fd9eff05877efe33b91ba6cd052658ed96ddc85d19067d
  languageName: node
  linkType: hard

"stacktrace-parser@npm:^0.1.10":
  version: 0.1.11
  resolution: "stacktrace-parser@npm:0.1.11"
  dependencies:
    type-fest: ^0.7.1
  checksum: 1120cf716606ec6a8e25cc9b6ada79d7b91e6a599bba1a6664e6badc8b5f37987d7df7d9ad0344f717a042781fd8e1e999de08614a5afea451b68902421036b5
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: c469b9519de16a4bb19600205cffb39ee471a5f17b82589757ca7bd40a8d92ebb6ed9f98b5a540c5d302ccbc78f15dc03cc0280dd6e00df1335568a5d5758a5c
  languageName: node
  linkType: hard

"stdin-discarder@npm:^0.2.2":
  version: 0.2.2
  resolution: "stdin-discarder@npm:0.2.2"
  checksum: 642ffd05bd5b100819d6b24a613d83c6e3857c6de74eb02fc51506fa61dc1b0034665163831873868157c4538d71e31762bcf319be86cea04c3aba5336470478
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.1.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    internal-slot: ^1.1.0
  checksum: be944489d8829fb3bdec1a1cc4a2142c6b6eb317305eeace1ece978d286d6997778afa1ae8cb3bd70e2b274b9aa8c69f93febb1e15b94b1359b11058f9d3c3a1
  languageName: node
  linkType: hard

"stream-buffers@npm:2.2.x":
  version: 2.2.0
  resolution: "stream-buffers@npm:2.2.0"
  checksum: 4587d9e8f050d689fb38b4295e73408401b16de8edecc12026c6f4ae92956705ecfd995ae3845d7fa3ebf19502d5754df9143d91447fd881d86e518f43882c1c
  languageName: node
  linkType: hard

"string-length@npm:^4.0.1":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: ^1.0.2
    strip-ansi: ^6.0.0
  checksum: ce85533ef5113fcb7e522bcf9e62cb33871aa99b3729cec5595f4447f660b0cefd542ca6df4150c97a677d58b0cb727a3fe09ac1de94071d05526c73579bf505
  languageName: node
  linkType: hard

"string-natural-compare@npm:^3.0.1":
  version: 3.0.1
  resolution: "string-natural-compare@npm:3.0.1"
  checksum: 65910d9995074086e769a68728395effbba9b7186be5b4c16a7fad4f4ef50cae95ca16e3e9086e019cbb636ae8daac9c7b8fe91b5f21865c5c0f26e3c0725406
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string-width@npm:^7.0.0, string-width@npm:^7.2.0":
  version: 7.2.0
  resolution: "string-width@npm:7.2.0"
  dependencies:
    emoji-regex: ^10.3.0
    get-east-asian-width: ^1.0.0
    strip-ansi: ^7.1.0
  checksum: 42f9e82f61314904a81393f6ef75b832c39f39761797250de68c041d8ba4df2ef80db49ab6cd3a292923a6f0f409b8c9980d120f7d32c820b4a8a84a2598a295
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.12":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-abstract: ^1.23.6
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.6
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    internal-slot: ^1.1.0
    regexp.prototype.flags: ^1.5.3
    set-function-name: ^2.0.2
    side-channel: ^1.1.0
  checksum: 98a09d6af91bfc6ee25556f3d7cd6646d02f5f08bda55d45528ed273d266d55a71af7291fe3fc76854deffb9168cc1a917d0b07a7d5a178c7e9537c99e6d2b57
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: ^1.1.3
    es-abstract: ^1.17.5
  checksum: 95dfc514ed7f328d80a066dabbfbbb1615c3e51490351085409db2eb7cbfed7ea29fdadaf277647fbf9f4a1e10e6dd9e95e78c0fd2c4e6bb6723ea6e59401004
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-data-property: ^1.1.4
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-object-atoms: ^1.0.0
    has-property-descriptors: ^1.0.2
  checksum: 87659cd8561237b6c69f5376328fda934693aedde17bb7a2c57008e9d9ff992d0c253a391c7d8d50114e0e49ff7daf86a362f7961cf92f7564cd01342ca2e385
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: cb86f639f41d791a43627784be2175daa9ca3259c7cb83e7a207a729909b74f2ea0ec5d85de5761e6835e5f443e9420c6ff3f63a845378e4a61dd793177bc287
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: df1007a7f580a49d692375d996521dc14fd103acda7f3034b3c558a60b82beeed3a64fa91e494e164581793a8ab0ae2f59578a49896a7af6583c1f20472bce96
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^5.2.0":
  version: 5.2.0
  resolution: "strip-ansi@npm:5.2.0"
  dependencies:
    ansi-regex: ^4.1.0
  checksum: bdb5f76ade97062bd88e7723aa019adbfacdcba42223b19ccb528ffb9fb0b89a5be442c663c4a3fb25268eaa3f6ea19c7c3fbae830bd1562d55adccae1fcec46
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1, strip-ansi@npm:^7.1.0":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 9dbcfbaf503c57c06af15fe2c8176fb1bf3af5ff65003851a102749f875a6dbe0ab3b30115eccf6e805e9d756830d3e40ec508b62b3f1ddf3761a20ebe29d3f3
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-final-newline@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-final-newline@npm:3.0.0"
  checksum: 23ee263adfa2070cd0f23d1ac14e2ed2f000c9b44229aec9c799f1367ec001478469560abefd00c5c99ee6f0b31c137d53ec6029c53e9f32a93804e18c201050
  languageName: node
  linkType: hard

"strip-indent@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-indent@npm:4.0.0"
  dependencies:
    min-indent: ^1.0.1
  checksum: 06cbcd93da721c46bc13caeb1c00af93a9b18146a1c95927672d2decab6a25ad83662772417cea9317a2507fb143253ecc23c4415b64f5828cef9b638a744598
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"structured-headers@npm:^0.4.1":
  version: 0.4.1
  resolution: "structured-headers@npm:0.4.1"
  checksum: 2f3073b2c8b4f2515367a1647ba0b6764ce6d35b3943605940de41077c2afd2513257f4bf6fbfd67a3455f25a3e844905da6fddde6b6ad7974256495311a25a3
  languageName: node
  linkType: hard

"stubborn-fs@npm:^1.2.5":
  version: 1.2.5
  resolution: "stubborn-fs@npm:1.2.5"
  checksum: 28d197afec1ec21ce7ffb06a42f01db19beecdf491694c53393ff5d92ec8a300df9c357e09a16d5cf55747ee2a70bc3c788b9e5577696061ffb9ae279655a600
  languageName: node
  linkType: hard

"styleq@npm:^0.1.3":
  version: 0.1.3
  resolution: "styleq@npm:0.1.3"
  checksum: 14a8d23abd914166a9b4bd04ed753bd91363f0e029ee4a94ec2c7dc37d3213fe01fceee22dc655288da3ae89f5dc01cec42d5e2b58478b0dea33bf5bdf509be1
  languageName: node
  linkType: hard

"sucrase@npm:3.35.0":
  version: 3.35.0
  resolution: "sucrase@npm:3.35.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.2
    commander: ^4.0.0
    glob: ^10.3.10
    lines-and-columns: ^1.1.6
    mz: ^2.7.0
    pirates: ^4.0.1
    ts-interface-checker: ^0.1.9
  bin:
    sucrase: bin/sucrase
    sucrase-node: bin/sucrase-node
  checksum: 9fc5792a9ab8a14dcf9c47dcb704431d35c1cdff1d17d55d382a31c2e8e3063870ad32ce120a80915498486246d612e30cda44f1624d9d9a10423e1a43487ad1
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^2.0.0":
  version: 2.3.0
  resolution: "supports-hyperlinks@npm:2.3.0"
  dependencies:
    has-flag: ^4.0.0
    supports-color: ^7.0.0
  checksum: 9ee0de3c8ce919d453511b2b1588a8205bd429d98af94a01df87411391010fe22ca463f268c84b2ce2abad019dfff8452aa02806eeb5c905a8d7ad5c4f4c52b8
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"synckit@npm:^0.11.7":
  version: 0.11.8
  resolution: "synckit@npm:0.11.8"
  dependencies:
    "@pkgr/core": ^0.2.4
  checksum: dd7193736e0b5eb209192e280649b98b539537bf23bef20c8a2b24cd12ccde47bcf4e77773ff9cb66c35960d2cb41e28c05e0b19124488abbfb6423258b56275
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"temp-dir@npm:~2.0.0":
  version: 2.0.0
  resolution: "temp-dir@npm:2.0.0"
  checksum: cc4f0404bf8d6ae1a166e0e64f3f409b423f4d1274d8c02814a59a5529f07db6cd070a749664141b992b2c1af337fa9bb451a460a43bb9bcddc49f235d3115aa
  languageName: node
  linkType: hard

"terminal-link@npm:^2.1.1":
  version: 2.1.1
  resolution: "terminal-link@npm:2.1.1"
  dependencies:
    ansi-escapes: ^4.2.1
    supports-hyperlinks: ^2.0.0
  checksum: ce3d2cd3a438c4a9453947aa664581519173ea40e77e2534d08c088ee6dda449eabdbe0a76d2a516b8b73c33262fedd10d5270ccf7576ae316e3db170ce6562f
  languageName: node
  linkType: hard

"terser@npm:^5.15.0":
  version: 5.43.1
  resolution: "terser@npm:5.43.1"
  dependencies:
    "@jridgewell/source-map": ^0.3.3
    acorn: ^8.14.0
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: 1d51747f4540a0842139c2f2617e88d68a26da42d7571cda8955e1bd8febac6e60bc514c258781334e1724aeeccfbd511473eb9d8d831435e4e5fad1ce7f6e8b
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": ^0.1.2
    glob: ^7.1.4
    minimatch: ^3.0.4
  checksum: 3b34a3d77165a2cb82b34014b3aba93b1c4637a5011807557dc2f3da826c59975a5ccad765721c4648b39817e3472789f9b0fa98fc854c5c1c7a1e632aacdc28
  languageName: node
  linkType: hard

"text-extensions@npm:^2.0.0":
  version: 2.4.0
  resolution: "text-extensions@npm:2.4.0"
  checksum: 9bdbc9959e004ccc86a6ec076d6c5bb6765978263e9d0d5febb640d7675c09919ea912f3fe9d50b68c3c7c43cc865610a7cb24954343abb31f74c205fbae4e45
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: ">= 3.1.0 < 4"
  checksum: dba7cc8a23a154cdcb6acb7f51d61511c37a6b077ec5ab5da6e8b874272015937788402fd271fdfc5f187f8cb0948e38d0a42dcc89d554d731652ab458f5343e
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: ^1.0.0
  checksum: 84e1b804bfec49f3531215f17b4a6e50fd4397b5f7c1bccc427b9c656e1ecfb13ea79d899930184f78bc2f57285c54d9a50a590c8868f4f0cef5c1d9f898b05e
  languageName: node
  linkType: hard

"throat@npm:^5.0.0":
  version: 5.0.0
  resolution: "throat@npm:5.0.0"
  checksum: 031ff7f4431618036c1dedd99c8aa82f5c33077320a8358ed829e84b320783781d1869fe58e8f76e948306803de966f5f7573766a437562c9f5c033297ad2fe2
  languageName: node
  linkType: hard

"through@npm:>=2.2.7 <3":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: a38c3e059853c494af95d50c072b83f8b676a9ba2818dcc5b108ef252230735c54e0185437618596c790bbba8fcdaef5b290405981ffa09dce67b1f1bf190cbd
  languageName: node
  linkType: hard

"tiny-invariant@npm:^1.3.3":
  version: 1.3.3
  resolution: "tiny-invariant@npm:1.3.3"
  checksum: 5e185c8cc2266967984ce3b352a4e57cb89dad5a8abb0dea21468a6ecaa67cd5bb47a3b7a85d08041008644af4f667fb8b6575ba38ba5fb00b3b5068306e59fe
  languageName: node
  linkType: hard

"tinyexec@npm:^1.0.0":
  version: 1.0.1
  resolution: "tinyexec@npm:1.0.1"
  checksum: 40f5219abf891884863b085ebe5e8c8bf95bde802f6480f279588b355835ad1604fa01eada2afe90063b48b53cd4b0be5c37393980e23f06fd10689d92fb9586
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: ^6.4.4
    picomatch: ^4.0.2
  checksum: 261e986e3f2062dec3a582303bad2ce31b4634b9348648b46828c000d464b012cf474e38f503312367d4117c3f2f18611992738fca684040758bba44c24de522
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: ~1.0.2
  checksum: 902d7aceb74453ea02abbf58c203f4a8fc1cead89b60b31e354f74ed5b3fb09ea817f94fb310f884a5d16987dd9fa5a735412a7c2dd088dd3d415aa819ae3a28
  languageName: node
  linkType: hard

"tmp@npm:^0.2.3":
  version: 0.2.3
  resolution: "tmp@npm:0.2.3"
  checksum: 73b5c96b6e52da7e104d9d44afb5d106bb1e16d9fa7d00dbeb9e6522e61b571fbdb165c756c62164be9a3bbe192b9b268c236d370a2a0955c7689cd2ae377b95
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: cd922d9b853c00fe414c5a774817be65b058d54a2d01ebb415840960406c669a0fc632f66df885e24cb022ec812739199ccbdb8d1164c3e513f85bfca5ab2873
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"trim-newlines@npm:^4.0.2":
  version: 4.1.1
  resolution: "trim-newlines@npm:4.1.1"
  checksum: 5b09f8e329e8f33c1111ef26906332ba7ba7248cde3e26fc054bb3d69f2858bf5feedca9559c572ff91f33e52977c28e0d41c387df6a02a633cbb8c2d8238627
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.3.0":
  version: 1.4.3
  resolution: "ts-api-utils@npm:1.4.3"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: ea00dee382d19066b2a3d8929f1089888b05fec797e32e7a7004938eda1dccf2e77274ee2afcd4166f53fab9b8d7ee90ebb225a3183f9ba8817d636f688a148d
  languageName: node
  linkType: hard

"ts-interface-checker@npm:^0.1.9":
  version: 0.1.13
  resolution: "ts-interface-checker@npm:0.1.13"
  checksum: 20c29189c2dd6067a8775e07823ddf8d59a33e2ffc47a1bd59a5cb28bb0121a2969a816d5e77eda2ed85b18171aa5d1c4005a6b88ae8499ec7cc49f78571cb5e
  languageName: node
  linkType: hard

"tslib@npm:^1.8.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tslib@npm:^2.0.1, tslib@npm:^2.1.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: e4aba30e632b8c8902b47587fd13345e2827fa639e7c3121074d5ee0880723282411a8838f830b55100cbe4517672f84a2472667d355b81e8af165a55dc6203a
  languageName: node
  linkType: hard

"tsutils@npm:^3.21.0":
  version: 3.21.0
  resolution: "tsutils@npm:3.21.0"
  dependencies:
    tslib: ^1.8.1
  peerDependencies:
    typescript: ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"
  checksum: 1843f4c1b2e0f975e08c4c21caa4af4f7f65a12ac1b81b3b8489366826259323feb3fc7a243123453d2d1a02314205a7634e048d4a8009921da19f99755cdc48
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 62b5628bff67c0eb0b66afa371bd73e230399a8d2ad30d852716efcc4656a7516904570cd8631a49a3ce57c10225adf5d0cbdcb47f6b0255fe6557c453925a15
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: e6b32a3b3877f04339bae01c193b273c62ba7bfc9e325b8703c4ee1b32dc8fe4ef5dfa54bf78265e069f7667d058e360ae0f37be5af9f153b22382cd55a9afe0
  languageName: node
  linkType: hard

"type-fest@npm:^0.7.1":
  version: 0.7.1
  resolution: "type-fest@npm:0.7.1"
  checksum: 5b1b113529d59949d97b76977d545989ddc11b81bb0c766b6d2ccc65473cb4b4a5c7d24f5be2c2bb2de302a5d7a13c1732ea1d34c8c59b7e0ec1f890cf7fc424
  languageName: node
  linkType: hard

"type-fest@npm:^1.0.1, type-fest@npm:^1.2.1, type-fest@npm:^1.2.2":
  version: 1.4.0
  resolution: "type-fest@npm:1.4.0"
  checksum: b011c3388665b097ae6a109a437a04d6f61d81b7357f74cbcb02246f2f5bd72b888ae33631b99871388122ba0a87f4ff1c94078e7119ff22c70e52c0ff828201
  languageName: node
  linkType: hard

"type-fest@npm:^2.5.1":
  version: 2.19.0
  resolution: "type-fest@npm:2.19.0"
  checksum: a4ef07ece297c9fba78fc1bd6d85dff4472fe043ede98bd4710d2615d15776902b595abf62bd78339ed6278f021235fb28a96361f8be86ed754f778973a0d278
  languageName: node
  linkType: hard

"type-fest@npm:^4.18.2, type-fest@npm:^4.21.0, type-fest@npm:^4.39.1, type-fest@npm:^4.6.0":
  version: 4.41.0
  resolution: "type-fest@npm:4.41.0"
  checksum: 7055c0e3eb188425d07403f1d5dc175ca4c4f093556f26871fe22041bc93d137d54bef5851afa320638ca1379106c594f5aa153caa654ac1a7f22c71588a4e80
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-typed-array: ^1.1.14
  checksum: 3fb91f0735fb413b2bbaaca9fabe7b8fc14a3fa5a5a7546bab8a57e755be0e3788d893195ad9c2b842620592de0e68d4c077d4c2c41f04ec25b8b5bb82fa9a80
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.14
  checksum: cda9352178ebeab073ad6499b03e938ebc30c4efaea63a26839d89c4b1da9d2640b0d937fc2bd1f049eb0a38def6fbe8a061b601292ae62fe079a410ce56e3a6
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.15
    reflect.getprototypeof: ^1.0.9
  checksum: 670b7e6bb1d3c2cf6160f27f9f529e60c3f6f9611c67e47ca70ca5cfa24ad95415694c49d1dbfeda016d3372cab7dfc9e38c7b3e1bb8d692cae13a63d3c144d7
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    is-typed-array: ^1.1.13
    possible-typed-array-names: ^1.0.0
    reflect.getprototypeof: ^1.0.6
  checksum: deb1a4ffdb27cd930b02c7030cb3e8e0993084c643208e52696e18ea6dd3953dfc37b939df06ff78170423d353dc8b10d5bae5796f3711c1b3abe52872b3774c
  languageName: node
  linkType: hard

"typedarray@npm:^0.0.6":
  version: 0.0.6
  resolution: "typedarray@npm:0.0.6"
  checksum: 33b39f3d0e8463985eeaeeacc3cb2e28bc3dfaf2a5ed219628c0b629d5d7b810b0eb2165f9f607c34871d5daa92ba1dc69f49051cf7d578b4cbd26c340b9d1b1
  languageName: node
  linkType: hard

"typescript@npm:^5.8.3":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: cb1d081c889a288b962d3c8ae18d337ad6ee88a8e81ae0103fa1fecbe923737f3ba1dbdb3e6d8b776c72bc73bfa6d8d850c0306eed1a51377d2fccdfd75d92c4
  languageName: node
  linkType: hard

"typescript@patch:typescript@^5.8.3#~builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#~builtin<compat/typescript>::version=5.8.3&hash=14eedb"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 1b503525a88ff0ff5952e95870971c4fb2118c17364d60302c21935dedcd6c37e6a0a692f350892bafcef6f4a16d09073fe461158547978d2f16fbe4cb18581c
  languageName: node
  linkType: hard

"ua-parser-js@npm:^1.0.35":
  version: 1.0.40
  resolution: "ua-parser-js@npm:1.0.40"
  bin:
    ua-parser-js: script/cli.js
  checksum: ae555a33dc9395dd877e295d6adbf5634e047aad7c3358328830218f3ca3a6233e35848cd355465a7612f269860e8029984389282940c7a27c9af4dfcdbba8c3
  languageName: node
  linkType: hard

"uglify-js@npm:^3.1.4":
  version: 3.19.3
  resolution: "uglify-js@npm:3.19.3"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: 7ed6272fba562eb6a3149cfd13cda662f115847865c03099e3995a0e7a910eba37b82d4fccf9e88271bb2bcbe505bb374967450f433c17fa27aa36d94a8d0553
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    has-bigints: ^1.0.2
    has-symbols: ^1.1.0
    which-boxed-primitive: ^1.1.1
  checksum: 729f13b84a5bfa3fead1d8139cee5c38514e63a8d6a437819a473e241ba87eeb593646568621c7fc7f133db300ef18d65d1a5a60dc9c7beb9000364d93c581df
  languageName: node
  linkType: hard

"unc-path-regex@npm:^0.1.2":
  version: 0.1.2
  resolution: "unc-path-regex@npm:0.1.2"
  checksum: a05fa2006bf4606051c10fc7968f08ce7b28fa646befafa282813aeb1ac1a56f65cb1b577ca7851af2726198d59475bb49b11776036257b843eaacee2860a4ec
  languageName: node
  linkType: hard

"undici-types@npm:~7.8.0":
  version: 7.8.0
  resolution: "undici-types@npm:7.8.0"
  checksum: 59521a5b9b50e72cb838a29466b3557b4eacbc191a83f4df5a2f7b156bc8263072b145dc4bb8ec41da7d56a7e9b178892458da02af769243d57f801a50ac5751
  languageName: node
  linkType: hard

"undici@npm:^6.18.2":
  version: 6.21.3
  resolution: "undici@npm:6.21.3"
  checksum: a2af0601deece36acbcc11ef722f36ad3c1e035d3065b9fbb36987487f7b69904046fa95c18f228a872ca45441f156fcaacd948fc920b0a97d0c1ab78ea63c04
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 3c3dabdb1d22aef4904399f9e810d0b71c0b12b3815169d96fac97e56d5642840c6071cf709adcace2252bc6bb80242396c2ec74b37224eb015c5f7aca40bad7
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 9e3151e1d0bc6be35c4cef105e317c04090364173e8462005b5cde08a1e7c858b6586486cfebac39dc2c6c8c9ee24afb245de6d527604866edfa454fe2a35fae
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unicorn-magic@npm:^0.1.0":
  version: 0.1.0
  resolution: "unicorn-magic@npm:0.1.0"
  checksum: 48c5882ca3378f380318c0b4eb1d73b7e3c5b728859b060276e0a490051d4180966beeb48962d850fd0c6816543bcdfc28629dcd030bb62a286a2ae2acb5acb6
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"unique-string@npm:~2.0.0":
  version: 2.0.0
  resolution: "unique-string@npm:2.0.0"
  dependencies:
    crypto-random-string: ^2.0.0
  checksum: ef68f639136bcfe040cf7e3cd7a8dff076a665288122855148a6f7134092e6ed33bf83a7f3a9185e46c98dddc445a0da6ac25612afa1a7c38b8b654d6c02498e
  languageName: node
  linkType: hard

"universal-user-agent@npm:^6.0.0":
  version: 6.0.1
  resolution: "universal-user-agent@npm:6.0.1"
  checksum: fdc8e1ae48a05decfc7ded09b62071f571c7fe0bd793d700704c80cea316101d4eac15cc27ed2bb64f4ce166d2684777c3198b9ab16034f547abea0d3aa1c93c
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: ecd8469fe0db28e7de9e5289d32bd1b6ba8f7183db34f3bfc4ca53c49891c2d6aa05f3fb3936a81285a905cc509fb641a0c3fc131ec786167eff41236ae32e60
  languageName: node
  linkType: hard

"unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: ^3.2.0
    picocolors: ^1.1.1
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 7b6d8d08c34af25ee435bccac542bedcb9e57c710f3c42421615631a80aa6dd28b0a81c9d2afbef53799d482fb41453f714b8a7a0a8003e3b4ec8fb1abb819af
  languageName: node
  linkType: hard

"update-notifier@npm:7.3.1":
  version: 7.3.1
  resolution: "update-notifier@npm:7.3.1"
  dependencies:
    boxen: ^8.0.1
    chalk: ^5.3.0
    configstore: ^7.0.0
    is-in-ci: ^1.0.0
    is-installed-globally: ^1.0.0
    is-npm: ^6.0.0
    latest-version: ^9.0.0
    pupa: ^3.1.0
    semver: ^7.6.3
    xdg-basedir: ^5.1.0
  checksum: d639bcbbd432c7e653ef79d684a8b2a4472a1894333647d658b8046143cef613f2608b5314e60327928e4744e64330a03c8a6d1b494543184c0699dd6c61915e
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"url-join@npm:5.0.0":
  version: 5.0.0
  resolution: "url-join@npm:5.0.0"
  checksum: 5921384a8ad4395b49ce4b50aa26efbc429cebe0bc8b3660ad693dd12fd859747b5369be0443e60e53a7850b2bc9d7d0687bcb94386662b40e743596bbf38101
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: c81095493225ecfc28add49c106ca4f09cdf56bc66731aa8dabc2edbbccb1e1bfe2de6a115e5c6a380d3ea166d1636410b62ef216bb07b3feb1cfde1d95d5080
  languageName: node
  linkType: hard

"uuid@npm:^7.0.3":
  version: 7.0.3
  resolution: "uuid@npm:7.0.3"
  bin:
    uuid: dist/bin/uuid
  checksum: f5b7b5cc28accac68d5c083fd51cca64896639ebd4cca88c6cfb363801aaa83aa439c86dfc8446ea250a7a98d17afd2ad9e88d9d4958c79a412eccb93bae29de
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.1":
  version: 9.3.0
  resolution: "v8-to-istanbul@npm:9.3.0"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.12
    "@types/istanbul-lib-coverage": ^2.0.1
    convert-source-map: ^2.0.0
  checksum: ded42cd535d92b7fd09a71c4c67fb067487ef5551cc227bfbf2a1f159a842e4e4acddaef20b955789b8d3b455b9779d036853f4a27ce15007f6364a4d30317ae
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1, validate-npm-package-license@npm:^3.0.4":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: ^3.0.0
    spdx-expression-parse: ^3.0.0
  checksum: 35703ac889d419cf2aceef63daeadbe4e77227c39ab6287eeb6c1b36a746b364f50ba22e88591f5d017bc54685d8137bc2d328d0a896e4d3fd22093c0f32a9ad
  languageName: node
  linkType: hard

"validate-npm-package-name@npm:^5.0.0":
  version: 5.0.1
  resolution: "validate-npm-package-name@npm:5.0.1"
  checksum: 0d583a1af23aeffea7748742cf22b6802458736fb8b60323ba5949763824d46f796474b0e1b9206beb716f9d75269e19dbd7795d6b038b29d561be95dd827381
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"vlq@npm:^1.0.0":
  version: 1.0.1
  resolution: "vlq@npm:1.0.1"
  checksum: 67ab6dd35c787eaa02c0ff1a869dd07a230db08722fb6014adaaf432634808ddb070765f70958b47997e438c331790cfcf20902411b0d6453f1a2a5923522f55
  languageName: node
  linkType: hard

"walker@npm:^1.0.7, walker@npm:^1.0.8":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: 1.0.12
  checksum: ad7a257ea1e662e57ef2e018f97b3c02a7240ad5093c392186ce0bcf1f1a60bbadd520d073b9beb921ed99f64f065efb63dfc8eec689a80e569f93c1c5d5e16c
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: ^1.0.3
  checksum: 814e9d1ddcc9798f7377ffa448a5a3892232b9275ebb30a41b529607691c0491de47cba426e917a4d08ded3ee7e9ba2f3fe32e62ee3cd9c7d3bafb7754bd553c
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"webidl-conversions@npm:^5.0.0":
  version: 5.0.0
  resolution: "webidl-conversions@npm:5.0.0"
  checksum: ccf1ec2ca7c0b5671e5440ace4a66806ae09c49016ab821481bec0c05b1b82695082dc0a27d1fe9d804d475a408ba0c691e6803fd21be608e710955d4589cd69
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^3.0.0":
  version: 3.6.20
  resolution: "whatwg-fetch@npm:3.6.20"
  checksum: c58851ea2c4efe5c2235f13450f426824cf0253c1d45da28f45900290ae602a20aff2ab43346f16ec58917d5562e159cd691efa368354b2e82918c2146a519c5
  languageName: node
  linkType: hard

"whatwg-url-without-unicode@npm:8.0.0-3":
  version: 8.0.0-3
  resolution: "whatwg-url-without-unicode@npm:8.0.0-3"
  dependencies:
    buffer: ^5.4.3
    punycode: ^2.1.1
    webidl-conversions: ^5.0.0
  checksum: 1fe266f7161e0bd961087c1254a5a59d1138c3d402064495eed65e7590d9caed5a1d9acfd6e7a1b0bf0431253b0e637ee3e4ffc08387cd60e0b2ddb9d4687a4b
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"when-exit@npm:^2.1.1":
  version: 2.1.4
  resolution: "when-exit@npm:2.1.4"
  checksum: d77635a0ed43bb63b3b41930637db16fb1e4e8630f5c6efd4aa669322c32b36ba750b7484991f806d3ac56f4e21cdf3925f82fff289b90706cc21e6745038a26
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: ^1.1.0
    is-boolean-object: ^1.2.1
    is-number-object: ^1.1.1
    is-string: ^1.1.1
    is-symbol: ^1.1.1
  checksum: ee41d0260e4fd39551ad77700c7047d3d281ec03d356f5e5c8393fe160ba0db53ef446ff547d05f76ffabfd8ad9df7c9a827e12d4cccdbc8fccf9239ff8ac21e
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    function.prototype.name: ^1.1.6
    has-tostringtag: ^1.0.2
    is-async-function: ^2.0.0
    is-date-object: ^1.1.0
    is-finalizationregistry: ^1.1.0
    is-generator-function: ^1.0.10
    is-regex: ^1.2.1
    is-weakref: ^1.0.2
    isarray: ^2.0.5
    which-boxed-primitive: ^1.1.0
    which-collection: ^1.0.2
    which-typed-array: ^1.1.16
  checksum: 7a3617ba0e7cafb795f74db418df889867d12bce39a477f3ee29c6092aa64d396955bf2a64eae3726d8578440e26777695544057b373c45a8bcf5fbe920bf633
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: ^2.0.3
    is-set: ^2.0.3
    is-weakmap: ^2.0.2
    is-weakset: ^2.0.3
  checksum: c51821a331624c8197916598a738fc5aeb9a857f1e00d89f5e4c03dc7c60b4032822b8ec5696d28268bb83326456a8b8216344fb84270d18ff1d7628051879d9
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    for-each: ^0.3.5
    get-proto: ^1.0.1
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
  checksum: 162d2a07f68ea323f88ed9419861487ce5d02cb876f2cf9dd1e428d04a63133f93a54f89308f337b27cabd312ee3d027cae4a79002b2f0a85b79b9ef4c190670
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"widest-line@npm:^5.0.0":
  version: 5.0.0
  resolution: "widest-line@npm:5.0.0"
  dependencies:
    string-width: ^7.0.0
  checksum: 07f6527b961b88d40ac250596c06fada00cbe049080c6cc8ef4d7bc4f4ab03d7eb1a1c2e5585dd0d8b6ec99ba6f168d5b236edd8ba9221aeb8d914451f0235f9
  languageName: node
  linkType: hard

"wildcard-match@npm:5.1.4":
  version: 5.1.4
  resolution: "wildcard-match@npm:5.1.4"
  checksum: 96e8c13f26b7ae508c694ceb6721640707df55f22045870fbd3b7d8f58529d3616e8e59fb6992524db5e8b323c9fe7c3e92d92b5ae36707529d1f4f170c00e23
  languageName: node
  linkType: hard

"windows-release@npm:^5.0.1":
  version: 5.1.1
  resolution: "windows-release@npm:5.1.1"
  dependencies:
    execa: ^5.1.1
  checksum: 8d15388ccfcbacb96d551f4a692a0a0930a12d2283d140d0a00ea0f6c4f950907cb8055a2cff8650d8bcd5125585338ff0f21a0d7661a30c1d67b6729d13b6b8
  languageName: node
  linkType: hard

"wonka@npm:^6.3.2":
  version: 6.3.5
  resolution: "wonka@npm:6.3.5"
  checksum: bd9f4330664ea971ddbc762275c081d5a635bcebd1c567211d43278b925f3394ad454bb33a0ef5e8beadfaad552cdbc92c018dfb96350f3895341998efa5f521
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 2a44b2788165d0a3de71fd517d4880a8e20ea3a82c080ce46e294f0b68b69a2e49cff5f99c600e275c698a90d12c5ea32aff06c311f0db2eb3f1201f3e7b2a04
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: 6cd96a410161ff617b63581a08376f0cb9162375adeb7956e10c8cd397821f7eb2a6de24eb22a0b28401300bf228c86e50617cd568209b5f6775b93c97d2fe3a
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrap-ansi@npm:^9.0.0":
  version: 9.0.0
  resolution: "wrap-ansi@npm:9.0.0"
  dependencies:
    ansi-styles: ^6.2.1
    string-width: ^7.0.0
    strip-ansi: ^7.1.0
  checksum: b2d43b76b3d8dcbdd64768165e548aad3e54e1cae4ecd31bac9966faaa7cf0b0345677ad6879db10ba58eb446ba8fa44fb82b4951872fd397f096712467a809f
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^4.0.2":
  version: 4.0.2
  resolution: "write-file-atomic@npm:4.0.2"
  dependencies:
    imurmurhash: ^0.1.4
    signal-exit: ^3.0.7
  checksum: 5da60bd4eeeb935eec97ead3df6e28e5917a6bd317478e4a85a5285e8480b8ed96032bbcc6ecd07b236142a24f3ca871c924ec4a6575e623ec1b11bf8c1c253c
  languageName: node
  linkType: hard

"write-file-atomic@npm:^5.0.1":
  version: 5.0.1
  resolution: "write-file-atomic@npm:5.0.1"
  dependencies:
    imurmurhash: ^0.1.4
    signal-exit: ^4.0.1
  checksum: 8dbb0e2512c2f72ccc20ccedab9986c7d02d04039ed6e8780c987dc4940b793339c50172a1008eed7747001bfacc0ca47562668a069a7506c46c77d7ba3926a9
  languageName: node
  linkType: hard

"ws@npm:^6.2.3":
  version: 6.2.3
  resolution: "ws@npm:6.2.3"
  dependencies:
    async-limiter: ~1.0.0
  checksum: bbc96ff5628832d80669a88fd117487bf070492dfaa50df77fa442a2b119792e772f4365521e0a8e025c0d51173c54fa91adab165c11b8e0674685fdd36844a5
  languageName: node
  linkType: hard

"ws@npm:^7, ws@npm:^7.5.10":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: f9bb062abf54cc8f02d94ca86dcd349c3945d63851f5d07a3a61c2fcb755b15a88e943a63cf580cbdb5b74436d67ef6b67f745b8f7c0814e411379138e1863cb
  languageName: node
  linkType: hard

"ws@npm:^8.12.1":
  version: 8.18.3
  resolution: "ws@npm:8.18.3"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: d64ef1631227bd0c5fe21b3eb3646c9c91229402fb963d12d87b49af0a1ef757277083af23a5f85742bae1e520feddfb434cb882ea59249b15673c16dc3f36e0
  languageName: node
  linkType: hard

"xcode@npm:^3.0.1":
  version: 3.0.1
  resolution: "xcode@npm:3.0.1"
  dependencies:
    simple-plist: ^1.1.0
    uuid: ^7.0.3
  checksum: 908ff85851f81aec6e36ca24427db092e1cc068f052716e14de5e762196858039efabbe053a1abe8920184622501049e74a93618e8692b982f7604a9847db108
  languageName: node
  linkType: hard

"xdg-basedir@npm:^5.1.0":
  version: 5.1.0
  resolution: "xdg-basedir@npm:5.1.0"
  checksum: b60e8a2c663ccb1dac77c2d913f3b96de48dafbfa083657171d3d50e10820b8a04bb4edfe9f00808c8c20e5f5355e1927bea9029f03136e29265cb98291e1fea
  languageName: node
  linkType: hard

"xml2js@npm:0.6.0":
  version: 0.6.0
  resolution: "xml2js@npm:0.6.0"
  dependencies:
    sax: ">=0.6.0"
    xmlbuilder: ~11.0.0
  checksum: 437f353fd66d367bf158e9555a0625df9965d944e499728a5c6bc92a54a2763179b144f14b7e1c725040f56bbd22b0fa6cfcb09ec4faf39c45ce01efe631f40b
  languageName: node
  linkType: hard

"xmlbuilder@npm:^15.1.1":
  version: 15.1.1
  resolution: "xmlbuilder@npm:15.1.1"
  checksum: 14f7302402e28d1f32823583d121594a9dca36408d40320b33f598bd589ca5163a352d076489c9c64d2dc1da19a790926a07bf4191275330d4de2b0d85bb1843
  languageName: node
  linkType: hard

"xmlbuilder@npm:~11.0.0":
  version: 11.0.1
  resolution: "xmlbuilder@npm:11.0.1"
  checksum: 7152695e16f1a9976658215abab27e55d08b1b97bca901d58b048d2b6e106b5af31efccbdecf9b07af37c8377d8e7e821b494af10b3a68b0ff4ae60331b415b0
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard

"yargs-parser@npm:21.1.1, yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: ed2d96a616a9e3e1cc7d204c62ecc61f7aaab633dcbfab2c6df50f7f87b393993fe6640d017759fe112d0cb1e0119f2b4150a87305cc873fd90831c6a58ccf1c
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.9":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 8bb69015f2b0ff9e17b2c8e6bfe224ab463dd00ca211eece72a4cd8a906224d2703fb8a326d36fdd0e68701e201b2a60ed7cf81ce0fd9b3799f9fe7745977ae3
  languageName: node
  linkType: hard

"yargs@npm:^17.0.0, yargs@npm:^17.3.1, yargs@npm:^17.5.1, yargs@npm:^17.6.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: 73b572e863aa4a8cbef323dd911d79d193b772defd5a51aab0aca2d446655216f5002c42c5306033968193bdbf892a7a4c110b0d77954a7fdf563e653967b56a
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"yocto-queue@npm:^1.0.0":
  version: 1.2.1
  resolution: "yocto-queue@npm:1.2.1"
  checksum: 0843d6c2c0558e5c06e98edf9c17942f25c769e21b519303a5c2adefd5b738c9b2054204dc856ac0cd9d134b1bc27d928ce84fd23c9e2423b7e013d5a6f50577
  languageName: node
  linkType: hard

"yoctocolors-cjs@npm:^2.1.1":
  version: 2.1.2
  resolution: "yoctocolors-cjs@npm:2.1.2"
  checksum: 1c474d4b30a8c130e679279c5c2c33a0d48eba9684ffa0252cc64846c121fb56c3f25457fef902edbe1e2d7a7872130073a9fc8e795299d75e13fa3f5f548f1b
  languageName: node
  linkType: hard
