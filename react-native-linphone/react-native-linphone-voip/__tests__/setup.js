/**
 * Test Setup for React Native Linphone VoIP
 */

// Mock React Native modules
jest.mock('react-native', () => ({
  NativeModules: {
    LinphoneModule: {
      register: jest.fn(),
      unregister: jest.fn(),
      call: jest.fn(),
      hangup: jest.fn(),
      sendMessage: jest.fn(),
      acceptCall: jest.fn(),
      declineCall: jest.fn(),
      toggleMute: jest.fn(),
      toggleSpeaker: jest.fn(),
      sendDTMF: jest.fn(),
      getCallHistory: jest.fn(),
      clearCallHistory: jest.fn(),
      addContact: jest.fn(),
      removeContact: jest.fn(),
      getContacts: jest.fn(),
    },
  },
  NativeEventEmitter: jest.fn(() => ({
    addListener: jest.fn(() => ({
      remove: jest.fn(),
    })),
    removeListener: jest.fn(),
    removeAllListeners: jest.fn(),
  })),
  Platform: {
    OS: 'ios',
    select: jest.fn((obj) => obj.ios),
  },
}));

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test configuration
global.TEST_CONFIG = {
  SIP_SERVER: {
    domain: '**************:5060',
    username: '1000',
    password: 'hoangcv',
    transport: 'UDP'
  },
  TEST_TARGETS: {
    CALL_URI: 'sip:1001@**************:5060',
    MESSAGE_URI: 'sip:1001@**************:5060',
    CONFERENCE_URI: 'sip:conference@**************:5060'
  },
  TIMEOUTS: {
    REGISTRATION: 10000,
    CALL_SETUP: 15000,
    MESSAGE_SEND: 5000,
    NETWORK_TIMEOUT: 30000
  }
};

// Helper functions for tests
global.TestHelpers = {
  // Wait for event with timeout
  waitForEvent: (eventEmitter, eventName, timeout = 5000) => {
    return new Promise((resolve, reject) => {
      const listener = eventEmitter.addListener(eventName, (event) => {
        listener.remove();
        resolve(event);
      });
      
      setTimeout(() => {
        listener.remove();
        reject(new Error(`Timeout waiting for event: ${eventName}`));
      }, timeout);
    });
  },
  
  // Create mock SIP URI
  createSipUri: (username, domain = '**************:5060') => {
    return `sip:${username}@${domain}`;
  },
  
  // Validate SIP configuration
  validateSipConfig: (config) => {
    const required = ['domain', 'username', 'password'];
    for (const field of required) {
      if (!config[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    return true;
  },
  
  // Generate test call ID
  generateCallId: () => {
    return `test-call-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  },
  
  // Create test message
  createTestMessage: (content = 'Test message') => {
    return {
      content,
      timestamp: Date.now(),
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
  }
};

// Setup test environment
beforeEach(() => {
  jest.clearAllMocks();

  // Setup default mock return values
  const { NativeModules } = require('react-native');

  NativeModules.LinphoneModule.register.mockResolvedValue({
    status: 'success',
    message: 'Mock: Registration initiated successfully'
  });

  NativeModules.LinphoneModule.call.mockResolvedValue({
    status: 'success',
    message: 'Mock: Call initiated successfully',
    uri: 'sip:1001@**************:5060'
  });

  NativeModules.LinphoneModule.hangup.mockResolvedValue({
    status: 'success',
    message: 'Mock: Call terminated successfully'
  });

  NativeModules.LinphoneModule.sendMessage.mockResolvedValue({
    status: 'success',
    message: 'Mock: Message sent successfully'
  });

  NativeModules.LinphoneModule.unregister.mockResolvedValue({
    status: 'success',
    message: 'Mock: Unregistered successfully'
  });
});

afterEach(() => {
  jest.restoreAllMocks();
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for integration tests
jest.setTimeout(30000);
