/**
 * Unit Tests for React Native Linphone VoIP Module
 * Test SIP functionality with real server parameters
 */

import { NativeModules, NativeEventEmitter } from 'react-native';

// Mock React Native modules
jest.mock('react-native', () => ({
  NativeModules: {
    LinphoneModule: {
      register: jest.fn(),
      call: jest.fn(),
      hangup: jest.fn(),
      sendMessage: jest.fn(),
      unregister: jest.fn(),
    },
  },
  NativeEventEmitter: jest.fn(() => ({
    addListener: jest.fn(),
    removeListener: jest.fn(),
    removeAllListeners: jest.fn(),
  })),
}));

const { LinphoneModule } = NativeModules;

// Test configuration with real SIP server
const TEST_CONFIG = {
  domain: '**************:5060',
  username: '1000',
  password: 'hoangcv',
  transport: 'UDP'
};

const TEST_CALL_URI = 'sip:1001@**************:5060';

describe('LinphoneModule', () => {
  let eventEmitter;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup event emitter mock
    eventEmitter = new NativeEventEmitter();
  });

  describe('SIP Registration', () => {
    test('should register with valid SIP credentials', async () => {
      // Mock successful registration
      LinphoneModule.register.mockResolvedValue({
        status: 'success',
        message: 'Registration initiated'
      });

      const result = await LinphoneModule.register(TEST_CONFIG);

      expect(LinphoneModule.register).toHaveBeenCalledWith(TEST_CONFIG);
      expect(result.status).toBe('success');
      expect(result.message).toContain('Registration');
    });

    test('should handle registration with missing credentials', async () => {
      const invalidConfig = {
        domain: TEST_CONFIG.domain,
        username: TEST_CONFIG.username,
        // Missing password
      };

      LinphoneModule.register.mockRejectedValue({
        code: 'INVALID_CONFIG',
        message: 'Missing required configuration'
      });

      await expect(LinphoneModule.register(invalidConfig))
        .rejects
        .toMatchObject({
          code: 'INVALID_CONFIG'
        });
    });

    test('should handle registration failure', async () => {
      LinphoneModule.register.mockRejectedValue({
        code: 'REGISTRATION_ERROR',
        message: 'Network timeout'
      });

      await expect(LinphoneModule.register(TEST_CONFIG))
        .rejects
        .toMatchObject({
          code: 'REGISTRATION_ERROR'
        });
    });

    test('should unregister successfully', async () => {
      LinphoneModule.unregister.mockResolvedValue({
        status: 'success',
        message: 'Unregistered successfully'
      });

      const result = await LinphoneModule.unregister();

      expect(LinphoneModule.unregister).toHaveBeenCalled();
      expect(result.status).toBe('success');
    });
  });

  describe('SIP Calling', () => {
    test('should initiate call with valid URI', async () => {
      LinphoneModule.call.mockResolvedValue({
        status: 'success',
        message: 'Call initiated',
        uri: TEST_CALL_URI
      });

      const result = await LinphoneModule.call(TEST_CALL_URI);

      expect(LinphoneModule.call).toHaveBeenCalledWith(TEST_CALL_URI);
      expect(result.status).toBe('success');
      expect(result.uri).toBe(TEST_CALL_URI);
    });

    test('should handle call with invalid URI', async () => {
      const invalidUri = 'invalid-uri';

      LinphoneModule.call.mockRejectedValue({
        code: 'CALL_ERROR',
        message: 'Invalid URI format'
      });

      await expect(LinphoneModule.call(invalidUri))
        .rejects
        .toMatchObject({
          code: 'CALL_ERROR'
        });
    });

    test('should hangup call successfully', async () => {
      LinphoneModule.hangup.mockResolvedValue({
        status: 'success',
        message: 'Call terminated'
      });

      const result = await LinphoneModule.hangup();

      expect(LinphoneModule.hangup).toHaveBeenCalled();
      expect(result.status).toBe('success');
    });
  });

  describe('SIP Messaging', () => {
    test('should send message successfully', async () => {
      const messageData = {
        to: TEST_CALL_URI,
        content: 'Hello from unit test'
      };

      LinphoneModule.sendMessage.mockResolvedValue({
        status: 'success',
        message: 'Message sent'
      });

      const result = await LinphoneModule.sendMessage(messageData);

      expect(LinphoneModule.sendMessage).toHaveBeenCalledWith(messageData);
      expect(result.status).toBe('success');
    });

    test('should handle message sending failure', async () => {
      const messageData = {
        to: 'invalid-uri',
        content: 'Test message'
      };

      LinphoneModule.sendMessage.mockRejectedValue({
        code: 'MESSAGE_ERROR',
        message: 'Failed to send message'
      });

      await expect(LinphoneModule.sendMessage(messageData))
        .rejects
        .toMatchObject({
          code: 'MESSAGE_ERROR'
        });
    });
  });

  describe('Event Handling', () => {
    test('should handle registration state changed event', () => {
      const mockListener = jest.fn();
      eventEmitter.addListener.mockReturnValue({ remove: jest.fn() });

      eventEmitter.addListener('RegistrationStateChanged', mockListener);

      // Simulate event
      const eventData = {
        state: 'Ok',
        message: 'Registration successful'
      };

      expect(eventEmitter.addListener).toHaveBeenCalledWith(
        'RegistrationStateChanged',
        mockListener
      );
    });

    test('should handle call state changed event', () => {
      const mockListener = jest.fn();
      eventEmitter.addListener.mockReturnValue({ remove: jest.fn() });

      eventEmitter.addListener('CallStateChanged', mockListener);

      // Simulate event
      const eventData = {
        callId: 'test-call-123',
        state: 'Connected',
        message: 'Call connected',
        remoteAddress: TEST_CALL_URI
      };

      expect(eventEmitter.addListener).toHaveBeenCalledWith(
        'CallStateChanged',
        mockListener
      );
    });

    test('should handle message received event', () => {
      const mockListener = jest.fn();
      eventEmitter.addListener.mockReturnValue({ remove: jest.fn() });

      eventEmitter.addListener('MessageReceived', mockListener);

      expect(eventEmitter.addListener).toHaveBeenCalledWith(
        'MessageReceived',
        mockListener
      );
    });
  });

  describe('Configuration Validation', () => {
    test('should validate SIP domain format', () => {
      const validConfigs = [
        { ...TEST_CONFIG, domain: '**************:5060' },
        { ...TEST_CONFIG, domain: 'sip.example.com' },
      ];

      const invalidConfigs = [
        { ...TEST_CONFIG, domain: '' },
        { ...TEST_CONFIG, domain: null },
      ];

      validConfigs.forEach((config) => {
        expect(() => validateConfig(config)).not.toThrow();
      });

      invalidConfigs.forEach((config) => {
        expect(() => validateConfig(config)).toThrow();
      });
    });

    test('should validate username format', () => {
      const configs = [
        { ...TEST_CONFIG, username: '1000' }, // Valid
        { ...TEST_CONFIG, username: 'user123' }, // Valid
        { ...TEST_CONFIG, username: '' }, // Invalid
        { ...TEST_CONFIG, username: null }, // Invalid
      ];

      configs.forEach((config, index) => {
        if (index < 2) {
          expect(() => validateConfig(config)).not.toThrow();
        } else {
          expect(() => validateConfig(config)).toThrow();
        }
      });
    });
  });
});

// Helper function for config validation
function validateConfig(config) {
  if (!config.domain || typeof config.domain !== 'string') {
    throw new Error('Invalid domain');
  }
  if (!config.username || typeof config.username !== 'string') {
    throw new Error('Invalid username');
  }
  if (!config.password || typeof config.password !== 'string') {
    throw new Error('Invalid password');
  }
  return true;
}
