{"name": "react-native-linphone-voip", "version": "0.1.0", "description": "React Native wrapper for Linphone SDK - Full featured SIP client library", "main": "./src/index.tsx", "types": "./src/index.tsx", "exports": {".": {"source": "./src/index.tsx", "types": "./src/index.tsx", "default": "./src/index.tsx"}, "./package.json": "./package.json"}, "files": ["src", "lib", "android", "ios", "cpp", "*.podsp<PERSON>", "react-native.config.js", "!ios/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!**/.*"], "scripts": {"example": "yarn workspace react-native-linphone-voip-example", "test": "jest", "test:unit": "jest --testPathPattern=__tests__/LinphoneModule.test.js", "test:integration": "jest --testPathPattern=__tests__/LinphoneIntegration.test.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:sip": "jest --testPathPattern=Integration --verbose", "typecheck": "tsc", "lint": "eslint \"**/*.{js,ts,tsx}\"", "clean": "del-cli lib", "prepare": "bob build", "release": "release-it --only-version"}, "keywords": ["react-native", "ios", "android"], "repository": {"type": "git", "url": "git+https://github.com/buiduc/react-native-linphone-voip.git"}, "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/buiduc)", "license": "MIT", "bugs": {"url": "https://github.com/buiduc/react-native-linphone-voip/issues"}, "homepage": "https://github.com/buiduc/react-native-linphone-voip#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@commitlint/config-conventional": "^19.6.0", "@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@evilmartians/lefthook": "^1.5.0", "@react-native/babel-preset": "0.78.2", "@react-native/eslint-config": "^0.78.0", "@release-it/conventional-changelog": "^9.0.2", "@types/jest": "^29.5.5", "@types/react": "^19.0.12", "commitlint": "^19.6.1", "del-cli": "^5.1.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "jest": "^29.7.0", "prettier": "^3.0.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-builder-bob": "^0.40.8", "release-it": "^17.10.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": "*", "react-native": "*"}, "workspaces": ["example"], "packageManager": "yarn@3.6.1", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "angular"}}}}, "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "create-react-native-library": {"languages": "js", "type": "library", "version": "0.51.1"}}