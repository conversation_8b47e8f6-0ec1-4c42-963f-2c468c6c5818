# react-native-linphone-voip

React Native wrapper for Linphone SDK - Full featured SIP client library

## Installation

```sh
npm install react-native-linphone-voip
```

## Usage


```js
import { multiply } from 'react-native-linphone-voip';

// ...

const result = await multiply(3, 7);
```


## Contributing

See the [contributing guide](CONTRIBUTING.md) to learn how to contribute to the repository and the development workflow.

## License

MIT

---

Made with [create-react-native-library](https://github.com/callstack/react-native-builder-bob)
