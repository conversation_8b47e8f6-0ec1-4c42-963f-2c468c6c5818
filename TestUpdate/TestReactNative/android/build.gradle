// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.22"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

// allprojects {
//     repositories {
//         maven {
//             // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
//             url("$rootDir/../node_modules/react-native/android")
//         }
//         maven {
//             // Android JSC is installed from npm
//             url("$rootDir/../node_modules/jsc-android/dist")
//         }
//         mavenCentral {
//             // We don't want to fetch react-native from Maven Central as there are
//             // older versions over there.
//             content {
//                 excludeGroup "com.facebook.react"
//             }
//         }
//         google()
//         maven { url 'https://www.jitpack.io' }
//     }
// }
