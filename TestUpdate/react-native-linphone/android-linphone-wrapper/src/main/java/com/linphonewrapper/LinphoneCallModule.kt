package com.linphonewrapper

import android.content.Context

class LinphoneCallModule(private val context: Context) {
    companion object {
        @Volatile
        private var INSTANCE: LinphoneCallModule? = null
        
        fun getInstance(context: Context): LinphoneCallModule {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LinphoneCallModule(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val callManager = CallManager(context)
    private val eventCallbacks = mutableMapOf<String, (String, String?) -> Unit>()
    
    init {
        callManager.delegate = object : CallManagerDelegate {
            override fun onCallStateChanged(state: CallState, message: String?) {
                emitEvent("callStateChanged", state.value, message)
            }
            
            override fun onRegistrationStateChanged(isRegistered: Boolean, message: String?) {
                emitEvent("registrationStateChanged", if (isRegistered) "registered" else "unregistered", message)
            }
            
            override fun onIncomingCall(from: String) {
                emitEvent("incomingCall", from)
            }
        }
    }
    
    // MARK: - Public API for React Native
    
    @Throws(Exception::class)
    fun initialize() {
        callManager.initialize()
    }
    
    @Throws(Exception::class)
    fun register(username: String, password: String, domain: String) {
        callManager.register(username, password, domain)
    }
    
    @Throws(Exception::class)
    fun startCall(to: String) {
        callManager.startCall(to)
    }
    
    fun acceptCall() {
        callManager.acceptCall()
    }
    
    fun declineCall() {
        callManager.declineCall()
    }
    
    fun hangUp() {
        callManager.hangUp()
    }
    
    fun toggleMute() {
        callManager.toggleMute()
    }
    
    fun toggleSpeaker() {
        callManager.toggleSpeaker()
    }
    
    fun destroy() {
        callManager.destroy()
        eventCallbacks.clear()
    }
    
    // MARK: - Event Handling
    
    fun addEventCallback(eventType: String, callback: (String, String?) -> Unit) {
        eventCallbacks[eventType] = callback
    }
    
    fun removeEventCallback(eventType: String) {
        eventCallbacks.remove(eventType)
    }
    
    private fun emitEvent(type: String, data: String, message: String? = null) {
        eventCallbacks[type]?.invoke(data, message)
    }
}
