package com.linphonewrapper

import android.content.Context
import android.media.AudioManager
import kotlinx.coroutines.*
import org.linphone.core.*

enum class CallState(val value: String) {
    IDLE("idle"),
    OUTGOING_INIT("outgoing_init"),
    OUTGOING_PROGRESS("outgoing_progress"),
    OUTGOING_RINGING("outgoing_ringing"),
    OUTGOING_EARLY_MEDIA("outgoing_early_media"),
    CONNECTED("connected"),
    STREAMS_RUNNING("streams_running"),
    PAUSING("pausing"),
    PAUSED("paused"),
    RESUMING("resuming"),
    REFERRED("referred"),
    ERROR("error"),
    END("end"),
    PAUSED_BY_REMOTE("paused_by_remote"),
    UPDATED_BY_REMOTE("updated_by_remote"),
    INCOMING_RECEIVED("incoming_received"),
    INCOMING_EARLY_MEDIA("incoming_early_media"),
    UPDATING("updating"),
    RELEASED("released"),
    EARLY_UPDATED_BY_REMOTE("early_updated_by_remote"),
    EARLY_UPDATING("early_updating")
}

interface CallManagerDelegate {
    fun onCallStateChanged(state: CallState, message: String?)
    fun onRegistrationStateChanged(isRegistered: Boolean, message: String?)
    fun onIncomingCall(from: String)
}

class CallManager(private val context: Context) {
    var delegate: CallManagerDelegate? = null
    
    private var core: Core? = null
    private var currentCall: Call? = null
    private var isInitialized = false
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    private val coreListener = object : CoreListenerStub() {
        override fun onCallStateChanged(
            core: Core,
            call: Call,
            state: Call.State,
            message: String
        ) {
            currentCall = call
            val callState = mapCallState(state)
            delegate?.onCallStateChanged(callState, message)
            
            if (state == Call.State.IncomingReceived) {
                val remoteAddress = call.remoteAddress?.asStringUriOnly() ?: "Unknown"
                delegate?.onIncomingCall(remoteAddress)
            }
        }
        
        override fun onRegistrationStateChanged(
            core: Core,
            cfg: ProxyConfig,
            state: RegistrationState,
            message: String
        ) {
            val isRegistered = state == RegistrationState.Ok
            delegate?.onRegistrationStateChanged(isRegistered, message)
        }
    }
    
    @Throws(Exception::class)
    fun initialize() {
        if (isInitialized) return
        
        val factory = Factory.instance()
        factory.setDebugMode(true, "LinphoneWrapper")
        
        core = factory.createCore(null, null, context).apply {
            addListener(coreListener)
            
            // Configure transports
            transports.udpPort = 5060
            transports.tcpPort = 5060
            transports.tlsPort = 5061
            
            // Start core
            start()
        }
        
        isInitialized = true
    }
    
    @Throws(Exception::class)
    fun register(username: String, password: String, domain: String) {
        val core = this.core ?: throw Exception("Core not initialized")
        
        // Create auth info
        val authInfo = Factory.instance().createAuthInfo(username, null, password, null, null, domain, null)
        core.addAuthInfo(authInfo)
        
        // Create proxy config
        val proxyConfig = core.createProxyConfig()
        val identity = "sip:$username@$domain"
        val identityAddress = Factory.instance().createAddress(identity)
        
        proxyConfig.identityAddress = identityAddress
        
        val serverAddr = "sip:$domain"
        val serverAddress = Factory.instance().createAddress(serverAddr)
        proxyConfig.serverAddr = serverAddress?.asStringUriOnly()
        
        proxyConfig.registerEnabled = true
        core.addProxyConfig(proxyConfig)
        core.defaultProxyConfig = proxyConfig
    }
    
    @Throws(Exception::class)
    fun startCall(to: String) {
        val core = this.core ?: throw Exception("Core not initialized")
        
        val remoteAddress = Factory.instance().createAddress(to)
        val params = core.createCallParams(null)
        
        currentCall = core.inviteAddressWithParams(remoteAddress, params)
    }
    
    fun acceptCall() {
        currentCall?.let { call ->
            try {
                call.accept()
            } catch (e: Exception) {
                delegate?.onCallStateChanged(CallState.ERROR, "Failed to accept call: ${e.message}")
            }
        }
    }
    
    fun declineCall() {
        currentCall?.let { call ->
            try {
                call.decline(Reason.Declined)
            } catch (e: Exception) {
                delegate?.onCallStateChanged(CallState.ERROR, "Failed to decline call: ${e.message}")
            }
        }
    }
    
    fun hangUp() {
        currentCall?.let { call ->
            try {
                call.terminate()
            } catch (e: Exception) {
                delegate?.onCallStateChanged(CallState.ERROR, "Failed to hang up: ${e.message}")
            }
        }
    }
    
    fun toggleMute() {
        currentCall?.let { call ->
            call.microphoneMuted = !call.microphoneMuted
        }
    }
    
    fun toggleSpeaker() {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        audioManager.isSpeakerphoneOn = !audioManager.isSpeakerphoneOn
    }
    
    fun destroy() {
        scope.cancel()
        core?.removeListener(coreListener)
        core?.stop()
        core = null
        isInitialized = false
    }
    
    private fun mapCallState(state: Call.State): CallState {
        return when (state) {
            Call.State.Idle -> CallState.IDLE
            Call.State.OutgoingInit -> CallState.OUTGOING_INIT
            Call.State.OutgoingProgress -> CallState.OUTGOING_PROGRESS
            Call.State.OutgoingRinging -> CallState.OUTGOING_RINGING
            Call.State.OutgoingEarlyMedia -> CallState.OUTGOING_EARLY_MEDIA
            Call.State.Connected -> CallState.CONNECTED
            Call.State.StreamsRunning -> CallState.STREAMS_RUNNING
            Call.State.Pausing -> CallState.PAUSING
            Call.State.Paused -> CallState.PAUSED
            Call.State.Resuming -> CallState.RESUMING
            Call.State.Referred -> CallState.REFERRED
            Call.State.Error -> CallState.ERROR
            Call.State.End -> CallState.END
            Call.State.PausedByRemote -> CallState.PAUSED_BY_REMOTE
            Call.State.UpdatedByRemote -> CallState.UPDATED_BY_REMOTE
            Call.State.IncomingReceived -> CallState.INCOMING_RECEIVED
            Call.State.IncomingEarlyMedia -> CallState.INCOMING_EARLY_MEDIA
            Call.State.Updating -> CallState.UPDATING
            Call.State.Released -> CallState.RELEASED
            Call.State.EarlyUpdatedByRemote -> CallState.EARLY_UPDATED_BY_REMOTE
            Call.State.EarlyUpdating -> CallState.EARLY_UPDATING
        }
    }
}
