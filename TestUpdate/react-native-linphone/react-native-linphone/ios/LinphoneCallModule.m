#import "LinphoneCallModule.h"
#import <React/RCTLog.h>

@interface LinphoneCallModule()
@property (nonatomic, strong) id linphoneWrapper;
@end

@implementation LinphoneCallModule

RCT_EXPORT_MODULE()

+ (BOOL)requiresMainQueueSetup {
    return YES;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // Import LinphoneWrapper Swift module
        Class LinphoneCallModuleClass = NSClassFromString(@"LinphoneWrapper.LinphoneCallModule");
        if (LinphoneCallModuleClass) {
            self.linphoneWrapper = [[LinphoneCallModuleClass alloc] init];
            [self setupEventCallbacks];
        } else {
            RCTLogError(@"LinphoneWrapper.LinphoneCallModule class not found");
        }
    }
    return self;
}

- (void)setupEventCallbacks {
    if (!self.linphoneWrapper) return;
    
    __weak typeof(self) weakSelf = self;
    
    // Call state changed callback
    [self.linphoneWrapper addEventCallback:@"callStateChanged" callback:^(NSString *data, NSString *message) {
        [weakSelf sendEventWithName:@"callStateChanged" body:@{
            @"state": data,
            @"message": message ?: [NSNull null]
        }];
    }];
    
    // Registration state changed callback
    [self.linphoneWrapper addEventCallback:@"registrationStateChanged" callback:^(NSString *data, NSString *message) {
        [weakSelf sendEventWithName:@"registrationStateChanged" body:@{
            @"state": data,
            @"message": message ?: [NSNull null]
        }];
    }];
    
    // Incoming call callback
    [self.linphoneWrapper addEventCallback:@"incomingCall" callback:^(NSString *data, NSString *message) {
        [weakSelf sendEventWithName:@"incomingCall" body:@{
            @"from": data
        }];
    }];
}

- (NSArray<NSString *> *)supportedEvents {
    return @[@"callStateChanged", @"registrationStateChanged", @"incomingCall"];
}

// MARK: - Exported Methods

RCT_EXPORT_METHOD(initialize:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    if (!self.linphoneWrapper) {
        reject(@"LINPHONE_ERROR", @"LinphoneWrapper not initialized", nil);
        return;
    }
    
    @try {
        [self.linphoneWrapper initialize];
        resolve(nil);
    } @catch (NSException *exception) {
        reject(@"LINPHONE_ERROR", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(register:(NSString *)username
                  password:(NSString *)password
                  domain:(NSString *)domain
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    if (!self.linphoneWrapper) {
        reject(@"LINPHONE_ERROR", @"LinphoneWrapper not initialized", nil);
        return;
    }
    
    @try {
        [self.linphoneWrapper registerWithUsername:username password:password domain:domain];
        resolve(nil);
    } @catch (NSException *exception) {
        reject(@"LINPHONE_ERROR", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(startCall:(NSString *)to
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    if (!self.linphoneWrapper) {
        reject(@"LINPHONE_ERROR", @"LinphoneWrapper not initialized", nil);
        return;
    }
    
    @try {
        [self.linphoneWrapper startCallTo:to];
        resolve(nil);
    } @catch (NSException *exception) {
        reject(@"LINPHONE_ERROR", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(acceptCall:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    if (!self.linphoneWrapper) {
        reject(@"LINPHONE_ERROR", @"LinphoneWrapper not initialized", nil);
        return;
    }
    
    [self.linphoneWrapper acceptCall];
    resolve(nil);
}

RCT_EXPORT_METHOD(declineCall:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    if (!self.linphoneWrapper) {
        reject(@"LINPHONE_ERROR", @"LinphoneWrapper not initialized", nil);
        return;
    }
    
    [self.linphoneWrapper declineCall];
    resolve(nil);
}

RCT_EXPORT_METHOD(hangUp:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    if (!self.linphoneWrapper) {
        reject(@"LINPHONE_ERROR", @"LinphoneWrapper not initialized", nil);
        return;
    }
    
    [self.linphoneWrapper hangUp];
    resolve(nil);
}

RCT_EXPORT_METHOD(toggleMute:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    if (!self.linphoneWrapper) {
        reject(@"LINPHONE_ERROR", @"LinphoneWrapper not initialized", nil);
        return;
    }
    
    [self.linphoneWrapper toggleMute];
    resolve(nil);
}

RCT_EXPORT_METHOD(toggleSpeaker:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    if (!self.linphoneWrapper) {
        reject(@"LINPHONE_ERROR", @"LinphoneWrapper not initialized", nil);
        return;
    }
    
    [self.linphoneWrapper toggleSpeaker];
    resolve(nil);
}

@end
