export enum CallState {
  IDLE = 'idle',
  OUTGOING_INIT = 'outgoing_init',
  OUTGOING_PROGRESS = 'outgoing_progress',
  OUTGOING_RINGING = 'outgoing_ringing',
  OUTGOING_EARLY_MEDIA = 'outgoing_early_media',
  CONNECTED = 'connected',
  STREAMS_RUNNING = 'streams_running',
  PAUSING = 'pausing',
  PAUSED = 'paused',
  RESUMING = 'resuming',
  REFERRED = 'referred',
  ERROR = 'error',
  END = 'end',
  PAUSED_BY_REMOTE = 'paused_by_remote',
  UPDATED_BY_REMOTE = 'updated_by_remote',
  INCOMING_RECEIVED = 'incoming_received',
  INCOMING_EARLY_MEDIA = 'incoming_early_media',
  UPDATING = 'updating',
  RELEASED = 'released',
  EARLY_UPDATED_BY_REMOTE = 'early_updated_by_remote',
  EARLY_UPDATING = 'early_updating',
}

export enum RegistrationState {
  REGISTERED = 'registered',
  UNREGISTERED = 'unregistered',
}

export interface SipConfig {
  username: string;
  password: string;
  domain: string;
}

export interface CallEvent {
  state: CallState;
  message?: string;
}

export interface RegistrationEvent {
  state: RegistrationState;
  message?: string;
}

export interface IncomingCallEvent {
  from: string;
}

export type CallStateCallback = (event: CallEvent) => void;
export type RegistrationStateCallback = (event: RegistrationEvent) => void;
export type IncomingCallCallback = (event: IncomingCallEvent) => void;
