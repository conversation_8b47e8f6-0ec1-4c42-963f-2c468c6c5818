import { NativeModules, NativeEventEmitter, Platform } from 'react-native';
import {
  CallState,
  RegistrationState,
  SipConfig,
  CallEvent,
  RegistrationEvent,
  IncomingCallEvent,
  CallStateCallback,
  RegistrationStateCallback,
  IncomingCallCallback,
} from './types';

const { LinphoneCallModule } = NativeModules;

if (!LinphoneCallModule) {
  throw new Error('LinphoneCallModule native module is not available');
}

class LinphoneCall {
  private eventEmitter: NativeEventEmitter;
  private callStateListeners: CallStateCallback[] = [];
  private registrationStateListeners: RegistrationStateCallback[] = [];
  private incomingCallListeners: IncomingCallCallback[] = [];

  constructor() {
    this.eventEmitter = new NativeEventEmitter(LinphoneCallModule);
    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.eventEmitter.addListener('callStateChanged', (data: { state: string; message?: string }) => {
      const event: CallEvent = {
        state: data.state as CallState,
        message: data.message,
      };
      this.callStateListeners.forEach(listener => listener(event));
    });

    this.eventEmitter.addListener('registrationStateChanged', (data: { state: string; message?: string }) => {
      const event: RegistrationEvent = {
        state: data.state as RegistrationState,
        message: data.message,
      };
      this.registrationStateListeners.forEach(listener => listener(event));
    });

    this.eventEmitter.addListener('incomingCall', (data: { from: string }) => {
      const event: IncomingCallEvent = {
        from: data.from,
      };
      this.incomingCallListeners.forEach(listener => listener(event));
    });
  }

  // MARK: - Core Methods

  async initialize(): Promise<void> {
    try {
      await LinphoneCallModule.initialize();
    } catch (error) {
      throw new Error(`Failed to initialize Linphone: ${error}`);
    }
  }

  async register(config: SipConfig): Promise<void> {
    try {
      await LinphoneCallModule.register(config.username, config.password, config.domain);
    } catch (error) {
      throw new Error(`Failed to register: ${error}`);
    }
  }

  async startCall(to: string): Promise<void> {
    try {
      await LinphoneCallModule.startCall(to);
    } catch (error) {
      throw new Error(`Failed to start call: ${error}`);
    }
  }

  async acceptCall(): Promise<void> {
    try {
      await LinphoneCallModule.acceptCall();
    } catch (error) {
      throw new Error(`Failed to accept call: ${error}`);
    }
  }

  async declineCall(): Promise<void> {
    try {
      await LinphoneCallModule.declineCall();
    } catch (error) {
      throw new Error(`Failed to decline call: ${error}`);
    }
  }

  async hangUp(): Promise<void> {
    try {
      await LinphoneCallModule.hangUp();
    } catch (error) {
      throw new Error(`Failed to hang up: ${error}`);
    }
  }

  async toggleMute(): Promise<void> {
    try {
      await LinphoneCallModule.toggleMute();
    } catch (error) {
      throw new Error(`Failed to toggle mute: ${error}`);
    }
  }

  async toggleSpeaker(): Promise<void> {
    try {
      await LinphoneCallModule.toggleSpeaker();
    } catch (error) {
      throw new Error(`Failed to toggle speaker: ${error}`);
    }
  }

  // MARK: - Event Listeners

  addCallStateListener(listener: CallStateCallback): () => void {
    this.callStateListeners.push(listener);
    return () => {
      const index = this.callStateListeners.indexOf(listener);
      if (index > -1) {
        this.callStateListeners.splice(index, 1);
      }
    };
  }

  addRegistrationStateListener(listener: RegistrationStateCallback): () => void {
    this.registrationStateListeners.push(listener);
    return () => {
      const index = this.registrationStateListeners.indexOf(listener);
      if (index > -1) {
        this.registrationStateListeners.splice(index, 1);
      }
    };
  }

  addIncomingCallListener(listener: IncomingCallCallback): () => void {
    this.incomingCallListeners.push(listener);
    return () => {
      const index = this.incomingCallListeners.indexOf(listener);
      if (index > -1) {
        this.incomingCallListeners.splice(index, 1);
      }
    };
  }

  removeAllListeners(): void {
    this.callStateListeners = [];
    this.registrationStateListeners = [];
    this.incomingCallListeners = [];
    this.eventEmitter.removeAllListeners('callStateChanged');
    this.eventEmitter.removeAllListeners('registrationStateChanged');
    this.eventEmitter.removeAllListeners('incomingCall');
  }
}

// Export singleton instance
export const linphoneCall = new LinphoneCall();

// Export types and enums
export * from './types';

// Export default
export default linphoneCall;
