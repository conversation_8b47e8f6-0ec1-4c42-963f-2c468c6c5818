{"compilerOptions": {"target": "es2018", "lib": ["es2018"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "./lib", "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "lib", "**/*.test.ts", "**/*.test.tsx"]}