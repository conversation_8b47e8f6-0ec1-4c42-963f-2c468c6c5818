{"name": "react-native-linphone", "version": "1.0.0", "description": "React Native Linphone SDK wrapper for VoIP calls", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "tsc", "prepare": "npm run build", "test": "jest"}, "keywords": ["react-native", "linphone", "voip", "sip", "call"], "author": "Your Name", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}, "devDependencies": {"@types/react": "^18.2.6", "@types/react-native": "^0.72.0", "react": "18.2.0", "react-native": "0.74.7", "typescript": "^5.0.4"}, "files": ["lib/", "ios/", "android/", "react-native-linphone.podspec"]}