import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import linphoneCall, { CallState, RegistrationState, SipConfig } from 'react-native-linphone';

const CallScreen: React.FC = () => {
  const [sipConfig, setSipConfig] = useState<SipConfig>({
    username: '',
    password: '',
    domain: '',
  });
  
  const [callTo, setCallTo] = useState('');
  const [isRegistered, setIsRegistered] = useState(false);
  const [callState, setCallState] = useState<CallState>(CallState.IDLE);
  const [incomingCallFrom, setIncomingCallFrom] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeLinphone();
    setupEventListeners();
    
    return () => {
      linphoneCall.removeAllListeners();
    };
  }, []);

  const initializeLinphone = async () => {
    try {
      await linphoneCall.initialize();
      setIsInitialized(true);
      Alert.alert('Success', 'Linphone initialized successfully');
    } catch (error) {
      Alert.alert('Error', `Failed to initialize Linphone: ${error}`);
    }
  };

  const setupEventListeners = () => {
    linphoneCall.addCallStateListener((event) => {
      setCallState(event.state);
      console.log('Call state changed:', event.state, event.message);
      
      if (event.state === CallState.ERROR) {
        Alert.alert('Call Error', event.message || 'Unknown error');
      }
    });

    linphoneCall.addRegistrationStateListener((event) => {
      const registered = event.state === RegistrationState.REGISTERED;
      setIsRegistered(registered);
      console.log('Registration state changed:', event.state, event.message);
      
      if (registered) {
        Alert.alert('Success', 'Successfully registered to SIP server');
      } else if (event.message) {
        Alert.alert('Registration Error', event.message);
      }
    });

    linphoneCall.addIncomingCallListener((event) => {
      setIncomingCallFrom(event.from);
      Alert.alert(
        'Incoming Call',
        `Call from: ${event.from}`,
        [
          { text: 'Decline', onPress: handleDeclineCall },
          { text: 'Accept', onPress: handleAcceptCall },
        ]
      );
    });
  };

  const handleRegister = async () => {
    if (!sipConfig.username || !sipConfig.password || !sipConfig.domain) {
      Alert.alert('Error', 'Please fill in all SIP configuration fields');
      return;
    }

    try {
      await linphoneCall.register(sipConfig);
    } catch (error) {
      Alert.alert('Error', `Registration failed: ${error}`);
    }
  };

  const handleStartCall = async () => {
    if (!callTo) {
      Alert.alert('Error', 'Please enter a number to call');
      return;
    }

    if (!isRegistered) {
      Alert.alert('Error', 'Please register first');
      return;
    }

    try {
      await linphoneCall.startCall(callTo);
    } catch (error) {
      Alert.alert('Error', `Failed to start call: ${error}`);
    }
  };

  const handleAcceptCall = async () => {
    try {
      await linphoneCall.acceptCall();
      setIncomingCallFrom(null);
    } catch (error) {
      Alert.alert('Error', `Failed to accept call: ${error}`);
    }
  };

  const handleDeclineCall = async () => {
    try {
      await linphoneCall.declineCall();
      setIncomingCallFrom(null);
    } catch (error) {
      Alert.alert('Error', `Failed to decline call: ${error}`);
    }
  };

  const handleHangUp = async () => {
    try {
      await linphoneCall.hangUp();
    } catch (error) {
      Alert.alert('Error', `Failed to hang up: ${error}`);
    }
  };

  const handleToggleMute = async () => {
    try {
      await linphoneCall.toggleMute();
    } catch (error) {
      Alert.alert('Error', `Failed to toggle mute: ${error}`);
    }
  };

  const handleToggleSpeaker = async () => {
    try {
      await linphoneCall.toggleSpeaker();
    } catch (error) {
      Alert.alert('Error', `Failed to toggle speaker: ${error}`);
    }
  };

  const getCallStateText = () => {
    switch (callState) {
      case CallState.IDLE: return 'Idle';
      case CallState.OUTGOING_INIT: return 'Initiating call...';
      case CallState.OUTGOING_PROGRESS: return 'Calling...';
      case CallState.OUTGOING_RINGING: return 'Ringing...';
      case CallState.CONNECTED: return 'Connected';
      case CallState.STREAMS_RUNNING: return 'In call';
      case CallState.INCOMING_RECEIVED: return 'Incoming call';
      case CallState.END: return 'Call ended';
      case CallState.ERROR: return 'Call error';
      default: return callState;
    }
  };

  const isInCall = [
    CallState.CONNECTED,
    CallState.STREAMS_RUNNING,
    CallState.OUTGOING_PROGRESS,
    CallState.OUTGOING_RINGING,
    CallState.INCOMING_RECEIVED,
  ].includes(callState);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <Text style={styles.title}>Linphone VoIP Call</Text>
      
      {/* Status */}
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Status: {isInitialized ? 'Initialized' : 'Not initialized'}
        </Text>
        <Text style={styles.statusText}>
          Registration: {isRegistered ? 'Registered' : 'Not registered'}
        </Text>
        <Text style={styles.statusText}>
          Call State: {getCallStateText()}
        </Text>
      </View>

      {/* SIP Configuration */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>SIP Configuration</Text>
        <TextInput
          style={styles.input}
          placeholder="Username"
          value={sipConfig.username}
          onChangeText={(text) => setSipConfig({...sipConfig, username: text})}
        />
        <TextInput
          style={styles.input}
          placeholder="Password"
          value={sipConfig.password}
          onChangeText={(text) => setSipConfig({...sipConfig, password: text})}
          secureTextEntry
        />
        <TextInput
          style={styles.input}
          placeholder="Domain (e.g., sip.example.com)"
          value={sipConfig.domain}
          onChangeText={(text) => setSipConfig({...sipConfig, domain: text})}
        />
        <TouchableOpacity
          style={[styles.button, !isInitialized && styles.buttonDisabled]}
          onPress={handleRegister}
          disabled={!isInitialized}
        >
          <Text style={styles.buttonText}>Register</Text>
        </TouchableOpacity>
      </View>

      {/* Call Controls */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Call Controls</Text>
        <TextInput
          style={styles.input}
          placeholder="Number to call (e.g., sip:<EMAIL>)"
          value={callTo}
          onChangeText={setCallTo}
        />
        
        {!isInCall ? (
          <TouchableOpacity
            style={[styles.button, styles.callButton, !isRegistered && styles.buttonDisabled]}
            onPress={handleStartCall}
            disabled={!isRegistered}
          >
            <Text style={styles.buttonText}>Start Call</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.callControlsContainer}>
            <TouchableOpacity style={[styles.button, styles.hangUpButton]} onPress={handleHangUp}>
              <Text style={styles.buttonText}>Hang Up</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={handleToggleMute}>
              <Text style={styles.buttonText}>Toggle Mute</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={handleToggleSpeaker}>
              <Text style={styles.buttonText}>Toggle Speaker</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Incoming Call */}
      {incomingCallFrom && (
        <View style={styles.incomingCallContainer}>
          <Text style={styles.incomingCallText}>Incoming call from:</Text>
          <Text style={styles.incomingCallFrom}>{incomingCallFrom}</Text>
          <View style={styles.incomingCallButtons}>
            <TouchableOpacity style={[styles.button, styles.acceptButton]} onPress={handleAcceptCall}>
              <Text style={styles.buttonText}>Accept</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, styles.declineButton]} onPress={handleDeclineCall}>
              <Text style={styles.buttonText}>Decline</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  statusContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  statusText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    fontSize: 16,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  callButton: {
    backgroundColor: '#34C759',
  },
  hangUpButton: {
    backgroundColor: '#FF3B30',
  },
  acceptButton: {
    backgroundColor: '#34C759',
    flex: 1,
    marginRight: 10,
  },
  declineButton: {
    backgroundColor: '#FF3B30',
    flex: 1,
    marginLeft: 10,
  },
  callControlsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  incomingCallContainer: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  incomingCallText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  incomingCallFrom: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#007AFF',
  },
  incomingCallButtons: {
    flexDirection: 'row',
  },
});

export default CallScreen;
