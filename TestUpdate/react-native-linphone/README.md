# React Native Linphone SDK 5.4.3

A comprehensive VoIP calling solution for React Native using Linphone SDK 5.4.3, supporting both iOS and Android platforms.

## 🚀 Features

- ✅ **Cross-platform**: iOS and Android support
- ✅ **SIP Registration**: Connect to any SIP server
- ✅ **Voice Calls**: Outgoing and incoming call support
- ✅ **Call Controls**: Mute, speaker, hold, transfer
- ✅ **Real-time Events**: Call state changes, registration status
- ✅ **Modern Architecture**: TypeScript support, Promise-based API
- ✅ **Production Ready**: Based on stable Linphone SDK 5.4.3

## 📦 Project Structure

```
react-native-linphone/
├── ios-linphone-wrapper/       # Swift Package using LinphoneSwift 5.4.3
├── android-linphone-wrapper/   # Android Module using Linphone SDK 5.4.3
├── react-native-linphone/      # React Native bridge library
└── example-app/                # Example React Native app
```

## 🛠 Installation

### Prerequisites

- React Native 0.70+
- iOS 13.0+
- Android API 23+
- Xcode 14+ (for iOS)
- Android Studio (for Android)

### Install the library

```bash
npm install react-native-linphone
# or
yarn add react-native-linphone
```

### iOS Setup

1. Add to your `ios/Podfile`:

```ruby
pod 'react-native-linphone', :path => '../node_modules/react-native-linphone'
```

2. Install pods:

```bash
cd ios && pod install
```

3. Add permissions to `Info.plist`:

```xml
<key>NSMicrophoneUsageDescription</key>
<string>This app needs microphone access for voice calls</string>
<key>NSCameraUsageDescription</key>
<string>This app needs camera access for video calls</string>
```

### Android Setup

1. Add to `android/settings.gradle`:

```gradle
include ':react-native-linphone'
project(':react-native-linphone').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-linphone/android')
```

2. Add to `android/app/build.gradle`:

```gradle
dependencies {
    implementation project(':react-native-linphone')
}
```

3. Add permissions to `AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## 📱 Usage

### Basic Example

```typescript
import linphoneCall, { CallState, RegistrationState } from 'react-native-linphone';

// Initialize Linphone
await linphoneCall.initialize();

// Register to SIP server
await linphoneCall.register({
  username: 'your-username',
  password: 'your-password',
  domain: 'sip.example.com'
});

// Start a call
await linphoneCall.startCall('sip:<EMAIL>');

// Listen to events
linphoneCall.addCallStateListener((event) => {
  console.log('Call state:', event.state);
});

linphoneCall.addRegistrationStateListener((event) => {
  console.log('Registration:', event.state);
});

linphoneCall.addIncomingCallListener((event) => {
  console.log('Incoming call from:', event.from);
});
```

### Advanced Usage

```typescript
// Accept incoming call
await linphoneCall.acceptCall();

// Decline incoming call
await linphoneCall.declineCall();

// Hang up current call
await linphoneCall.hangUp();

// Toggle mute
await linphoneCall.toggleMute();

// Toggle speaker
await linphoneCall.toggleSpeaker();

// Remove all listeners
linphoneCall.removeAllListeners();
```

## 🎯 API Reference

### Methods

| Method | Parameters | Returns | Description |
|--------|------------|---------|-------------|
| `initialize()` | - | `Promise<void>` | Initialize Linphone core |
| `register(config)` | `SipConfig` | `Promise<void>` | Register to SIP server |
| `startCall(to)` | `string` | `Promise<void>` | Start outgoing call |
| `acceptCall()` | - | `Promise<void>` | Accept incoming call |
| `declineCall()` | - | `Promise<void>` | Decline incoming call |
| `hangUp()` | - | `Promise<void>` | End current call |
| `toggleMute()` | - | `Promise<void>` | Toggle microphone mute |
| `toggleSpeaker()` | - | `Promise<void>` | Toggle speaker mode |

### Event Listeners

| Listener | Event Type | Description |
|----------|------------|-------------|
| `addCallStateListener` | `CallEvent` | Call state changes |
| `addRegistrationStateListener` | `RegistrationEvent` | Registration status |
| `addIncomingCallListener` | `IncomingCallEvent` | Incoming call received |

### Types

```typescript
interface SipConfig {
  username: string;
  password: string;
  domain: string;
}

enum CallState {
  IDLE = 'idle',
  OUTGOING_INIT = 'outgoing_init',
  OUTGOING_PROGRESS = 'outgoing_progress',
  OUTGOING_RINGING = 'outgoing_ringing',
  CONNECTED = 'connected',
  STREAMS_RUNNING = 'streams_running',
  END = 'end',
  ERROR = 'error',
  // ... more states
}

enum RegistrationState {
  REGISTERED = 'registered',
  UNREGISTERED = 'unregistered',
}
```

## 🧪 Example App

Run the example app to see the library in action:

```bash
cd example-app/LinphoneExample
npm install
npx react-native run-ios
# or
npx react-native run-android
```

## 🔧 Development

### Building the library

```bash
cd react-native-linphone
npm run build
```

### Testing

```bash
npm test
```

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/yourname/react-native-linphone/issues)
- 📖 Documentation: [Wiki](https://github.com/yourname/react-native-linphone/wiki)

## 🙏 Acknowledgments

- [Linphone SDK](https://linphone.org/) - The core VoIP library
- [React Native Community](https://reactnative.dev/) - For the amazing framework
