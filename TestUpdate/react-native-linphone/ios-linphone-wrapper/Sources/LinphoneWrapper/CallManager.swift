import Foundation
import linphonesw
import AVFoundation

public enum CallState: String, CaseIterable {
    case idle = "idle"
    case outgoingInit = "outgoing_init"
    case outgoingProgress = "outgoing_progress"
    case outgoingRinging = "outgoing_ringing"
    case outgoingEarlyMedia = "outgoing_early_media"
    case connected = "connected"
    case streamsRunning = "streams_running"
    case pausing = "pausing"
    case paused = "paused"
    case resuming = "resuming"
    case referred = "referred"
    case error = "error"
    case end = "end"
    case pausedByRemote = "paused_by_remote"
    case updatedByRemote = "updated_by_remote"
    case incomingReceived = "incoming_received"
    case incomingEarlyMedia = "incoming_early_media"
    case updating = "updating"
    case released = "released"
    case earlyUpdatedByRemote = "early_updated_by_remote"
    case earlyUpdating = "early_updating"
}

public protocol CallManagerDelegate: AnyObject {
    func onCallStateChanged(state: CallState, message: String?)
    func onRegistrationStateChanged(isRegistered: Bool, message: String?)
    func onIncomingCall(from: String)
}

public class CallManager: NSObject {
    public weak var delegate: CallManagerDelegate?
    
    private var core: Core?
    private var currentCall: Call?
    private var isInitialized = false
    
    public override init() {
        super.init()
        setupAudioSession()
    }
    
    deinit {
        core?.stop()
    }
    
    // MARK: - Public Methods
    
    public func initialize() throws {
        guard !isInitialized else { return }
        
        LoggingService.Instance.logLevel = LogLevel.Debug
        
        do {
            core = try Factory.Instance.createCore(configPath: "", factoryConfigPath: "", systemContext: nil)
            core?.delegate = self
            
            // Configure transports
            let transports = core?.transports
            transports?.udpPort = 5060
            transports?.tcpPort = 5060
            transports?.tlsPort = 5061
            
            // Start core
            try core?.start()
            isInitialized = true
            
        } catch {
            throw error
        }
    }
    
    public func register(username: String, password: String, domain: String) throws {
        guard let core = core else {
            throw NSError(domain: "CallManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Core not initialized"])
        }
        
        // Create auth info
        let authInfo = try Factory.Instance.createAuthInfo(username: username, userid: "", passwd: password, ha1: "", realm: "", domain: domain)
        core.addAuthInfo(authInfo: authInfo)
        
        // Create proxy config
        let proxyConfig = try core.createProxyConfig()
        let identity = "sip:\(username)@\(domain)"
        let address = try Factory.Instance.createAddress(addr: identity)
        
        try proxyConfig.setIdentityAddress(newValue: address)
        
        let serverAddr = "sip:\(domain)"
        let serverAddress = try Factory.Instance.createAddress(addr: serverAddr)
        try proxyConfig.setServerAddress(newValue: serverAddress)
        
        proxyConfig.registerEnabled = true
        core.addProxyConfig(config: proxyConfig)
        core.defaultProxyConfig = proxyConfig
    }
    
    public func startCall(to: String) throws {
        guard let core = core else {
            throw NSError(domain: "CallManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Core not initialized"])
        }
        
        let remoteAddress = try Factory.Instance.createAddress(addr: to)
        let params = try core.createCallParams(call: nil)
        
        currentCall = core.inviteAddressWithParams(addr: remoteAddress, params: params)
    }
    
    public func acceptCall() {
        guard let call = currentCall else { return }
        
        do {
            try call.accept()
        } catch {
            delegate?.onCallStateChanged(state: .error, message: "Failed to accept call: \(error.localizedDescription)")
        }
    }
    
    public func declineCall() {
        guard let call = currentCall else { return }
        
        do {
            try call.decline(reason: Reason.Declined)
        } catch {
            delegate?.onCallStateChanged(state: .error, message: "Failed to decline call: \(error.localizedDescription)")
        }
    }
    
    public func hangUp() {
        guard let call = currentCall else { return }
        
        do {
            try call.terminate()
        } catch {
            delegate?.onCallStateChanged(state: .error, message: "Failed to hang up: \(error.localizedDescription)")
        }
    }
    
    public func toggleMute() {
        guard let call = currentCall else { return }
        call.microphoneMuted = !call.microphoneMuted
    }
    
    public func toggleSpeaker() {
        guard let core = core else { return }
        core.outputAudioDevice = core.outputAudioDevice?.type == AudioDeviceType.Speaker ? 
            core.defaultInputAudioDevice : core.speakerAudioDevice
    }
    
    // MARK: - Private Methods
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .voiceChat, options: [.allowBluetooth, .allowBluetoothA2DP])
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    private func callStateToEnum(_ state: Call.State) -> CallState {
        switch state {
        case .Idle: return .idle
        case .OutgoingInit: return .outgoingInit
        case .OutgoingProgress: return .outgoingProgress
        case .OutgoingRinging: return .outgoingRinging
        case .OutgoingEarlyMedia: return .outgoingEarlyMedia
        case .Connected: return .connected
        case .StreamsRunning: return .streamsRunning
        case .Pausing: return .pausing
        case .Paused: return .paused
        case .Resuming: return .resuming
        case .Referred: return .referred
        case .Error: return .error
        case .End: return .end
        case .PausedByRemote: return .pausedByRemote
        case .UpdatedByRemote: return .updatedByRemote
        case .IncomingReceived: return .incomingReceived
        case .IncomingEarlyMedia: return .incomingEarlyMedia
        case .Updating: return .updating
        case .Released: return .released
        case .EarlyUpdatedByRemote: return .earlyUpdatedByRemote
        case .EarlyUpdating: return .earlyUpdating
        }
    }
}

// MARK: - CoreDelegate

extension CallManager: CoreDelegate {
    public func onCallStateChanged(core: Core, call: Call, state: Call.State, message: String) {
        currentCall = call
        let callState = callStateToEnum(state)
        delegate?.onCallStateChanged(state: callState, message: message)
        
        if state == .IncomingReceived {
            let remoteAddress = call.remoteAddress?.asStringUriOnly() ?? "Unknown"
            delegate?.onIncomingCall(from: remoteAddress)
        }
    }
    
    public func onRegistrationStateChanged(core: Core, cfg: ProxyConfig, state: RegistrationState, message: String) {
        let isRegistered = state == .Ok
        delegate?.onRegistrationStateChanged(isRegistered: isRegistered, message: message)
    }
}
