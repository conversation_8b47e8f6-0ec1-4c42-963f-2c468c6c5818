import Foundation

public class LinphoneCallModule: NSObject {
    public static let shared = LinphoneCallModule()
    
    private let callManager = CallManager()
    private var eventCallbacks: [String: (String, String?) -> Void] = [:]
    
    public override init() {
        super.init()
        callManager.delegate = self
    }
    
    // MARK: - Public API for React Native
    
    public func initialize() throws {
        try callManager.initialize()
    }
    
    public func register(username: String, password: String, domain: String) throws {
        try callManager.register(username: username, password: password, domain: domain)
    }
    
    public func startCall(to: String) throws {
        try callManager.startCall(to: to)
    }
    
    public func acceptCall() {
        callManager.acceptCall()
    }
    
    public func declineCall() {
        callManager.declineCall()
    }
    
    public func hangUp() {
        callManager.hangUp()
    }
    
    public func toggleMute() {
        callManager.toggleMute()
    }
    
    public func toggleSpeaker() {
        callManager.toggleSpeaker()
    }
    
    // MARK: - Event Handling
    
    public func addEventCallback(eventType: String, callback: @escaping (String, String?) -> Void) {
        eventCallbacks[eventType] = callback
    }
    
    public func removeEventCallback(eventType: String) {
        eventCallbacks.removeValue(forKey: eventType)
    }
    
    private func emitEvent(type: String, data: String, message: String? = nil) {
        eventCallbacks[type]?(data, message)
    }
}

// MARK: - CallManagerDelegate

extension LinphoneCallModule: CallManagerDelegate {
    public func onCallStateChanged(state: CallState, message: String?) {
        emitEvent(type: "callStateChanged", data: state.rawValue, message: message)
    }
    
    public func onRegistrationStateChanged(isRegistered: Bool, message: String?) {
        emitEvent(type: "registrationStateChanged", data: isRegistered ? "registered" : "unregistered", message: message)
    }
    
    public func onIncomingCall(from: String) {
        emitEvent(type: "incomingCall", data: from)
    }
}
