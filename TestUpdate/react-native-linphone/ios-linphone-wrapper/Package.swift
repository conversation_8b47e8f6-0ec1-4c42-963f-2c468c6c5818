// swift-tools-version: 5.7
import PackageDescription

let package = Package(
    name: "LinphoneWrapper",
    platforms: [
        .iOS(.v13)
    ],
    products: [
        .library(
            name: "LinphoneWrapper",
            targets: ["LinphoneWrapper"]
        ),
    ],
    dependencies: [
        .package(url: "https://gitlab.linphone.org/BC/public/linphone-sdk.git", exact: "5.4.3")
    ],
    targets: [
        .target(
            name: "LinphoneWrapper",
            dependencies: [
                .product(name: "linphonesw", package: "linphone-sdk")
            ],
            path: "Sources"
        ),
    ]
)
